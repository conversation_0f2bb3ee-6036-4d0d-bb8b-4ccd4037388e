<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonMenu_custom extends JwpagefactoryAddons
{

    public function render()
    {

        $settings = $this->addon->settings;
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();

        if(count($section_tab_item) > 0) {
            foreach ($section_tab_item as $key => $item) {
                //var_dump($item);
                $item->index = $key;
            }
        }
        //print_r($section_tab_item);

        $addon_id = $this->addon->id;

        //是否开启轮播
        $is_swiper = (isset($settings->is_swiper) && $settings->is_swiper) ? $settings->is_swiper : 0;
        //是否开启切换
        $is_swiper_button = (isset($settings->is_swiper_button) && $settings->is_swiper_button) ? $settings->is_swiper_button : 0;
        //是否开启轮播点
        $is_swiper_pagination = (isset($settings->is_swiper_pagination) && $settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 0;
        //是否显示名称
        $is_show_name = (isset($settings->is_show_name) && $settings->is_show_name) ? $settings->is_show_name : 0;
        //是否显示名称下划线
        $is_show_name_line = (isset($settings->is_show_name_line) && $settings->is_show_name_line) ? $settings->is_show_name_line : 0;
        //是否显示简介
        $is_show_desc = (isset($settings->is_show_desc) && $settings->is_show_desc) ? $settings->is_show_desc : 0;
        //是否显示更多按钮
        $is_show_more = (isset($settings->is_show_more) && $settings->is_show_more) ? $settings->is_show_more : 0;
        // 更多按钮
        $more_icon = (isset($settings->more_icon) && $settings->more_icon) ? $settings->more_icon : 'https://oss.lcweb01.cn/joomla/20210817/cd03035e0bdb7530651387167c7645ed.png';

        // 选项卡相关
        $is_tab = (isset($settings->is_tab) && $settings->is_tab) ? $settings->is_tab : 0;
        // 选项卡列表
        $change_tab_item = (isset($settings->change_tab_item) && $settings->change_tab_item) ? $settings->change_tab_item : array();
        // 选项卡布局
        $tab_theme = (isset($settings->tab_theme) && $settings->tab_theme) ? $settings->tab_theme : 'type01';

        // 是否显示内容
        $is_show_content = (isset($settings->is_show_content) && $settings->is_show_content) ? $settings->is_show_content : 0;

        // 默认选中第几个
        $section_tab_active = (isset($settings->section_tab_active) && $settings->section_tab_active) ? $settings->section_tab_active : 1;

        $output = '';

        if($is_tab == 1 && count($change_tab_item) > 0)
        {
            $output .= '<div class="menu-tab-box">';
            foreach ($change_tab_item as $key => $tab) {
                $active = '';
                // if($key == 0) {$section_tab_active
                if($key == ($section_tab_active - 1)) {
                    $active = 'actives';
                }
                $output .= '<div class="menu-tab-item ' . $active . '" data-menu="menu-' . $addon_id . '-' . $key . '"><span>' . $tab->title . '</span></div>';
            }
            $output .= '</div>';
            foreach ($change_tab_item as $tab_key => $tab_item) {
                $display = 'none';
                // if($tab_key == 0) {
                if($tab_key == ($section_tab_active - 1)) {
                    $display = 'block';
                }
                $output .= '<div class="section-5-box" id="menu-' . $addon_id . '-' . $tab_key . '" style="display: ' . $display . ';">';
                if(is_array($section_tab_item) && $tab_item->start_item_num && $tab_item->end_item_num){
                    $new_arr = array_slice($section_tab_item, $tab_item->start_item_num - 1, $tab_item->end_item_num - $tab_item->start_item_num + 1);
                    $output .= $this->itemSection($new_arr);
                    if($is_show_content == 1) {
                        $output .= $this->contentSection($new_arr);
                    }
                }
                else
                {
                    $output .= $this->itemSection($section_tab_item);
                    if($is_show_content == 1) {
                        $output .= $this->contentSection($section_tab_item);
                    }
                }
                $output .= '</div>';
            }
        }
        else
        {
            $output .= $this->itemSection($section_tab_item);
            if($is_show_content == 1) {
                $output .= $this->contentSection($section_tab_item);
            }
        }

        return $output;
    }

    public function itemSection($items)
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;

        //是否开启轮播
        $is_swiper = (isset($settings->is_swiper) && $settings->is_swiper) ? $settings->is_swiper : 0;
        //是否开启切换
        $is_swiper_button = (isset($settings->is_swiper_button) && $settings->is_swiper_button) ? $settings->is_swiper_button : 0;
        //是否开启轮播点
        $is_swiper_pagination = (isset($settings->is_swiper_pagination) && $settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 0;
        //是否显示名称
        $is_show_name = (isset($settings->is_show_name) && $settings->is_show_name) ? $settings->is_show_name : 0;
        //是否显示副标题
        $is_show_s_name = (isset($settings->is_show_s_name) && $settings->is_show_s_name) ? $settings->is_show_s_name : 0;
        //是否显示名称下划线
        $is_show_name_line = (isset($settings->is_show_name_line) && $settings->is_show_name_line) ? $settings->is_show_name_line : 0;
        //是否显示简介
        $is_show_desc = (isset($settings->is_show_desc) && $settings->is_show_desc) ? $settings->is_show_desc : 0;
        //是否显示更多按钮
        $is_show_more = (isset($settings->is_show_more) && $settings->is_show_more) ? $settings->is_show_more : 0;
        // 更多按钮
        $more_icon = (isset($settings->more_icon) && $settings->more_icon) ? $settings->more_icon : 'https://oss.lcweb01.cn/joomla/20210817/cd03035e0bdb7530651387167c7645ed.png';

        // 下划线位置
        $line_border_position = isset($settings->line_border_position) && $settings->line_border_position ? $settings->line_border_position : 'type01';
        // 名称位置
        $content_icon_position = isset($settings->content_icon_position) && $settings->content_icon_position ? $settings->content_icon_position : 'type01';

        // 边框动画
        $animate_border_line = isset($settings->animate_border_line) && $settings->animate_border_line ? $settings->animate_border_line : 'none';

        // 入场动画
        $animate_translate_ruchang = isset($settings->animate_translate_ruchang) && $settings->animate_translate_ruchang ? $settings->animate_translate_ruchang : '0';
        $animate_ruch_delay = isset($settings->animate_ruch_delay) && $settings->animate_ruch_delay ? $settings->animate_ruch_delay : '0.2';
        // 默认选中第几个
        $section_tab_active = (isset($settings->section_tab_active) && $settings->section_tab_active) ? $settings->section_tab_active : 1;

        $output = '<div class="section-5';
        if($is_swiper == 1){
            $output .= ' swiper-container" data-swiper="swiper-container-' . $this->addon->id .'" id="swiper-container-' . $this->addon->id;
        }
        $output .= '">';
        $output .= '  <ul class="s-ul';
        if($is_swiper == 1){
            $output .= ' swiper-wrapper';
        }
        $output .= '">';
        foreach ($items as $key => $tab) {
            $dhmc='';
            $dhsj='';
            if($animate_translate_ruchang==1){
                $dhmc=' jwpf-wow fadeInDownBig jwpf-animated ';
                $delay=0.3 + $animate_ruch_delay*$tab->index;
                $delays=500 + 200*$tab->index;

                $dhsj='data-jwpf-wow-duration="'.$delays.'ms" data-jwpf-wow-delay="' . $delay . 's"';
            }

            $actives = '';
            if($settings->is_item_active == 1 && $key == ($section_tab_active - 1)) {
                $actives = 'actives';
            }
            $output .= '<li class="s-li  ' . $actives .$dhmc;
            if($is_swiper == 1){
                $output .= ' swiper-slide';
            }
            $output .= '" data-index="' . $tab->index . '" '.$dhsj.' >';
            $link = '';
            if ($tab->detail_page_type == 'external_links') {
              if ($tab->detail_url) {
                // 判断是不是http或者https链接
                if (filter_var($tab->detail_url, FILTER_VALIDATE_URL) !== false) {
                  $link = $tab->detail_url;
                } else {
                  $link = 'http://' . $tab->detail_url;
                }
              }
            } else {
              if($tab->detail_page_id){
                  $id = base64_encode($tab->detail_page_id);
                  $link .= 'component/jwpagefactory?view=page&id=' . $id . '&company_id=' . $company_id . '&site_id=' . $site_id . '&layout_id=' . $layout_id;
              }
            }
            $link_target = $tab->link_target ?: '_blank';
            $output .= '<a ';
            if($link != ''){
                $output .= 'target="' . $link_target . '" href="' . $link .'" ';
            }
            $output .= 'class="item ' . $actives . '">';

            if($tab->icon && $content_icon_position == 'type01'){
                $output .= '<div class="content-icon">
                            <i class="icon-box jwpf-animated "></i>
                        </div>';
            }
            if($is_show_name == 1 || $is_show_desc == 1){
                $output .= '<div class="info">';
                if($is_show_name_line == 1 && $line_border_position == 'type02'){
                    $output .= '<div class="line-box">
                             <span class="line"></span>
                        </div>';
                }
                if($is_show_name == 1){
                    if($tab->icon && $content_icon_position == 'type03'){
                        $output .= '<div class="flex">
                        <div class="content-icon" style="width: auto;">
                            <i class="icon-box jwpf-animated "></i>
                        </div>';
                    }
                    $output .= '<p class="name"' . ($content_icon_position == 'type03' ? ' style="width: auto;"' : '') . '><b>'.$tab->botname.'</b>' . $tab->name . '</p>';
                    if($tab->icon && $content_icon_position == 'type03'){
                        $output .= '</div>';
                    }
                }
                if($is_show_s_name == 1){
                    $output .= '<p class="s-name">' . $tab->s_name . '</p>';
                }
                if($tab->icon && $content_icon_position == 'type02'){
                    $output .= '<div class="content-icon">
                            <i class="icon-box jwpf-animated "></i>
                        </div>';
                }
                if($is_show_name_line == 1 && $line_border_position == 'type01'){
                    $output .= '<div class="line-box">
                             <span class="line"></span>
                        </div>';
                }
                if($is_show_desc == 1){
                    $output .= '<p class="desc">' . $tab->desc . '</p>';
                }
                if($is_show_more == 1) {
                    $output .= '<div class="more-box">
                             <img src="' . $more_icon . '" alt="" class="more-icon"/>
                        </div>';
                }
                $output .= '</div>';
            }

            $output .= '</a>';
            if($animate_border_line != 'none') {
                $output .= '
                    <div class="border-line border-line-01"></div><!--上-->
                    <div class="border-line border-line-02"></div><!--右-->
                    <div class="border-line border-line-03"></div><!--下-->
                    <div class="border-line border-line-04"></div><!--左-->';
            }
            $output .= '</li>';
        }
        $output .= '  </ul>';
        $output .= '</div>';
        if($is_swiper == 1 && $is_swiper_button == 1){
            $output .= '<div class="swiper-button swiper-button-prev"></div><!--左箭头。如果放置在swiper-container外面，需要自定义样式。-->
            <div class="swiper-button swiper-button-next"></div><!--右箭头。如果放置在swiper-container外面，需要自定义样式。-->';
        }
        if($is_swiper == 1 && $is_swiper_pagination == 1){
            $output .= '<div class="swiper-pagination"></div><!--分页器。如果放置在swiper-container外面，需要自定义样式。-->';
        }

        return $output;
    }

    public function contentSection($items)
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;

        $settings = $this->addon->settings;
        // 默认选中第几个
        $section_tab_active = (isset($settings->section_tab_active) && $settings->section_tab_active) ? $settings->section_tab_active : 1;

        $output = '';

        foreach ($items as $key => $tab) {
            $show = '';
            if($key != ($section_tab_active - 1)) {
                $show = 'hidden';
            }
            $output .= '
                <div class="section-content-box ' . $show . '">
                    <div class="section-content">
                        <div class="text-wrap">
                            <div class="text-title">' . $tab->content_title . '</div>
                            <div class="text-desc">' . $tab->content_desc . '</div>
                        </div>
                        <div class="content-line"></div>
                        <div class="image-wrap">';
                        if($tab->content_img) {
                            $output .= '<img src="' . $tab->content_img . '" class="content-img"/>';
                        }
                        $output .= '</div>
                    </div>
                </div> 
            ';
        }

        return $output;
    }

    public function scripts()
    {
        $js = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $js;
    }

    public  function js() {
        $addonId = '#jwpf-addon-' . $this->addon->id;

        $settings = $this->addon->settings;
        //是否开启轮播
        $is_swiper = (isset($settings->is_swiper) && $settings->is_swiper) ? $settings->is_swiper : 0;
        //是否开启自动切换
        $is_swiper_autoplay = (isset($settings->is_swiper_autoplay) && $settings->is_swiper_autoplay) ? $settings->is_swiper_autoplay : 0;
        //pc端一行显示个数
        $section_tab_num_md = (isset($settings->section_tab_num) && $settings->section_tab_num) ? $settings->section_tab_num : 5;
        //平板端一行显示个数
        $section_tab_num_sm = (isset($settings->section_tab_num_sm) && $settings->section_tab_num_sm) ? $settings->section_tab_num_sm : 5;
        //手机端一行显示个数
        $section_tab_num_xs = (isset($settings->section_tab_num_xs) && $settings->section_tab_num_xs) ? $settings->section_tab_num_xs : 3;
        // 菜单外边距
        $section_tab_m_md = (isset($settings->section_tab_m) && $settings->section_tab_m) ? $settings->section_tab_m : 0;
        $section_tab_m_sm = $settings->section_tab_m_sm ?: 0;
        $section_tab_m_xs = $settings->section_tab_m_xs ?: 0;

        // 是否循环切换
        $is_swiper_loop = isset($settings->is_swiper_loop) && $settings->is_swiper_loop ? $settings->is_swiper_loop : 0;

        // 选项卡相关
        $is_tab = (isset($settings->is_tab) && $settings->is_tab) ? $settings->is_tab : 0;
        // 选项卡列表
        $change_tab_item = (isset($settings->change_tab_item) && $settings->change_tab_item) ? $settings->change_tab_item : array();

        $js = 'jQuery(function($){';
        if($is_swiper == 1) {
            $js .= '
            var swiper1 = new Swiper(\'' . $addonId . ' .swiper-container\',{
                autoHeight: true,
                observer: true,  //开启动态检查器，监测swiper和slide
                observeParents: true,  //监测Swiper 的祖/父元素
                loop: ' . ($is_swiper_loop == 1 ? 'true' : 'false') . ',
                autoplay: ' . ($is_swiper_autoplay == 1 ? 'true' : 'false') . ',
                slidesPerView : ' . $section_tab_num_md . ',
                spaceBetween: ' . $section_tab_m_md . ',
                pagination: {
                    el: \'' . $addonId . ' .swiper-pagination\',
                },
                navigation: {
                  nextEl: \'' . $addonId . ' .swiper-button-next\',
                  prevEl: \'' . $addonId . ' .swiper-button-prev\',
                },
                breakpoints: {
                    0: {  //当屏幕宽度大于等于0
                        slidesPerView: ' . $section_tab_num_xs . ',
                        spaceBetween: ' . $section_tab_m_xs . '
                    },
                    768: {  //当屏幕宽度大于等于768
                        slidesPerView: ' . $section_tab_num_sm . ',
                        spaceBetween: ' . $section_tab_m_sm . '
                    },
                    996: {  //当屏幕宽度大于等于996
                        //slidesPerGroup: ' . $section_tab_num_md . ',
                        slidesPerView: ' . $section_tab_num_md . ',
                        spaceBetween: ' . $section_tab_m_md . '
                    }
                },
                on: {';

            $js .= '},
            });';
        }
        if($settings->is_item_active == 1) {
            $js .= '
            $("' . $addonId . ' .section-5 .s-li").click(function(){
                $(this).addClass("actives").siblings().removeClass("actives");
                $("' . $addonId . ' .section-5 .s-li .item").removeClass("actives");
                $(this).find(".item").addClass("actives");
                ';
                if($settings->is_show_content == 1) {
                    $js .= '
                    var liIndex = $(this).index()
                        console.log(liIndex);
                        $("' . $addonId . ' .section-content-box").eq(liIndex).removeClass("hidden").siblings().addClass("hidden");
                    ';
                }
            $js .= '})
            ';
        }
        if($is_tab == 1 && count($change_tab_item) > 0)
        {
            $js .= '
            $("' . $addonId . ' .menu-tab-box .menu-tab-item").click(function(){
                var menu = $(this).attr("data-menu");
                $(this).addClass("actives").siblings().removeClass("actives");
                $("' . $addonId . ' .section-5-box").hide();
                $("' . $addonId . ' #" + menu).show();
            })
            ';
        }
        $js .= '})';

        return $js;
    }

    public function stylesheets()
    {
        $style_sheet = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/jzt-animate.css',
        );
        return $style_sheet;
    }

    public function css()
    {
        $addonId = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;

        // 切换item
        $section_tab_item = (isset($settings->section_tab_item) && $settings->section_tab_item) ? $settings->section_tab_item : array();

        // 是否开启轮播
        $is_swiper = (isset($settings->is_swiper) && $settings->is_swiper) ? $settings->is_swiper : 0;
        // 是否开启选中效果
        $is_item_active = (isset($settings->is_item_active) && $settings->is_item_active) ? $settings->is_item_active : 0;
        // 轮播框内边距
        $swiper_container_p_md = (isset($settings->swiper_container_p) && trim($settings->swiper_container_p)) ? $settings->swiper_container_p : "";
        $swiper_container_p_sm = (isset($settings->swiper_container_p_sm) && trim($settings->swiper_container_p_sm)) ? $settings->swiper_container_p_sm : "";
        $swiper_container_p_xs = (isset($settings->swiper_container_p_xs) && trim($settings->swiper_container_p_xs)) ? $settings->swiper_container_p_xs : "";
        $is_swiper_pagination = (isset($settings->is_swiper_pagination) && $settings->is_swiper_pagination) ? $settings->is_swiper_pagination : 0;


        // 上翻页按钮
        $swiper_button_prev = (isset($settings->swiper_button_prev) && $settings->swiper_button_prev) ? $settings->swiper_button_prev : 'https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png';
        // 下翻页按钮
        $swiper_button_next = (isset($settings->swiper_button_next) && $settings->swiper_button_next) ? $settings->swiper_button_next : 'https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png';
        // 移入上翻页按钮
        $swiper_button_prev_hover = (isset($settings->swiper_button_prev_hover) && $settings->swiper_button_prev_hover) ? $settings->swiper_button_prev_hover : '';
        // 移入下翻页按钮
        $swiper_button_next_hover = (isset($settings->swiper_button_next_hover) && $settings->swiper_button_next_hover) ? $settings->swiper_button_next_hover : '';
        // 切换按钮宽度
        $swiper_button_width_md = (isset($settings->swiper_button_width) && $settings->swiper_button_width) ? $settings->swiper_button_width : 24;
        $swiper_button_width_sm = (isset($settings->swiper_button_width_sm) && $settings->swiper_button_width_sm) ? $settings->swiper_button_width_sm : 24;
        $swiper_button_width_xs = (isset($settings->swiper_button_width_xs) && $settings->swiper_button_width_xs) ? $settings->swiper_button_width_xs : 24;
        // 切换按钮高度
        $swiper_button_height_md = (isset($settings->swiper_button_height) && $settings->swiper_button_height) ? $settings->swiper_button_height : 24;
        $swiper_button_height_sm = (isset($settings->swiper_button_height_sm) && $settings->swiper_button_height_sm) ? $settings->swiper_button_height_sm : 24;
        $swiper_button_height_xs = (isset($settings->swiper_button_height_xs) && $settings->swiper_button_height_xs) ? $settings->swiper_button_height_xs : 24;
        // 切换按钮上边距（百分比）
        $swiper_button_top_md = (isset($settings->swiper_button_top) && $settings->swiper_button_top) ? $settings->swiper_button_top : 48;
        $swiper_button_top_sm = (isset($settings->swiper_button_top_sm) && $settings->swiper_button_top_sm) ? $settings->swiper_button_top_sm : 48;
        $swiper_button_top_xs = (isset($settings->swiper_button_top_xs) && $settings->swiper_button_top_xs) ? $settings->swiper_button_top_xs : 48;
        // 切换按钮 上边距单位
        $swiper_button_top_unit = (isset($settings->is_swiper_button_top_px) && $settings->is_swiper_button_top_px == 1) ? 'px' : '%';
        // 切换按钮两侧边距（px）
        $swiper_button_left_md = (isset($settings->swiper_button_left) && $settings->swiper_button_left) ? $settings->swiper_button_left : 10;
        $swiper_button_left_sm = (isset($settings->swiper_button_left_sm) && $settings->swiper_button_left_sm) ? $settings->swiper_button_left_sm : 10;
        $swiper_button_left_xs = (isset($settings->swiper_button_left_xs) && $settings->swiper_button_left_xs) ? $settings->swiper_button_left_xs : 10;


        // 菜单一行显示个数
        //pc端一行显示个数
        $section_tab_num_md = (isset($settings->section_tab_num) && $settings->section_tab_num) ? $settings->section_tab_num : 5;
        //平板端一行显示个数
        $section_tab_num_sm = (isset($settings->section_tab_num_sm) && $settings->section_tab_num_sm) ? $settings->section_tab_num_sm : 5;
        //手机端一行显示个数
        $section_tab_num_xs = (isset($settings->section_tab_num_xs) && $settings->section_tab_num_xs) ? $settings->section_tab_num_xs : 3;

        // 菜单高度
        $section_tab_height_md = (isset($settings->section_tab_height) && $settings->section_tab_height) ? $settings->section_tab_height : "auto";
        $section_tab_height_sm = (isset($settings->section_tab_height_sm) && $settings->section_tab_height_sm) ? $settings->section_tab_height_sm : "auto";
        $section_tab_height_xs = (isset($settings->section_tab_height_xs) && $settings->section_tab_height_xs) ? $settings->section_tab_height_xs : "auto";

        // 菜单外边距
        $section_tab_m_md = (isset($settings->section_tab_m) && trim($settings->section_tab_m)) ? $settings->section_tab_m : "";
        $section_tab_m_sm = (isset($settings->section_tab_m_sm) && trim($settings->section_tab_m_sm)) ? $settings->section_tab_m_sm : "";
        $section_tab_m_xs = trim($settings->section_tab_m_xs) ? $settings->section_tab_m_xs : "0";

        // 下划线下边距
        $section_line_m_md = (isset($settings->section_line_m) && trim($settings->section_line_m)) ? $settings->section_line_m : "";
        $section_line_m_sm = (isset($settings->section_line_m_sm) && trim($settings->section_line_m_sm)) ? $settings->section_line_m_sm : "";
        $section_line_m_xs = (isset($settings->section_line_m_xs) && trim($settings->section_line_m_xs)) ? $settings->section_line_m_xs : "";

        // 菜单内边距
        $section_tab_p_md = (isset($settings->section_tab_p) && trim($settings->section_tab_p)) ? $settings->section_tab_p : "";
        $section_tab_p_sm = (isset($settings->section_tab_p_sm) && trim($settings->section_tab_p_sm)) ? $settings->section_tab_p_sm : "";
        $section_tab_p_xs = (isset($settings->section_tab_p_xs) && trim($settings->section_tab_p_xs)) ? $settings->section_tab_p_xs : "";

        // 名称外边距
        $section_name_m_md = (isset($settings->section_name_m) && trim($settings->section_name_m)) ? $settings->section_name_m : "";
        $section_name_m_sm = (isset($settings->section_name_m_sm) && trim($settings->section_name_m_sm)) ? $settings->section_name_m_sm : "";
        $section_name_m_xs = (isset($settings->section_name_m_xs) && trim($settings->section_name_m_xs)) ? $settings->section_name_m_xs : "";

        // 名称内边距
        $section_name_p_md = (isset($settings->section_name_p) && trim($settings->section_name_p)) ? $settings->section_name_p : "";
        $section_name_p_sm = (isset($settings->section_name_p_sm) && trim($settings->section_name_p_sm)) ? $settings->section_name_p_sm : "";
        $section_name_p_xs = (isset($settings->section_name_p_xs) && trim($settings->section_name_p_xs)) ? $settings->section_name_p_xs : "";

        // 图标外边距
        $section_icon_m_md = (isset($settings->section_icon_m) && trim($settings->section_icon_m)) ? $settings->section_icon_m : "";
        $section_icon_m_sm = (isset($settings->section_icon_m_sm) && trim($settings->section_icon_m_sm)) ? $settings->section_icon_m_sm : "";
        $section_icon_m_xs = (isset($settings->section_icon_m_xs) && trim($settings->section_icon_m_xs)) ? $settings->section_icon_m_xs : "";

        // 简介内边距
        $section_desc_p_md = (isset($settings->section_desc_p) && trim($settings->section_desc_p)) ? $settings->section_desc_p : "";
        $section_desc_p_sm = (isset($settings->section_desc_p_sm) && trim($settings->section_desc_p_sm)) ? $settings->section_desc_p_sm : "";
        $section_desc_p_xs = (isset($settings->section_desc_p_xs) && trim($settings->section_desc_p_xs)) ? $settings->section_desc_p_xs : "";

        // 简介高度
        $section_desc_height_md = (isset($settings->section_desc_height) && $settings->section_desc_height) ? $settings->section_desc_height : "auto";
        $section_desc_height_sm = (isset($settings->section_desc_height_sm) && $settings->section_desc_height_sm) ? $settings->section_desc_height_sm : "auto";
        $section_desc_height_xs = (isset($settings->section_desc_height_xs) && $settings->section_desc_height_xs) ? $settings->section_desc_height_xs : "auto";


        // 排列方式
        $content_style = (isset($settings->content_style) && $settings->content_style) ? $settings->content_style : "column";
        // 图标对齐方式
        $content_icon_align = (isset($settings->content_icon_align) && $settings->content_icon_align) ? $settings->content_icon_align : "center";
        // 名称对齐方式
        $content_name_align = (isset($settings->content_name_align) && $settings->content_name_align) ? $settings->content_name_align : "center";
        // 副标题对齐方式
        $content_s_name_align = (isset($settings->content_s_name_align) && $settings->content_s_name_align) ? $settings->content_s_name_align : "center";
        // 简介对齐方式
        $content_desc_align = (isset($settings->content_desc_align) && $settings->content_desc_align) ? $settings->content_desc_align : "center";

        // 是否统一背景
        $is_unite_bgColor = (isset($settings->is_unite_bgColor) && $settings->is_unite_bgColor) ? $settings->is_unite_bgColor : 0;
        $bgColor_wz = (isset($settings->bgColor_wz) && $settings->bgColor_wz) ? $settings->bgColor_wz : 1;
        // 是否显示名称
        $is_show_name = (isset($settings->is_show_name) && $settings->is_show_name) ? $settings->is_show_name : 0;
        // 是否显示名称下划线
        $is_show_name_line = (isset($settings->is_show_name_line) && $settings->is_show_name_line) ? $settings->is_show_name_line : 0;
        // 是否显示简介
        $is_show_desc = (isset($settings->is_show_desc) && $settings->is_show_desc) ? $settings->is_show_desc : 0;

        // 更多按钮外边距
        $more_icon_m_md = (isset($settings->more_icon_m) && trim($settings->more_icon_m)) ? $settings->more_icon_m : "";
        $more_icon_m_sm = (isset($settings->more_icon_m_sm) && trim($settings->more_icon_m_sm)) ? $settings->more_icon_m_sm : "";
        $more_icon_m_xs = (isset($settings->more_icon_m_xs) && trim($settings->more_icon_m_xs)) ? $settings->more_icon_m_xs : "";

        // 更多按钮宽度
        $more_icon_width_md = (isset($settings->more_icon_width) && $settings->more_icon_width) ? $settings->more_icon_width : "auto";
        $more_icon_width_sm = (isset($settings->more_icon_width_sm) && $settings->more_icon_width_sm) ? $settings->more_icon_width_sm : "auto";
        $more_icon_width_xs = (isset($settings->more_icon_width_xs) && $settings->more_icon_width_xs) ? $settings->more_icon_width_xs : "auto";

        // 更多按钮对齐方式
        $more_icon_align = (isset($settings->more_icon_align) && $settings->more_icon_align) ? $settings->more_icon_align : "flex-start";
        // 正常状态关闭更多按钮
        $is_show_more_normal = (isset($settings->is_show_more_normal) && $settings->is_show_more_normal) ? $settings->is_show_more_normal : 0;

        /* ---- 正常 ---- */
        // 名称文字大小
        $section_tab_fontsize_md = (isset($settings->section_tab_fontsize) && $settings->section_tab_fontsize) ? $settings->section_tab_fontsize : 18;
        $section_tab_fontsize_sm = (isset($settings->section_tab_fontsize_sm) && $settings->section_tab_fontsize_sm) ? $settings->section_tab_fontsize_sm : 18;
        $section_tab_fontsize_xs = (isset($settings->section_tab_fontsize_xs) && $settings->section_tab_fontsize_xs) ? $settings->section_tab_fontsize_xs : 16;
        // 名称文字行高
        $section_tab_lineHeight_md = (isset($settings->section_tab_lineHeight) && $settings->section_tab_lineHeight) ? $settings->section_tab_lineHeight : 32;
        $section_tab_lineHeight_sm = (isset($settings->section_tab_lineHeight_sm) && $settings->section_tab_lineHeight_sm) ? $settings->section_tab_lineHeight_sm : 32;
        $section_tab_lineHeight_xs = (isset($settings->section_tab_lineHeight_xs) && $settings->section_tab_lineHeight_xs) ? $settings->section_tab_lineHeight_xs : 32;
        // 名称文字颜色
        $section_tab_fontcolor = (isset($settings->section_tab_fontcolor) && $settings->section_tab_fontcolor) ? $settings->section_tab_fontcolor : "#ffffff";
        // 名称文字加粗
        $section_tab_fontW = (isset($settings->section_tab_fontW) && $settings->section_tab_fontW) ? $settings->section_tab_fontW : 0;
        // 副标题文字大小
        $section_tab_s_name_fontsize_md = (isset($settings->section_tab_s_name_fontsize) && $settings->section_tab_s_name_fontsize) ? $settings->section_tab_s_name_fontsize : 18;
        $section_tab_s_name_fontsize_sm = (isset($settings->section_tab_s_name_fontsize_sm) && $settings->section_tab_s_name_fontsize_sm) ? $settings->section_tab_s_name_fontsize_sm : 18;
        $section_tab_s_name_fontsize_xs = (isset($settings->section_tab_s_name_fontsize_xs) && $settings->section_tab_s_name_fontsize_xs) ? $settings->section_tab_s_name_fontsize_xs : 16;
        // 副标题文字行高
        $section_tab_s_name_lineHeight_md = (isset($settings->section_tab_s_name_lineHeight) && $settings->section_tab_s_name_lineHeight) ? $settings->section_tab_s_name_lineHeight : 32;
        $section_tab_s_name_lineHeight_sm = (isset($settings->section_tab_s_name_lineHeight_sm) && $settings->section_tab_s_name_lineHeight_sm) ? $settings->section_tab_s_name_lineHeight_sm : 32;
        $section_tab_s_name_lineHeight_xs = (isset($settings->section_tab_s_name_lineHeight_xs) && $settings->section_tab_s_name_lineHeight_xs) ? $settings->section_tab_s_name_lineHeight_xs : 32;
        // 副标题文字颜色
        $section_tab_s_name_fontcolor = (isset($settings->section_tab_s_name_fontcolor) && $settings->section_tab_s_name_fontcolor) ? $settings->section_tab_s_name_fontcolor : "#ffffff";
        // 简介文字大小
        $section_desc_fontsize_md = (isset($settings->section_desc_fontsize) && $settings->section_desc_fontsize) ? $settings->section_desc_fontsize : 16;
        $section_desc_fontsize_sm = (isset($settings->section_desc_fontsize_sm) && $settings->section_desc_fontsize_sm) ? $settings->section_desc_fontsize_sm : 16;
        $section_desc_fontsize_xs = (isset($settings->section_desc_fontsize_xs) && $settings->section_desc_fontsize_xs) ? $settings->section_desc_fontsize_xs : 16;
        // 简介文字行高
        $section_desc_lineHeight_md = (isset($settings->section_desc_lineHeight) && $settings->section_desc_lineHeight) ? $settings->section_desc_lineHeight : 32;
        $section_desc_lineHeight_sm = (isset($settings->section_desc_lineHeight_sm) && $settings->section_desc_lineHeight_sm) ? $settings->section_desc_lineHeight_sm : 32;
        $section_desc_lineHeight_xs = (isset($settings->section_desc_lineHeight_xs) && $settings->section_desc_lineHeight_xs) ? $settings->section_desc_lineHeight_xs : 32;
        // 简介文字颜色
        $section_desc_fontcolor = (isset($settings->section_desc_fontcolor) && $settings->section_desc_fontcolor) ? $settings->section_desc_fontcolor : "#ffffff";
        // 图标宽度
        $section_tab_icon_width_md = (isset($settings->section_tab_icon_width) && $settings->section_tab_icon_width) ? $settings->section_tab_icon_width : "";
        $section_tab_icon_width_sm = (isset($settings->section_tab_icon_width_sm) && $settings->section_tab_icon_width_sm) ? $settings->section_tab_icon_width_sm : "";
        $section_tab_icon_width_xs = (isset($settings->section_tab_icon_width_xs) && $settings->section_tab_icon_width_xs) ? $settings->section_tab_icon_width_xs : "";
        // 图标高度
        $section_tab_icon_height_md = (isset($settings->section_tab_icon_height) && $settings->section_tab_icon_height) ? $settings->section_tab_icon_height : "";
        $section_tab_icon_height_sm = (isset($settings->section_tab_icon_height_sm) && $settings->section_tab_icon_height_sm) ? $settings->section_tab_icon_height_sm : "";
        $section_tab_icon_height_xs = (isset($settings->section_tab_icon_height_xs) && $settings->section_tab_icon_height_xs) ? $settings->section_tab_icon_height_xs : "";

        // 下划线宽度
        $line_border_width_md = (isset($settings->line_border_width) && $settings->line_border_width) ? $settings->line_border_width : "";
        $line_border_width_sm = (isset($settings->line_border_width_sm) && $settings->line_border_width_sm) ? $settings->line_border_width_sm : "";
        $line_border_width_xs = (isset($settings->line_border_width_xs) && $settings->line_border_width_xs) ? $settings->line_border_width_xs : "";
        // 下划线高度
        $line_border_height_md = (isset($settings->line_border_height) && $settings->line_border_height) ? $settings->line_border_height : "";
        $line_border_height_sm = (isset($settings->line_border_height_sm) && $settings->line_border_height_sm) ? $settings->line_border_height_sm : "";
        $line_border_height_xs = (isset($settings->line_border_height_xs) && $settings->line_border_height_xs) ? $settings->line_border_height_xs : "";
        // 下划线对齐方式
        $line_border_align = (isset($settings->line_border_align) && $settings->line_border_align) ? $settings->line_border_align : "center";
        // 下划线背景样式
        $line_border_color_style = (isset($settings->line_border_color_style) && $settings->line_border_color_style) ? $settings->line_border_color_style : "color";
        // 下划线颜色
        $line_border_color = (isset($settings->line_border_color) && $settings->line_border_color) ? $settings->line_border_color : "#ffffff";
        // 下划线样式
        $line_border_style = (isset($settings->line_border_style) && $settings->line_border_style) ? $settings->line_border_style : "none";
        // 下划线渐变色
        $line_border_bgGradient = (isset($settings->line_border_bgGradient) && $settings->line_border_bgGradient) ? $settings->line_border_bgGradient : null;


        // 统一背景颜色
        $section_tab_bgColor = (isset($settings->section_tab_bgColor) && $settings->section_tab_bgColor) ? $settings->section_tab_bgColor : "#ffffff";

        // 开启投影
        $is_shadow = (isset($settings->is_shadow) && $settings->is_shadow) ? $settings->is_shadow : 0;
        // 投影颜色
        $box_color = (isset($settings->box_color) && $settings->box_color) ? $settings->box_color : "#ffffff";
        // 水平偏移
        $box_h_shadow_md = (isset($settings->box_h_shadow) && $settings->box_h_shadow) ? $settings->box_h_shadow : 1;
        $box_h_shadow_sm = (isset($settings->box_h_shadow_sm) && $settings->box_h_shadow_sm) ? $settings->box_h_shadow_sm : 1;
        $box_h_shadow_xs = (isset($settings->box_h_shadow_xs) && $settings->box_h_shadow_xs) ? $settings->box_h_shadow_xs : 1;
        // 垂直偏移
        $box_v_shadow_md = (isset($settings->box_v_shadow) && $settings->box_v_shadow) ? $settings->box_v_shadow : 1;
        $box_v_shadow_sm = (isset($settings->box_v_shadow_sm) && $settings->box_v_shadow_sm) ? $settings->box_v_shadow_sm : 1;
        $box_v_shadow_xs = (isset($settings->box_v_shadow_xs) && $settings->box_v_shadow_xs) ? $settings->box_v_shadow_xs : 1;
        // 模糊
        $box_blur_md = (isset($settings->box_blur) && $settings->box_blur) ? $settings->box_blur : 1;
        $box_blur_sm = (isset($settings->box_blur_sm) && $settings->box_blur_sm) ? $settings->box_blur_sm : 1;
        $box_blur_xs = (isset($settings->box_blur_xs) && $settings->box_blur_xs) ? $settings->box_blur_xs : 1;
        // 扩展
        $box_spread_md = (isset($settings->box_spread) && $settings->box_spread) ? $settings->box_spread : 1;
        $box_spread_sm = (isset($settings->box_spread_sm) && $settings->box_spread_sm) ? $settings->box_spread_sm : 1;
        $box_spread_xs = (isset($settings->box_spread_xs) && $settings->box_spread_xs) ? $settings->box_spread_xs : 1;

        // 使用边框
        $is_border = (isset($settings->is_border) && $settings->is_border) ? $settings->is_border : 0;
        // 自定义边框宽度
        $custom_border_width = (isset($settings->custom_border_width) && $settings->custom_border_width) ? $settings->custom_border_width : 0;
        // 边框颜色
        $border_color = (isset($settings->border_color) && $settings->border_color) ? $settings->border_color : "#ffffff";
        // 边框宽度
        $border_width_md = (isset($settings->border_width) && $settings->border_width) ? $settings->border_width : 1;
        $border_width_sm = (isset($settings->border_width_sm) && $settings->border_width_sm) ? $settings->border_width_sm : 1;
        $border_width_xs = (isset($settings->border_width_xs) && $settings->border_width_xs) ? $settings->border_width_xs : 1;
        // 边框样式
        $border_style = (isset($settings->border_style) && $settings->border_style) ? $settings->border_style : "none";
        // 边框半径
        $border_radius_md = (isset($settings->border_radius) && $settings->border_radius) ? $settings->border_radius : 0;
        $border_radius_sm = (isset($settings->border_radius_sm) && $settings->border_radius_sm) ? $settings->border_radius_sm : 0;
        $border_radius_xs = (isset($settings->border_radius_xs) && $settings->border_radius_xs) ? $settings->border_radius_xs : 0;

        /* ---- 滑过 ---- */
        // 名称文字大小
        $section_tab_fontsize_hover_md = (isset($settings->section_tab_fontsize_hover) && $settings->section_tab_fontsize_hover) ? $settings->section_tab_fontsize_hover : null;
        $section_tab_fontsize_hover_sm = (isset($settings->section_tab_fontsize_hover_sm) && $settings->section_tab_fontsize_hover_sm) ? $settings->section_tab_fontsize_hover_sm : null;
        $section_tab_fontsize_hover_xs = (isset($settings->section_tab_fontsize_hover_xs) && $settings->section_tab_fontsize_hover_xs) ? $settings->section_tab_fontsize_hover_xs : null;
        // 名称文字颜色
        $section_tab_fontcolor_hover = (isset($settings->section_tab_fontcolor_hover) && $settings->section_tab_fontcolor_hover) ? $settings->section_tab_fontcolor_hover : null;
        // 副标题文字大小
        $section_tab_s_name_fontsize_hover_md = (isset($settings->section_tab_s_name_fontsize_hover) && $settings->section_tab_s_name_fontsize_hover) ? $settings->section_tab_s_name_fontsize_hover : null;
        $section_tab_s_name_fontsize_hover_sm = (isset($settings->section_tab_s_name_fontsize_hover_sm) && $settings->section_tab_s_name_fontsize_hover_sm) ? $settings->section_tab_s_name_fontsize_hover_sm : null;
        $section_tab_s_name_fontsize_hover_xs = (isset($settings->section_tab_s_name_fontsize_hover_xs) && $settings->section_tab_s_name_fontsize_hover_xs) ? $settings->section_tab_s_name_fontsize_hover_xs : null;
        // 副标题文字颜色
        $section_tab_s_name_fontcolor_hover = (isset($settings->section_tab_s_name_fontcolor_hover) && $settings->section_tab_s_name_fontcolor_hover) ? $settings->section_tab_s_name_fontcolor_hover : null;
        // 简介文字大小
        $section_desc_fontsize_hover_md = (isset($settings->section_desc_fontsize_hover) && $settings->section_desc_fontsize_hover) ? $settings->section_desc_fontsize_hover : null;
        $section_desc_fontsize_hover_sm = (isset($settings->section_desc_fontsize_hover_sm) && $settings->section_desc_fontsize_hover_sm) ? $settings->section_desc_fontsize_hover_sm : null;
        $section_desc_fontsize_hover_xs = (isset($settings->section_desc_fontsize_hover_xs) && $settings->section_desc_fontsize_hover_xs) ? $settings->section_desc_fontsize_hover_xs : null;
        // 简介文字颜色
        $section_desc_fontcolor_hover = (isset($settings->section_desc_fontcolor_hover) && $settings->section_desc_fontcolor_hover) ? $settings->section_desc_fontcolor_hover : null;
        // 图标宽度
        $section_tab_icon_width_hover_md = (isset($settings->section_tab_icon_width_hover) && $settings->section_tab_icon_width_hover) ? $settings->section_tab_icon_width_hover : null;
        $section_tab_icon_width_hover_sm = (isset($settings->section_tab_icon_width_hover_sm) && $settings->section_tab_icon_width_hover_sm) ? $settings->section_tab_icon_width_hover_sm : null;
        $section_tab_icon_width_hover_xs = (isset($settings->section_tab_icon_width_hover_xs) && $settings->section_tab_icon_width_hover_xs) ? $settings->section_tab_icon_width_hover_xs : null;
        // 图标高度
        $section_tab_icon_height_hover_md = (isset($settings->section_tab_icon_height_hover) && $settings->section_tab_icon_height_hover) ? $settings->section_tab_icon_height_hover : null;
        $section_tab_icon_height_hover_sm = (isset($settings->section_tab_icon_height_hover_sm) && $settings->section_tab_icon_height_hover_sm) ? $settings->section_tab_icon_height_hover_sm : null;
        $section_tab_icon_height_hover_xs = (isset($settings->section_tab_icon_height_hover_xs) && $settings->section_tab_icon_height_hover_xs) ? $settings->section_tab_icon_height_hover_xs : null;

        // 下划线宽度
        $line_border_width_hover_md = (isset($settings->line_border_width_hover) && $settings->line_border_width_hover) ? $settings->line_border_width_hover : "";
        $line_border_width_hover_sm = (isset($settings->line_border_width_hover_sm) && $settings->line_border_width_hover_sm) ? $settings->line_border_width_hover_sm : "";
        $line_border_width_hover_xs = (isset($settings->line_border_width_hover_xs) && $settings->line_border_width_hover_xs) ? $settings->line_border_width_hover_xs : "";
        // 下划线高度
        $line_border_height_hover_md = (isset($settings->line_border_height_hover) && $settings->line_border_height_hover) ? $settings->line_border_height_hover : "";
        $line_border_height_hover_sm = (isset($settings->line_border_height_hover_sm) && $settings->line_border_height_hover_sm) ? $settings->line_border_height_hover_sm : "";
        $line_border_height_hover_xs = (isset($settings->line_border_height_hover_xs) && $settings->line_border_height_hover_xs) ? $settings->line_border_height_hover_xs : "";
        // 下划线颜色
        $line_border_color_hover = (isset($settings->line_border_color_hover) && $settings->line_border_color_hover) ? $settings->line_border_color_hover : "#ffffff";
        // 下划线样式
        $line_border_style_hover = (isset($settings->line_border_style_hover) && $settings->line_border_style_hover) ? $settings->line_border_style_hover : "none";
        // 下划线渐变色
        $line_border_bgGradient_hover = (isset($settings->line_border_bgGradient_hover) && $settings->line_border_bgGradient_hover) ? $settings->line_border_bgGradient_hover : null;

        // 统一背景颜色
        $section_tab_bgColor_hover = (isset($settings->section_tab_bgColor_hover) && $settings->section_tab_bgColor_hover) ? $settings->section_tab_bgColor_hover : null;

        // 开启投影
        $is_shadow_hover = (isset($settings->is_shadow_hover) && $settings->is_shadow_hover) ? $settings->is_shadow_hover : 0;
        // 投影颜色
        $box_color_hover = (isset($settings->box_color_hover) && $settings->box_color_hover) ? $settings->box_color_hover : null;
        // 水平偏移
        $box_h_shadow_hover_md = (isset($settings->box_h_shadow_hover) && $settings->box_h_shadow_hover) ? $settings->box_h_shadow_hover : null;
        $box_h_shadow_hover_sm = (isset($settings->box_h_shadow_hover_sm) && $settings->box_h_shadow_hover_sm) ? $settings->box_h_shadow_hover_sm : null;
        $box_h_shadow_hover_xs = (isset($settings->box_h_shadow_hover_xs) && $settings->box_h_shadow_hover_xs) ? $settings->box_h_shadow_hover_xs : null;
        // 垂直偏移
        $box_v_shadow_hover_md = (isset($settings->box_v_shadow_hover) && $settings->box_v_shadow_hover) ? $settings->box_v_shadow_hover : null;
        $box_v_shadow_hover_sm = (isset($settings->box_v_shadow_hover_sm) && $settings->box_v_shadow_hover_sm) ? $settings->box_v_shadow_hover_sm : null;
        $box_v_shadow_hover_xs = (isset($settings->box_v_shadow_hover_xs) && $settings->box_v_shadow_hover_xs) ? $settings->box_v_shadow_hover_xs : null;
        // 模糊
        $box_blur_hover_md = (isset($settings->box_blur_hover) && $settings->box_blur_hover) ? $settings->box_blur_hover : null;
        $box_blur_hover_sm = (isset($settings->box_blur_hover_sm) && $settings->box_blur_hover_sm) ? $settings->box_blur_hover_sm : null;
        $box_blur_hover_xs = (isset($settings->box_blur_hover_xs) && $settings->box_blur_hover_xs) ? $settings->box_blur_hover_xs : null;
        // 扩展
        $box_spread_hover_md = (isset($settings->box_spread_hover) && $settings->box_spread_hover) ? $settings->box_spread_hover : null;
        $box_spread_hover_sm = (isset($settings->box_spread_hover_sm) && $settings->box_spread_hover_sm) ? $settings->box_spread_hover_sm : null;
        $box_spread_hover_xs = (isset($settings->box_spread_hover_xs) && $settings->box_spread_hover_xs) ? $settings->box_spread_hover_xs : null;

        // 使用边框
        $is_border_hover = (isset($settings->is_border_hover) && $settings->is_border_hover) ? $settings->is_border_hover : 0;
        // 边框颜色
        $border_color_hover = (isset($settings->border_color_hover) && $settings->border_color_hover) ? $settings->border_color_hover : null;
        // 边框宽度
        $border_width_hover_md = (isset($settings->border_width_hover) && $settings->border_width_hover) ? $settings->border_width_hover : null;
        $border_width_hover_sm = (isset($settings->border_width_hover_sm) && $settings->border_width_hover_sm) ? $settings->border_width_hover_sm : null;
        $border_width_hover_xs = (isset($settings->border_width_hover_xs) && $settings->border_width_hover_xs) ? $settings->border_width_hover_xs : null;
        // 边框样式
        $border_style_hover = (isset($settings->border_style_hover) && $settings->border_style_hover) ? $settings->border_style_hover : null;
        // 边框半径
        $border_radius_hover_md = (isset($settings->border_radius_hover) && $settings->border_radius_hover) ? $settings->border_radius_hover : null;
        $border_radius_hover_sm = (isset($settings->border_radius_hover_sm) && $settings->border_radius_hover_sm) ? $settings->border_radius_hover_sm : null;
        $border_radius_hover_xs = (isset($settings->border_radius_hover_xs) && $settings->border_radius_hover_xs) ? $settings->border_radius_hover_xs : null;

        // 使用动画
        $is_animate_hover = (isset($settings->is_animate_hover) && $settings->is_animate_hover) ? $settings->is_animate_hover : 0;
        // 图标动画效果
        $animate_icon = (isset($settings->animate_icon) && $settings->animate_icon) ? $settings->animate_icon : "pulse";
        // 整个item动画效果
        $animate_menu = (isset($settings->animate_menu) && $settings->animate_menu) ? $settings->animate_menu : "translateY";
        // 移动距离
        $animate_translate = (isset($settings->animate_translate) && $settings->animate_translate) ? $settings->animate_translate : -12;

        // 选项卡布局
        $tab_theme = (isset($settings->tab_theme) && $settings->tab_theme) ? $settings->tab_theme : "type01";
        // 选项卡一行显示个数
        $change_tab_num_md = (isset($settings->change_tab_num) && $settings->change_tab_num) ? $settings->change_tab_num : 4;
        $change_tab_num_sm = (isset($settings->change_tab_num_sm) && $settings->change_tab_num_sm) ? $settings->change_tab_num_sm : '';
        $change_tab_num_xs = (isset($settings->change_tab_num_xs) && $settings->change_tab_num_xs) ? $settings->change_tab_num_xs : '';
        // 选项卡高度
        $tab_item_height_md = (isset($settings->tab_item_height) && $settings->tab_item_height) ? $settings->tab_item_height : 67;
        $tab_item_height_sm = (isset($settings->tab_item_height_sm) && $settings->tab_item_height_sm) ? $settings->tab_item_height_sm : '';
        $tab_item_height_xs = (isset($settings->tab_item_height_xs) && $settings->tab_item_height_xs) ? $settings->tab_item_height_xs : '';
        // 选项卡对齐方式
        $tab_align = (isset($settings->tab_align) && $settings->tab_align) ? $settings->tab_align : "flex-start";

        // 边框颜色
        $tab_box_border_color = (isset($settings->tab_box_border_color) && $settings->tab_box_border_color) ? $settings->tab_box_border_color : "";
        // 边框宽度
        $tab_box_border_width_md = (isset($settings->tab_box_border_width) && $settings->tab_box_border_width) ? $settings->tab_box_border_width : 0;
        $tab_box_border_width_sm = (isset($settings->tab_box_border_width_sm) && $settings->tab_box_border_width_sm) ? $settings->tab_box_border_width_sm : '';
        $tab_box_border_width_xs = (isset($settings->tab_box_border_width_xs) && $settings->tab_box_border_width_xs) ? $settings->tab_box_border_width_xs : '';
        // 边框样式
        $tab_box_border_style = (isset($settings->tab_box_border_style) && $settings->tab_box_border_style) ? $settings->tab_box_border_style : "none";
        // 边框半径
        $tab_box_border_radius_md = (isset($settings->tab_box_border_radius) && $settings->tab_box_border_radius) ? $settings->tab_box_border_radius : 0;
        $tab_box_border_radius_sm = (isset($settings->tab_box_border_radius_sm) && $settings->tab_box_border_radius_sm) ? $settings->tab_box_border_radius_sm : '';
        $tab_box_border_radius_xs = (isset($settings->tab_box_border_radius_xs) && $settings->tab_box_border_radius_xs) ? $settings->tab_box_border_radius_xs : '';

        // 选项卡整体背景样式
        $tab_box_bg_style = (isset($settings->tab_box_bg_style) && $settings->tab_box_bg_style) ? $settings->tab_box_bg_style : "color";
        // 选项卡整体背景颜色
        $tab_box_bgColor = (isset($settings->tab_box_bgColor) && $settings->tab_box_bgColor) ? $settings->tab_box_bgColor : "";
        // 选项卡整体渐变色背景
        $tab_box_bgGradient = (isset($settings->tab_box_bgGradient) && $settings->tab_box_bgGradient) ? $settings->tab_box_bgGradient : null;

        /* 正常 */
        // 文字大小
        $tab_item_fontsize_md = (isset($settings->tab_item_fontsize) && $settings->tab_item_fontsize) ? $settings->tab_item_fontsize : 18;
        $tab_item_fontsize_sm = (isset($settings->tab_item_fontsize_sm) && $settings->tab_item_fontsize_sm) ? $settings->tab_item_fontsize_sm : '';
        $tab_item_fontsize_xs = (isset($settings->tab_item_fontsize_xs) && $settings->tab_item_fontsize_xs) ? $settings->tab_item_fontsize_xs : '';
        // 文字加粗
        $tab_item_fontW = (isset($settings->tab_item_fontW) && $settings->tab_item_fontW) ? $settings->tab_item_fontW : 0;
        // 文字颜色
        $tab_item_fontcolor = (isset($settings->tab_item_fontcolor) && $settings->tab_item_fontcolor) ? $settings->tab_item_fontcolor : "#979A9F";

        /* 滑过 */
        // 文字大小
        $tab_item_fontsize_hover_md = (isset($settings->tab_item_fontsize_hover) && $settings->tab_item_fontsize_hover) ? $settings->tab_item_fontsize_hover : 18;
        $tab_item_fontsize_hover_sm = (isset($settings->tab_item_fontsize_hover_sm) && $settings->tab_item_fontsize_hover_sm) ? $settings->tab_item_fontsize_hover_sm : '';
        $tab_item_fontsize_hover_xs = (isset($settings->tab_item_fontsize_hover_xs) && $settings->tab_item_fontsize_hover_xs) ? $settings->tab_item_fontsize_hover_xs : '';
        // 文字加粗
        $tab_item_fontW_hover = (isset($settings->tab_item_fontW_hover) && $settings->tab_item_fontW_hover) ? $settings->tab_item_fontW_hover : 0;
        // 文字颜色
        $tab_item_fontcolor_hover = (isset($settings->tab_item_fontcolor_hover) && $settings->tab_item_fontcolor_hover) ? $settings->tab_item_fontcolor_hover : "#111111";

        /* 选中 */
        // 文字大小
        $tab_item_fontsize_active_md = (isset($settings->tab_item_fontsize_active) && $settings->tab_item_fontsize_active) ? $settings->tab_item_fontsize_active : 18;
        $tab_item_fontsize_active_sm = (isset($settings->tab_item_fontsize_active_sm) && $settings->tab_item_fontsize_active_sm) ? $settings->tab_item_fontsize_active_sm : '';
        $tab_item_fontsize_active_xs = (isset($settings->tab_item_fontsize_active_xs) && $settings->tab_item_fontsize_active_xs) ? $settings->tab_item_fontsize_active_xs : '';
        // 文字加粗
        $tab_item_fontW_active = (isset($settings->tab_item_fontW_active) && $settings->tab_item_fontW_active) ? $settings->tab_item_fontW_active : 0;
        // 文字颜色
        $tab_item_fontcolor_active = (isset($settings->tab_item_fontcolor_active) && $settings->tab_item_fontcolor_active) ? $settings->tab_item_fontcolor_active : "#111111";
        // 下划线高度
        $tab_item_bor_h_active_md = (isset($settings->tab_item_bor_h_active) && $settings->tab_item_bor_h_active) ? $settings->tab_item_bor_h_active : 4;
        $tab_item_bor_h_active_sm = (isset($settings->tab_item_bor_h_active_sm) && $settings->tab_item_bor_h_active_sm) ? $settings->tab_item_bor_h_active_sm : '';
        $tab_item_bor_h_active_xs = (isset($settings->tab_item_bor_h_active_xs) && $settings->tab_item_bor_h_active_xs) ? $settings->tab_item_bor_h_active_xs : '';
        // 下划线颜色
        $tab_item_bor_c_active = (isset($settings->tab_item_bor_c_active) && $settings->tab_item_bor_c_active) ? $settings->tab_item_bor_c_active : "#B71B28";

        /*菜单项 内容配置*/
        // 菜单项内容边距
        $item_content_p_md = (isset($settings->item_content_p) && $settings->item_content_p) ? $settings->item_content_p : 0;
        $item_content_p_sm = (isset($settings->item_content_p_sm) && $settings->item_content_p_sm) ? $settings->item_content_p_sm : '';
        $item_content_p_xs = (isset($settings->item_content_p_xs) && $settings->item_content_p_xs) ? $settings->item_content_p_xs : '';
        // 内容布局方式（pc及平板）
        $item_content_style = (isset($settings->item_content_style) && $settings->item_content_style) ? $settings->item_content_style : "row";
        // 内容布局方式（手机端）
        $item_content_style_xs = (isset($settings->item_content_style_xs) && $settings->item_content_style_xs) ? $settings->item_content_style_xs : "column";
        // 图文之间间距
        $item_content_between_md = (isset($settings->item_content_between) && $settings->item_content_between) ? $settings->item_content_between : 154;
        $item_content_between_sm = (isset($settings->item_content_between_sm) && $settings->item_content_between_sm) ? $settings->item_content_between_sm : '';
        $item_content_between_xs = (isset($settings->item_content_between_xs) && $settings->item_content_between_xs) ? $settings->item_content_between_xs : '';

        // 内容整体背景颜色
        $item_content_bg = (isset($settings->item_content_bg) && $settings->item_content_bg) ? $settings->item_content_bg : '';
        
        // 内容图片边框半径
        $item_content_img_border_radius_md = strlen($settings->item_content_img_border_radius) ? $settings->item_content_img_border_radius : 4;
        $item_content_img_border_radius_sm = strlen($settings->item_content_img_border_radius_sm) ? $settings->item_content_img_border_radius_sm : '';
        $item_content_img_border_radius_xs = strlen($settings->item_content_img_border_radius_xs) ? $settings->item_content_img_border_radius_xs : '';

        // 内容标题文字大小
        $item_content_t_fontsize_md = (isset($settings->item_content_t_fontsize) && $settings->item_content_t_fontsize) ? $settings->item_content_t_fontsize : 24;
        $item_content_t_fontsize_sm = (isset($settings->item_content_t_fontsize_sm) && $settings->item_content_t_fontsize_sm) ? $settings->item_content_t_fontsize_sm : '';
        $item_content_t_fontsize_xs = (isset($settings->item_content_t_fontsize_xs) && $settings->item_content_t_fontsize_xs) ? $settings->item_content_t_fontsize_xs : '';
        // 内容标题文字加粗
        $item_content_t_fontW = (isset($settings->item_content_t_fontW) && $settings->item_content_t_fontW) ? $settings->item_content_t_fontW : 0;
        // 内容标题文字行高
        $item_content_t_lineHeight_md = (isset($settings->item_content_t_lineHeight) && $settings->item_content_t_lineHeight) ? $settings->item_content_t_lineHeight : 32;
        $item_content_t_lineHeight_sm = (isset($settings->item_content_t_lineHeight_sm) && $settings->item_content_t_lineHeight_sm) ? $settings->item_content_t_lineHeight_sm : '';
        $item_content_t_lineHeight_xs = (isset($settings->item_content_t_lineHeight_xs) && $settings->item_content_t_lineHeight_xs) ? $settings->item_content_t_lineHeight_xs : '';
        // 内容标题文字颜色
        $item_content_t_fontcolor = (isset($settings->item_content_t_fontcolor) && $settings->item_content_t_fontcolor) ? $settings->item_content_t_fontcolor : '#111111';
        // 内容标题下边距
        $item_content_t_mb_md = (isset($settings->item_content_t_mb) && $settings->item_content_t_mb) ? $settings->item_content_t_mb : 54;
        $item_content_t_mb_sm = (isset($settings->item_content_t_mb_sm) && $settings->item_content_t_mb_sm) ? $settings->item_content_t_mb_sm : '';
        $item_content_t_mb_xs = (isset($settings->item_content_t_mb_xs) && $settings->item_content_t_mb_xs) ? $settings->item_content_t_mb_xs : '';
        // 内容简介文字大小
        $item_content_d_fontsize_md = (isset($settings->item_content_d_fontsize) && $settings->item_content_d_fontsize) ? $settings->item_content_d_fontsize : 16;
        $item_content_d_fontsize_sm = (isset($settings->item_content_d_fontsize_sm) && $settings->item_content_d_fontsize_sm) ? $settings->item_content_d_fontsize_sm : '';
        $item_content_d_fontsize_xs = (isset($settings->item_content_d_fontsize_xs) && $settings->item_content_d_fontsize_xs) ? $settings->item_content_d_fontsize_xs : '';
        // 内容简介文字行高
        $item_content_d_lineHeight_md = (isset($settings->item_content_d_lineHeight) && $settings->item_content_d_lineHeight) ? $settings->item_content_d_lineHeight : 24;
        $item_content_d_lineHeight_sm = (isset($settings->item_content_d_lineHeight_sm) && $settings->item_content_d_lineHeight_sm) ? $settings->item_content_d_lineHeight_sm : '';
        $item_content_d_lineHeight_xs = (isset($settings->item_content_d_lineHeight_xs) && $settings->item_content_d_lineHeight_xs) ? $settings->item_content_d_lineHeight_xs : '';
        // 内容简介文字颜色
        $item_content_d_fontcolor = (isset($settings->item_content_d_fontcolor) && $settings->item_content_d_fontcolor) ? $settings->item_content_d_fontcolor : '#979A9F';

        // 内容图片宽度
        $item_content_image_width_md = (isset($settings->item_content_image_width) && $settings->item_content_image_width) ? $settings->item_content_image_width . 'px' : '100%';
        $item_content_image_width_sm = (isset($settings->item_content_image_width_sm) && $settings->item_content_image_width_sm) ? $settings->item_content_image_width_sm . 'px' : '100%';
        $item_content_image_width_xs = (isset($settings->item_content_image_width_xs) && $settings->item_content_image_width_xs) ? $settings->item_content_image_width_xs . 'px' : '100%';
        // 内容图片宽度
        $item_content_image_height_md = (isset($settings->item_content_image_height) && $settings->item_content_image_height) ? $settings->item_content_image_height . 'px' : '100%';
        $item_content_image_height_sm = (isset($settings->item_content_image_height_sm) && $settings->item_content_image_height_sm) ? $settings->item_content_image_height_sm . 'px' : '100%';
        $item_content_image_height_xs = (isset($settings->item_content_image_height_xs) && $settings->item_content_image_height_xs) ? $settings->item_content_image_height_xs . 'px' : '100%';
        // 图片填充方式
        $item_image_fit = (isset($settings->item_image_fit) && $settings->item_image_fit) ? $settings->item_image_fit : 'none';


        // 首字符放大
        $section_name_first = (isset($settings->section_name_first) && $settings->section_name_first) ? $settings->section_name_first : '0';
        $fontsize_first = (isset($settings->fontsize_first) && $settings->fontsize_first) ? $settings->fontsize_first : '1';
        $section_text_Height = isset($settings->section_text_Height) ? $settings->section_text_Height : '0';

        // 图标定位
        $icon_zdywz = (isset($settings->icon_zdywz) && $settings->icon_zdywz) ? $settings->icon_zdywz : '0';
        $icon_top = (isset($settings->icon_top) && $settings->icon_top) ? $settings->icon_top : '0';
        $icon_left = (isset($settings->icon_left) && $settings->icon_left) ? $settings->icon_left : '0';

        // 名称动画效果
        $animate_name = (isset($settings->animate_name) && $settings->animate_name) ? $settings->animate_name : "none";
        // 名称动画移动距离
        $animate_name_translate = (isset($settings->animate_name_translate) && $settings->animate_name_translate) ? $settings->animate_name_translate : -12;

        // 副标题动画效果
        $animate_s_name = (isset($settings->animate_s_name) && $settings->animate_s_name) ? $settings->animate_s_name : "none";
        // 副标题动画移动距离
        $animate_s_name_translate = (isset($settings->animate_s_name_translate) && $settings->animate_s_name_translate) ? $settings->animate_s_name_translate : -12;

        // 下划线动画效果
        $animate_name_line = (isset($settings->animate_name_line) && $settings->animate_name_line) ? $settings->animate_name_line : "none";
        // 下划线动画移动距离
        $animate_name_line_translate = (isset($settings->animate_name_line_translate) && $settings->animate_name_line_translate) ? $settings->animate_name_line_translate : -12;

        // 是否启用背景图片
        $is_bgImg = isset($settings->is_bgImg) && $settings->is_bgImg ? $settings->is_bgImg : 0;

        // 是否启用菜单之间箭头
        $is_zhong_label = isset($settings->is_zhong_label) && $settings->is_zhong_label ? $settings->is_zhong_label : 0;
        $jintou_icon = isset($settings->jintou_icon) && $settings->jintou_icon ? $settings->jintou_icon : 'https://oss.lcweb01.cn/joomla/20220516/a778388170b2a96c37c594cfe88300bc.png';
        $jiantou_width = isset($settings->jiantou_width) && $settings->jiantou_width ? $settings->jiantou_width : '36';
        $jiantou_right = isset($settings->jiantou_right) && $settings->jiantou_right ? $settings->jiantou_right : 0;
        $jiantou_top = isset($settings->jiantou_top) && $settings->jiantou_top ? $settings->jiantou_top : '50';
        $jiantou_height = isset($settings->jiantou_height) && $settings->jiantou_height ? $settings->jiantou_height : '36';

        // 第几个箭头旋转90°
        $jiantou_nth_md = $settings->jiantou_nth ? $settings->jiantou_nth : '0';
        $jiantou_nth_sm = $settings->jiantou_nth_sm ? $settings->jiantou_nth_sm : '0';
        $jiantou_nth_xs = $settings->jiantou_nth_xs ? $settings->jiantou_nth_xs : '3';
        $jiantou_nthright = isset($settings->jiantou_nthright) && $settings->jiantou_nthright ? $settings->jiantou_nthright : 0;
        $jiantou_nthtop = isset($settings->jiantou_nthtop) && $settings->jiantou_nthtop ? $settings->jiantou_nthtop : '50';

        // 边框动画
        $animate_border_line = isset($settings->animate_border_line) && $settings->animate_border_line ? $settings->animate_border_line : 'none';

        $lineW = '';
        if($item_content_style == 'row' || $item_content_style == 'row-reverse') {
            $lineW = 'width';
        }else {
            $lineW = 'height';
        }

        $lineW_xs = '';
        if($item_content_style_xs == 'row' || $item_content_style_xs == 'row-reverse') {
            $lineW_xs = 'width';
        }else {
            $lineW_xs = 'height';
        }

        $css =
            $addonId . ' * {
				margin: 0;
				padding: 0;
			}';
            if($is_swiper == 1 && $is_swiper_pagination==1) {
                $css .= $addonId . ' .swiper-pagination{position: inherit!important;}';
                $css .= $addonId . ' .swiper-pagination-bullet{margin: 0px 2px;!important;}';

            }
			$css .=$addonId . ' .flex {
			    display: flex;
			    align-items: center;
			}
			' . $addonId . ' .section-5 {';
                if($is_swiper == 1 && $swiper_container_p_md) {
                    $css .= 'padding: ' . $swiper_container_p_md . ';';
                }
            $css .= '}
			' . $addonId . ' .section-5, ' . $addonId . ' .section-5 .s-ul {
				width: 100%;
			    display: flex;
			    display: -webkit-flex;
				/*align-items: center;*/';
                if($is_swiper == 0) {
                    $css .= 'flex-wrap: wrap;';
                }
                $css .= 'list-style: none;
			}';
            if($is_zhong_label==1){
                $css .= $addonId . ' .section-5 .s-ul>li:after{
                        width: '.$jiantou_width.'px;
                        height: '.$jiantou_height.'px;
                        position: absolute;
                        left: '.$jiantou_right.'px;
                        top:'.$jiantou_top.'px;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                }
                ';
                if($jiantou_nth_md!=0){

                    $css .=$addonId . ' .section-5 .s-li:nth-child('.$jiantou_nth_md.'):after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: '.$jiantou_nthright.'px;
                            top:'.$jiantou_nthtop.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                    }
                    '.$addonId . ' .section-5, ' . $addonId . ' .section-5 .s-ul {
                            display:block;
                            flex-wrap: inherit;
                        }
                    ' . $addonId . ' .section-5 .s-ul li{float:left;}
                    ' . $addonId . ' .section-5 .s-ul .s-li:nth-child('.$jiantou_nth_md.') ~ .s-li {float:right;}';
                    $css .=$addonId . ' .section-5 .s-li:nth-of-type('.$jiantou_nth_md.') ~ .s-li:after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: -15px;
                            top:'.$jiantou_top.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                    }
                    ';

                }

                $css .= $addonId . ' .section-5 .s-ul li:last-child:after{
                        width: 0px;
                        height: 0px;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                }';

            }


			$css .=$addonId . ' .section-5 .s-li {';
                $mg = 0;
                if($section_tab_m_md != ""){
                    $mg = $section_tab_m_md * 2;
                }
                $css .= 'width: calc(100% / ' . $section_tab_num_md . ' - ' . $mg .'px);
	            cursor: pointer;';
                if($is_unite_bgColor) {
                    if($bgColor_wz==1 && !$is_bgImg){
                        $css .= 'background-color: ' . $section_tab_bgColor . ' !important;';
                    }
                }
                //if($icon_zdywz==1 || ($settings->animate_bg && $settings->animate_bg != 'none')){
                    $css .= 'position: relative;';
                //}
                if($section_tab_height_md && $section_tab_height_md != 'auto') {
                    $css .= 'height: ' . $section_tab_height_md . 'px;';
                }else {
                    $css .= 'height: auto;';
                }
                if($section_tab_m_md && $is_swiper == 0) {
                    $css .= 'margin: ' . $section_tab_m_md . 'px;';
                }
                if($section_tab_p_md && !$is_bgImg) {
                    $css .= 'padding: ' . $section_tab_p_md . ';';
                }
                if($is_shadow == 1) {
                    $css .= 'box-shadow: ' . $box_color . ' ' . $box_h_shadow_md . 'px ' . $box_v_shadow_md . 'px ' . $box_blur_md . 'px ' . $box_spread_md . 'px;';
                }
                if($is_border == 1) {
                    $css .= 'border: ' . $border_color . ' ' . $border_style . ' ' . $border_width_md . 'px;';
                    $css .= 'border-radius: ' . $border_radius_md . 'px;';
                }
                $css .= 'transition: all ease-in-out 300ms;';
                if(($settings->animate_bg && $settings->animate_bg != 'none') || $is_bgImg) {
                    $css .= 'overflow: hidden;';
                }
			$css .= '}';
            if($is_border == 1 && $animate_border_line != 'none') {
                $css .= $addonId . ' .section-5 .s-li .border-line {
                    position: absolute;
                    background-color: ' . $border_color_hover . ';
				    transition: all ease-in-out 300ms;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-01,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-03
                {
                    width: 0px;
                    height: ' . $border_width_hover_md . 'px;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-02,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-04
                {
                    height: 0px;
                    width: ' . $border_width_hover_md . 'px;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-02,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-03
                {
                    bottom: 0;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-01,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-04
                {
                    top: 0;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-01,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-03,
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-04
                {
                    left: 0;
                }
                ' . $addonId . ' .section-5 .s-li .border-line.border-line-02
                {
                    right: 0;
                }
                ' . $addonId . ' .section-5 .s-li:hover .border-line.border-line-01,
                ' . $addonId . ' .section-5 .s-li.actives .border-line.border-line-01,
                ' . $addonId . ' .section-5 .s-li:hover .border-line.border-line-03,
                ' . $addonId . ' .section-5 .s-li.actives .border-line.border-line-03
                {
                    width: 100%;
                }
                ' . $addonId . ' .section-5 .s-li:hover .border-line.border-line-02,
                ' . $addonId . ' .section-5 .s-li.actives .border-line.border-line-02,
                ' . $addonId . ' .section-5 .s-li:hover .border-line.border-line-04,
                ' . $addonId . ' .section-5 .s-li.actives .border-line.border-line-04
                {
                    height: 100%;
                }';
            }
			$css .= $addonId . ' .section-5 .item {';
                if($content_style && ($content_style == "row" || $content_style == "row-reverse")) {
                    $css .= 'height: 100%;
                    justify-content: flex-start !important;';
                }
                $css .= 'text-decoration: none;				
				display: flex;
			    display: -webkit-flex;
				align-items: center;
				justify-content: center;
                position: relative;
                transition: all ease-in-out 300ms;
                z-index: 10;';
                if($content_style) {
                    $css .= 'flex-direction: ' . $content_style . ';';
                } else {
                    $css .= 'flex-direction: column;';
                }
                if($is_bgImg) {
                    if($is_unite_bgColor) {
                        if($bgColor_wz == 1){
                            $css .= 'background-color: ' . $section_tab_bgColor . ' !important;';
                        }
                    }
                }
                if($section_tab_p_md && $is_bgImg) {
                    $css .= 'padding: ' . $section_tab_p_md . ';';
                }
            $css .= '}';
            if($is_bgImg) {
                $css .= $addonId . ' .section-5 .s-li::before {
                    content: "";
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    z-index: 1;
                    left: 0;
                    top: 0;
                    background-image: url();
                    background-size: cover;
                    transition: all ease-in-out 300ms;
                }';
                $css .= $addonId . ' .section-5 .s-li:hover::before,';
                $css .= $addonId . ' .section-5 .s-li.actives::before {';
                    if($settings->is_ngImg_scale) {
                        $css .= 'transform: scale(1.08);';
                    }
                $css .= '}';
            }
            if($settings->animate_bg && $settings->animate_bg != 'none') {
                $css .= $addonId . ' .section-5 .s-li::after {
			        width: 100%;
                    height: 100%;
                    background: ' . $section_tab_bgColor_hover . ';
                    left: 0;
                    top: 0;
                    content: "";
                    position: absolute;
                    z-index: 2;
				    transition: all ease-in-out ' . ($settings->animate_bg_time ?: 300) . 'ms;';
				    if($settings->animate_bg == "middleTopExpansion") {
                        $css .= 'transform: scale(1, 0);';
			        }
				    if($settings->animate_bg == "middleLeftExpansion") {
                        $css .= 'transform: scale(0, 1);';
			        }
				    if($settings->animate_bg == "bottomExpansion") {
                        $css .= 'top: inherit;
                        bottom: 0;
                        height: 0;';
			        }
                $css .= '}';
                $css .= $addonId . ' .section-5 .s-li:hover::after ';
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives::after';
                $css .= '{';
                    if($settings->animate_bg == "middleTopExpansion" || $settings->animate_bg == "middleLeftExpansion") {
                        $css .= 'transform: scale(1, 1);';
                    }
                    if($settings->animate_bg == "bottomExpansion") {
                        $css .= 'height: 100%;';
                    }
                $css .= '}';
            }
            if($is_zhong_label==1){
                $css .= $addonId . ' .section-5 .s-li:hover a';
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives a';
                $css .= '{';
                    if($is_unite_bgColor && !$is_bgImg && $section_tab_bgColor_hover) {
                        if($bgColor_wz==1 && (!$settings->animate_bg || $settings->animate_bg == 'none')){
                            $css .= 'background-color: ' . $section_tab_bgColor_hover . ' !important;';
                        }
                        if($settings->animate_bg && $settings->animate_bg != "none") {
                            $css .= 'background-color: transparent !important;';
                        }
                    }
                    if($is_shadow_hover == 1) {
                        $css .= 'box-shadow: ' . $box_color_hover . ' ' . $box_h_shadow_hover_md . 'px ' . $box_v_shadow_hover_md . 'px ' . $box_blur_hover_md . 'px ' . $box_spread_hover_md . 'px;';
                    }
                    if($is_border_hover == 1 && $animate_border_line == "none") {
                        $css .= 'border: ' . $border_color_hover . ' ' . $border_style_hover . ' ' . $border_width_hover_md . 'px;';
                        $css .= 'border-radius: ' . $border_radius_hover_md . 'px;';
                    }
                    if($is_animate_hover && $animate_menu != "none") {
                        $css .= 'transform: ' . $animate_menu . '(' . $animate_translate . 'px);';
                        $css .= '-webkit-transform: ' . $animate_menu . '(' . $animate_translate . 'px);';
                    }
                $css .= '}';
            }else{
                $css .= $addonId . ' .section-5 .s-li:hover ';
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives ';
                $css .= '{';
                    if($is_unite_bgColor && !$is_bgImg && $section_tab_bgColor_hover) {
                        if($bgColor_wz==1 && (!$settings->animate_bg || $settings->animate_bg == 'none')){
                            $css .= 'background-color: ' . $section_tab_bgColor_hover . ' !important;';
                        }
                        if($settings->animate_bg && $settings->animate_bg != "none") {
                            $css .= 'background-color: transparent !important;';
                        }
                    }
                    if($is_shadow_hover == 1) {
                        $css .= 'box-shadow: ' . $box_color_hover . ' ' . $box_h_shadow_hover_md . 'px ' . $box_v_shadow_hover_md . 'px ' . $box_blur_hover_md . 'px ' . $box_spread_hover_md . 'px;';
                    }
                    if($is_border_hover == 1 && $animate_border_line == "none") {
                        $css .= 'border: ' . $border_color_hover . ' ' . $border_style_hover . ' ' . $border_width_hover_md . 'px;';
                        $css .= 'border-radius: ' . $border_radius_hover_md . 'px;';
                    }
                    if($is_animate_hover && $animate_menu != "none") {
                        $css .= 'transform: ' . $animate_menu . '(' . $animate_translate . 'px);';
                        $css .= '-webkit-transform: ' . $animate_menu . '(' . $animate_translate . 'px);';
                    }
                $css .= '}';
            }

            $css .=$addonId . ' .section-5 .s-li:hover .item,
            ' . $addonId . ' .section-5 .s-li.actives .item {';
                if($is_bgImg) {
                    if($is_unite_bgColor && $section_tab_bgColor_hover) {
                        if($bgColor_wz==1 && (!$settings->animate_bg || $settings->animate_bg == 'none')){
                            $css .= 'background-color: ' . $section_tab_bgColor_hover . ' !important;';
                        }
                        if($settings->animate_bg && $settings->animate_bg != "none") {
                            $css .= 'background-color: transparent !important;';
                        }
                    }
                }
            $css .= '}
            ' . $addonId . ' .section-5 .item .content-icon {';
                if($content_style && ($content_style == "row" || $content_style == "row-reverse")) {
                    if($section_tab_icon_width_md) {
                        $css .= 'width: ' . $section_tab_icon_width_md . 'px;';
                    } else {
                        $css .= 'width: 100%;';
                    }
                    if($section_tab_icon_height_md) {
                        $css .= 'height: ' . $section_tab_icon_height_md . 'px;';
                    } else {
                        $css .= 'height: 100%;';
                    }
                } else {
                    $css .= 'width: 100%;';
                }
                if($section_icon_m_md) {
                    $css .= 'margin: ' . $section_icon_m_md . ';';
                }
                if($settings->is_hidden_icon == 1) {
                    $css .= 'overflow: hidden;';
                }
                $css .= 'text-align: ' . $content_icon_align . ';
            }
            ' . $addonId . ' .section-5 .item .icon-box {
                display: inline-block;
                transition: all ease-in-out ' . ($settings->animate_time ?: '300') .'ms;';
                if($section_tab_icon_width_md) {
                    $css .= 'width: ' . $section_tab_icon_width_md . 'px;';
                } else {
                    $css .= 'width: 100%;';
                }
                if($section_tab_icon_height_md) {
                    $css .= 'height: ' . $section_tab_icon_height_md . 'px;';
                } else {
                    $css .= 'height: 100px;';
                }

                $mg = 0;
                if($section_tab_m_md != ""){
                    $mg = $section_tab_m_md * 2;
                }

                if($icon_zdywz==1){
                    $css .= 'position:absolute;top: '.$icon_top.'px;right:calc(100% / '.$section_tab_m_md.' - '.$mg.'px - '.$icon_left.'px);';
                }

                $css .= 'background: url() no-repeat center;
                background-size: 100%;
            }
            ' . $addonId . ' .section-5 .s-li:hover .icon-box ';
            if($is_item_active == 1) {
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .icon-box';
            }
            $css .= '{';
                $css .= 'width: ' . $section_tab_icon_width_hover_md . 'px;';
                $css .= 'height: ' . $section_tab_icon_height_hover_md . 'px;';
                if($is_animate_hover && $animate_icon != "none") {
                    if($animate_icon=="translateY"){
                        $css .= 'transform: ' . $animate_icon . '(-20px);';
                        $css .= '-webkit-transform: ' . $animate_icon . '(-20px);';
                    }else{
                        $css .= '-webkit-animation-name: ' . $animate_icon . ';';
                        $css .= 'animation-name: ' . $animate_icon . ';';
                        $css .= 'transform: ' . $animate_icon . ';';
                    }

                }
            $css .= '}';
            foreach ($section_tab_item as $key => $tab) {
                if($is_unite_bgColor != 1) {
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"] {';
                    if($tab->item_bg_style == "color") {
                        $css .= 'background-color: ' . $tab->bgColor . ';';
                    }
                    if($tab->item_bg_style == "gradient" && $tab->bgGradient) {
                        $css .= 'background-image: ' . ($tab->bgGradient->type ?: "linear") .'-gradient(';
                        if($tab->bgGradient->type && $tab->bgGradient->type == "radial") {
                            $css .= 'at ' . ($tab->bgGradient->radialPos ?: "center center");
                        } else {
                            $css .= ($tab->bgGradient->deg ?: 0) . 'deg';
                        }
                        $css .= ',
                                    ' . $tab->bgGradient->color . ' ' . ($tab->bgGradient->pos ?: 0) .'%,';
                        $css .= $tab->bgGradient->color2 . ' ' . ($tab->bgGradient->pos2 ?: 100) .'%);';
                    }
                    $css .= '}';
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"]:hover ';
                    if($is_item_active == 1) {
                        $css .= ', ' . $addonId . ' .section-5 .s-li[data-index="' . $key. '"].actives';
                    }
                    $css .= ' {';
                    if($tab->item_bg_style == "color" && $tab->hover_bgColor) {
                        $css .= 'background-color: ' . $tab->hover_bgColor . ';';
                    }
                    if($tab->item_bg_style == "gradient" && $tab->hover_bgGradient) {
                        $css .= 'background-image: ' . ($tab->hover_bgGradient->type ?: "linear") .'-gradient(';
                        if($tab->hover_bgGradient->type && $tab->hover_bgGradient->type == "radial") {
                            $css .= 'at ' . ($tab->hover_bgGradient->radialPos ?: "center center");
                        } else {
                            $css .= ($tab->hover_bgGradient->deg ?: 0) . 'deg';
                        }
                        $css .= ',
                                    ' . $tab->hover_bgGradient->color . ' ' . ($tab->hover_bgGradient->pos ?: 0) .'%,';
                        $css .= $tab->hover_bgGradient->color2 . ' ' . ($tab->hover_bgGradient->pos2 ?: 100) .'%);';
                    }
                    $css .= '}';
                }
                if($tab->icon) {
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"] .item .icon-box {
                        background-image: url(' . $tab->icon . ') !important;
                    }';
                }
                if($tab->hover_icon) {
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"]:hover .icon-box';
                    if($is_item_active == 1) {
                        $css .= ', ' . $addonId . ' .section-5 .s-li[data-index="' . $key. '"].actives .item .icon-box';
                    }
                    $css .= ' {
                        background-image: url(' . $tab->hover_icon . ') !important;
                    }';
                }
                if($custom_border_width) {
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"] {';
                    if($tab->c_border_width) {
                        $css .= 'border-width: ' . $tab->c_border_width->md . ';';
                    }
                    $css .= '}';
                }
                if($is_bgImg) {
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"]::before {';
                        if($tab->bgImg) {
                            $css .= 'background-image: url(' . $tab->bgImg . ');';
                        }
                    $css .= '}';
                    $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"]:hover::before {';
                        if($tab->hover_bgImg) {
                            $css .= 'background-image: url(' . $tab->hover_bgImg . ');';
                        }
                    $css .= '}';
                }
            }
            $css .= $addonId . ' .section-5 .item .info {
                width: 100%;
            }';
            $css .= $addonId . ' .section-5 .item .name {
				color: ' . $section_tab_fontcolor . ';
				background-color: ' . $settings->section_tab_name_bgColor . ';
				background-image: url(' . $settings->section_tab_name_bgImg . ');
				font-size: ' . $section_tab_fontsize_md . 'px;
				line-height: ' . $section_tab_lineHeight_md . 'px;
                width: 100%;
				transition: all ease-in-out 300ms;
                text-align: ' . $content_name_align  . ';';
                if($section_tab_fontW == 1) {
                    $css .= 'font-weight: bold;';
                }
                if($section_name_m_md) {
                    $css .= 'margin: ' . $section_name_m_md . ';';
                }
                if($section_name_p_md) {
                    $css .= 'padding: ' . $section_name_p_md . ';';
                }
                if($is_unite_bgColor) {
                    if($bgColor_wz==2){
                        $css .= 'background-color: ' . $section_tab_bgColor . ' !important;';
                    }
                }
                if($section_text_Height){
                    $css .= 'height:'.$section_text_Height.'px;';
                }
            $css .= '}';
            if($content_name_align && $content_name_align == 'type01') {
                $css .= $addonId . ' .section-5 .s-li:nth-child(2n) .item .name {
                    text-align: right;
                }';
            }
            $section_item_name_b_style = '';
            if($settings->section_item_name_b_bgImg) {
                $section_item_name_b_style .= '
                background-image: url(' . $settings->section_item_name_b_bgImg . ');
                background-size: contain;
                background-repeat: no-repeat;
                text-align: center;
                width: 26px;
                height: 26px;
                line-height: 26px;
                margin-right: 5px;';
            }
            if($section_item_name_b_style) {
                $css .=  $addonId . ' .section-5 .item .name b {
                    display: inline-block;
				    ' . $section_item_name_b_style . '
                }';
            }
            if($section_name_first==1){
                $css .= $addonId . ' .section-5 .item p.name:first-letter{
                    font-size: '.$fontsize_first.'px;
                    position:relative;z-index:1;bottom:0px;line-height:0px;
                }';
            }
	        $css .=$addonId . ' .section-5 .s-li:hover .name';
            if($is_item_active == 1) {
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .name';
            }
            $css .= ' {
				background-color: ' . $settings->section_tab_name_bgColor_hover . ';';
                if($settings->section_tab_name_bgImg_hover) {
                    $css .= 'background-image: url(' . $settings->section_tab_name_bgImg_hover . ');';
                }
                if($section_tab_fontcolor_hover) {
                    $css .= 'color: ' . $section_tab_fontcolor_hover . ';';
                }
                if($section_tab_fontsize_hover_md) {
                    $css .= 'font-size: ' . $section_tab_fontsize_hover_md . 'px;';
                }
                if($is_unite_bgColor) {
                    if($bgColor_wz==2){
                        $css .= 'background-color: ' . $section_tab_bgColor_hover . ' !important;';
                    }
                }
                if($is_animate_hover && $animate_name != "none") {
                    $css .= 'transform: ' . $animate_name . '(' . $animate_name_translate . 'px);';
                    $css .= '-webkit-transform: ' . $animate_name . '(' . $animate_name_translate . 'px);';
                }
            $css .= '}';
            $css .= $addonId . ' .section-5 .item .s-name {
                color: ' . $section_tab_s_name_fontcolor . ';
                font-size: ' . $section_tab_s_name_fontsize_md . 'px;
                line-height: ' . $section_tab_s_name_lineHeight_md . 'px;
                width: 100%;
                transition: all ease-in-out 300ms;
                text-align: ' . $content_s_name_align  . ';';
            $css .= '}';
            $css .=$addonId . ' .section-5 .s-li:hover .s-name';
            if($is_item_active == 1) {
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .s-name';
            }
            $css .= ' {';
                if($section_tab_s_name_fontcolor_hover) {
                    $css .= 'color: ' . $section_tab_s_name_fontcolor_hover . ';';
                }
                if($section_tab_s_name_fontsize_hover_md) {
                    $css .= 'font-size: ' . $section_tab_s_name_fontsize_hover_md . 'px;';
                }
                if($is_animate_hover && $animate_s_name != "none") {
                    $css .= 'transform: ' . $animate_s_name . '(' . $animate_s_name_translate . 'px);';
                    $css .= '-webkit-transform: ' . $animate_s_name . '(' . $animate_s_name_translate . 'px);';
                }
            $css .= '}';
	        $css .= $addonId . ' .section-5 .item .line-box {
	            text-align: ' . $line_border_align  . ';
	        }
	        ' . $addonId . ' .section-5 .item .line {
	            display: inline-block;
				transition: all ease-in-out 300ms;
	            width: ' . $line_border_width_md . 'px;';
                if($line_border_color_style == 'color'){
                    $css .= 'border-bottom: ' . $line_border_color . ' ' . $line_border_style . ' ' . $line_border_height_md . 'px;';
                }
                if($line_border_color_style == 'gradient'){
                    $css .= 'height: ' . $line_border_height_md . 'px;
                            border-radius: ' . $line_border_height_md . 'px;';
                    if($line_border_bgGradient) {
                        $css .= 'background-image: ' . ($line_border_bgGradient->type ?: "linear") .'-gradient(';
                        if($line_border_bgGradient->type && $line_border_bgGradient->type == "radial") {
                            $css .= 'at ' . ($line_border_bgGradient->radialPos ?: "center center");
                        } else {
                            $css .= ($line_border_bgGradient->deg ?: 0) . 'deg';
                        }
                        $css .= ',
                                        ' . $line_border_bgGradient->color . ' ' . ($line_border_bgGradient->pos ?: 0) .'%,';
                        $css .= $line_border_bgGradient->color2 . ' ' . ($line_border_bgGradient->pos2 ?: 100) .'%);';
                    }
                }
                $css .= 'margin-bottom: ' . $section_line_m_md . 'px;
            }
            ' . $addonId . ' .section-5 .s-li:hover .line';
            if($is_item_active == 1) {
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .line';
            }
            $css .= ' {
	            width: ' . $line_border_width_hover_md . 'px;';
                if($line_border_color_style == 'color'){
                    $css .= 'border-bottom: ' . $line_border_color . ' ' . $line_border_style . ' ' . $line_border_height_hover_md . 'px;';
                }
                if($line_border_color_style == 'gradient'){
                    $css .= 'height: ' . $line_border_height_hover_md . 'px;
                                    border-radius: ' . $line_border_height_hover_md . 'px;';
                    if($line_border_bgGradient_hover) {
                        $css .= 'background-image: ' . ($line_border_bgGradient_hover->type ?: "linear") .'-gradient(';
                        if($line_border_bgGradient_hover->type && $line_border_bgGradient_hover->type == "radial") {
                            $css .= 'at ' . ($line_border_bgGradient_hover->radialPos ?: "center center");
                        } else {
                            $css .= ($line_border_bgGradient_hover->deg ?: 0) . 'deg';
                        }
                        $css .= ',
                                                ' . $line_border_bgGradient_hover->color . ' ' . ($line_border_bgGradient_hover->pos ?: 0) .'%,';
                        $css .= $line_border_bgGradient_hover->color2 . ' ' . ($line_border_bgGradient_hover->pos2 ?: 100) .'%);';
                    }
                }
                if($is_animate_hover && $animate_name_line != "none") {
                    $css .= 'transform: ' . $animate_name_line . '(' . $animate_name_line_translate . 'px);';
                    $css .= '-webkit-transform: ' . $animate_name_line . '(' . $animate_name_line_translate . 'px);';
                }
            $css .= '}
			' . $addonId . ' .section-5 .item .desc {
				color: ' . $section_desc_fontcolor . ';
				font-size: ' . $section_desc_fontsize_md . 'px;
				line-height: ' . $section_desc_lineHeight_md . 'px;
                width: 100%;
				transition: all ease-in-out 300ms;
                text-align: ' . $content_desc_align . ';';
                if($section_desc_p_md) {
                    $css .= 'padding: ' . $section_desc_p_md . ';';
                }
                if($section_desc_height_md && $section_desc_height_md != 'auto') {
                    $css .= 'height: ' . $section_desc_height_md . 'px;';
                } else {
                    $css .= 'height: auto;';
                }
                if($is_unite_bgColor) {
                    if($bgColor_wz==3){
                        $css .= 'background-color: ' . $section_tab_bgColor . ' !important;';
                    }
                }
            $css .='}
			' . $addonId . ' .section-5 .s-li:hover .desc';
            if($is_item_active == 1) {
                $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .desc';
            }
            $css .= ' {';
                if($section_desc_fontcolor_hover) {
                    $css .= 'color: ' . $section_desc_fontcolor_hover . ';';
                }
                if($section_desc_fontsize_hover_md) {
                    $css .= 'font-size: ' . $section_desc_fontsize_hover_md . 'px;';
                }
                if($is_unite_bgColor) {
                    if($bgColor_wz==3){
                        $css .= 'background-color: ' . $section_tab_bgColor_hover . ' !important;';
                    }
                }
            $css .= '}
	        /* 更多按钮 */
	        ' . $addonId . ' .section-5 .item .more-box {';
                if($more_icon_m_md) {
                    $css .= 'padding: ' . $more_icon_m_md . ';';
                }
                $css .= '
                display: flex;
                justify-content: ' . $more_icon_align . ';
				transition: all ease-in-out 300ms;';
                if($is_show_more_normal == 1) {
                    $css .= 'opacity: 0;';
                }
            $css .= '}
            ' . $addonId . ' .section-5 .s-li:hover .item .more-box,
            ' . $addonId . ' .section-5 .s-li.actives .item .more-box
            {
                opacity: 1;
            }
	        ' . $addonId . ' .section-5 .item .more-icon {';
                if($more_icon_width_md && $more_icon_width_md != 'auto') {
                    $css .= 'width: ' . $more_icon_width_md . 'px;';
                } else {
                    $css .= 'width: auto;';
                }
            $css .= '}
	        /* 切换 配置样式 */
	        ' . $addonId . ' .swiper-button {
	            width: auto;
	            height: auto;
                top: ' . $swiper_button_top_md . $swiper_button_top_unit .';
	        }
	        ' . $addonId . ' .swiper-button:after {
	            content: "";
                background: url() no-repeat center;
                background-size: cover;
                width: ' . $swiper_button_width_md . 'px;
                height: ' . $swiper_button_height_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev {
	            left: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-prev:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:after {
                background-image: url(' . $swiper_button_prev . ');
            }';
            if($swiper_button_prev_hover) {
                $css .= $addonId . ' .swiper-button-prev:hover:after,' . $addonId . ' .swiper-container-rtl .swiper-button-next:hover:after {
                    background-image: url(' . $swiper_button_prev_hover . ');
                }';
            }
            $css .= $addonId . ' .swiper-button-next {
	            right: ' . $swiper_button_left_md . 'px;
	        }
	        ' . $addonId . ' .swiper-button-next:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url(' . $swiper_button_next . ');
	        }';
            if($swiper_button_next_hover) {
                $css .= $addonId . ' .swiper-button-next:hover:after,' . $addonId . ' .swiper-container-rtl .swiper-button-prev:hover:after {
                    background-image: url(' . $swiper_button_next_hover . ');
                }';
            }
        $css .=
	        '
	        /* 选项卡 */
	        ' . $addonId . ' .menu-tab-box {
			    display: flex;
			    display: -webkit-flex;
			    align-items: center;
			    flex-wrap: wrap;
			    justify-content: ' . $tab_align . ';
			    border-color: ' . $tab_box_border_color . ';
			    border-width: ' . $tab_box_border_width_md . ';
			    border-style: ' . $tab_box_border_style . ';
			    border-radius: ' . $tab_box_border_radius_md . ';';
                if($tab_box_bg_style == "color") {
                    $css .= 'background-color: ' . $tab_box_bgColor . ';';
                }
                if($tab_box_bg_style == "gradient" && $tab_box_bgGradient) {
                    $css .= 'background-image: ' . ($tab_box_bgGradient->type ?: "linear") .'-gradient(';
                    if($tab_box_bgGradient->type && $tab_box_bgGradient->type == "radial") {
                        $css .= 'at ' . ($tab_box_bgGradient->radialPos ?: "center center");
                    } else {
                        $css .= ($tab_box_bgGradient->deg ?: 0) . 'deg';
                    }
                    $css .= ',
                                ' . $tab_box_bgGradient->color . ' ' . ($tab_box_bgGradient->pos ?: 0) .'%,';
                    $css .= $tab_box_bgGradient->color2 . ' ' . ($tab_box_bgGradient->pos2 ?: 100) .'%);';
                }
                $css .= 'margin-bottom: 35px;
			}
			' . $addonId . ' .menu-tab-box .menu-tab-item {
			    font-size: ' . $tab_item_fontsize_md . 'px;
			    line-height: ' . $tab_item_height_md . 'px;';
                if($tab_item_fontW == 1) {
                    $css .= 'font-weight: bold;';
                }
                $css .= 'color: ' . $tab_item_fontcolor . ';
                width: calc(100% / ' . $change_tab_num_md . ');
                text-align: center;
                position: relative;
                cursor: pointer;
                transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .menu-tab-box .menu-tab-item span {
                position: relative;
                display: inline-block;
			}
			' . $addonId . ' .menu-tab-box .menu-tab-item span:after {
			    content: "";
			    width: 100%;
			    height: 0px;
                position: absolute;
                left: 0;
                right: 0;
                bottom: -' . $tab_item_bor_h_active_md . 'px;
                margin: auto;
                transition: all ease-in-out 300ms;
			}
			' . $addonId . ' .menu-tab-box .menu-tab-item:hover {
			    font-size: ' . $tab_item_fontsize_hover_md . 'px;';
                if($tab_item_fontW_hover == 1) {
                    $css .= 'font-weight: bold;';
                }
                $css .= 'color: ' . $tab_item_fontcolor_hover . ';
            }
			' . $addonId . ' .menu-tab-box .menu-tab-item.actives {
			    font-size: ' . $tab_item_fontsize_active_md . 'px;';
                if($tab_item_fontW_active == 1) {
                    $css .= 'font-weight: bold;';
                }
                $css .= 'color: ' . $tab_item_fontcolor_active . ';
			}
			' . $addonId . ' .menu-tab-box .menu-tab-item.actives span:after {
			    height: ' . $tab_item_bor_h_active_md . 'px;
                background: ' . $tab_item_bor_c_active . ';
			}
			/* 内容部分样式 */
			' . $addonId . ' .section-content {
			    display: flex;
			    flex-direction: ' . $item_content_style . ';
			    align-items: center;
                justify-content: space-between;
                background-color: ' . $item_content_bg . ';
                ';
                if($item_content_p_md) {
                    $css .= 'padding: ' . $item_content_p_md . ';';
                }else {
                    $css .= 'padding: 95px 0;';
                }
            $css .= '}
			' . $addonId . ' .section-content-box.hidden {
			    display: none;
			}
			' . $addonId . ' .section-content .text-wrap {
			    flex: 1;
			}
			' . $addonId . ' .section-content .content-line {
			    ' . $lineW . ': ' . $item_content_between_md . 'px;
			}
			' . $addonId . ' .section-content .text-wrap .text-title {
			    font-size: ' . $item_content_t_fontsize_md . 'px;
			    line-height: ' . $item_content_t_lineHeight_md . 'px;
                color: ' . $item_content_t_fontcolor . ';
                margin-bottom: ' . $item_content_t_mb_md . 'px;
                text-align: ' . $settings->item_content_title_align . ';';
                if($item_content_t_fontW == 1) {
                    $css .= 'font-weight: bold;';
                }
			$css .= '}
			' . $addonId . ' .section-content .text-wrap .text-desc {
                font-size: ' . $item_content_d_fontsize_md . 'px;
                color: ' . $item_content_d_fontcolor . ';
                line-height: ' . $item_content_d_lineHeight_md . 'px;
			}
			' . $addonId . ' .section-content .image-wrap {
			    width: ' . $item_content_image_width_md . ';
                height: ' . $item_content_image_height_md . ';
                border-radius: ' . $item_content_img_border_radius_md . 'px;
                overflow: hidden;
			}
			' . $addonId . ' .section-content .image-wrap img {
			    width: 100%;
			    height: 100%;
			    object-fit: ' . $item_image_fit . ';
			}
			@media (min-width: 768px) and (max-width: 991px) {';

                if($is_zhong_label==1){
                    $css .= $addonId . ' .section-5 .s-ul>li:after{
                        width: '.$jiantou_width.'px;
                        height: '.$jiantou_height.'px;
                        position: absolute;
                        left: '.$jiantou_right.'px;
                        top:'.$jiantou_top.'px;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                    }
                    ';
                    if($jiantou_nth_sm!=0){

                        $css .=$addonId . ' .section-5 .s-li:nth-child('.$jiantou_nth_sm.'):after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: '.$jiantou_nthright.'px;
                            top:'.$jiantou_nthtop.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                        }
                        '.$addonId . ' .section-5, ' . $addonId . ' .section-5 .s-ul {
                            display:block;
                            flex-wrap: inherit;
                        }
                        ' . $addonId . ' .section-5 .s-ul li{float:left;}
                        ' . $addonId . ' .section-5 .s-ul .s-li:nth-child('.$jiantou_nth_sm.') ~ .s-li {float:right;}';
                        $css .=$addonId . ' .section-5 .s-li:nth-of-type('.$jiantou_nth_sm.') ~ .s-li:after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: -15px;
                            top:'.$jiantou_top.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                        }
                        ';

                    }

                    $css .= $addonId . ' .section-5 .s-ul li:last-child:after{
                        width: 0px;
                        height: 0px;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                    }';

                }

                $css .= $addonId . ' .section-5 {';
                    if($is_swiper == 1 && $swiper_container_p_sm) {
                        $css .= 'padding: ' . $swiper_container_p_sm . ';';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .s-li {';
                    $mg = 0;
                    if($section_tab_m_sm != ""){
                        $mg = $section_tab_m_sm * 2;
                    }
                    $css .= 'width: calc(100% / ' . $section_tab_num_sm . ' - ' . $mg .'px);';
                    if($section_tab_height_sm && $section_tab_height_sm != 'auto') {
                        $css .= 'height: ' . $section_tab_height_sm . 'px;';
                    }else {
                        $css .= 'height: auto;';
                    }
                    if($section_tab_m_sm && $is_swiper == 0) {
                        $css .= 'margin: ' . $section_tab_m_sm . 'px;';
                    }
                    if($section_tab_p_sm && !$is_bgImg) {
                        $css .= 'padding: ' . $section_tab_p_sm . ';';
                    }
                    if($is_shadow == 1) {
                        $css .= 'box-shadow: ' . $box_color . ' ' . $box_h_shadow_sm . 'px ' . $box_v_shadow_sm . 'px ' . $box_blur_sm . 'px ' . $box_spread_sm . 'px;';
                    }
                    if($is_border == 1) {
                        $css .= 'border: ' . $border_color . ' ' . $border_style . ' ' . $border_width_sm . 'px;';
                        $css .= 'border-radius: ' . $border_radius_sm . 'px;';
                    }
                $css .= '}';
                if($is_border == 1 && $animate_border_line != 'none') {
                    $css .= $addonId . ' .section-5 .s-li .border-line.border-line-01,
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-03
                    {
                        height: ' . $border_width_hover_sm . 'px;
                    }
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-02,
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-04
                    {
                        width: ' . $border_width_hover_sm . 'px;
                    }';
                }
                $css .= $addonId . ' .section-5 .item {';
                if($section_tab_p_sm && $is_bgImg) {
                    $css .= 'padding: ' . $section_tab_p_sm . ';';
                }
                $css .= '}';
                if($custom_border_width) {
                    foreach ($section_tab_item as $key => $tab) {
                        $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"] {';
                        if($tab->c_border_width) {
                            $css .= 'border-width: ' . $tab->c_border_width->sm . ';';
                        }
                        $css .= '}';
                    }
                }
                $css .= $addonId . ' .section-5 .s-li:hover';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives';
                }
                $css .= ' {';
                    if($is_shadow_hover == 1) {
                        $css .= 'box-shadow: ' . $box_color_hover . ' ' . $box_h_shadow_hover_sm . 'px ' . $box_v_shadow_hover_sm . 'px ' . $box_blur_hover_sm . 'px ' . $box_spread_hover_sm . 'px;';
                    }
                    if($is_border_hover == 1) {
                        $css .= 'border: ' . $border_color_hover . ' ' . $border_style_hover . ' ' . $border_width_hover_sm . 'px;';
                        $css .= 'border-radius: ' . $border_radius_hover_sm . 'px;';
                    }
                $css .= '}
			    ' . $addonId . ' .section-5 .item .content-icon {';
                    if($content_style && ($content_style == "row" || $content_style == "row-reverse")) {
                        if($section_tab_icon_width_sm) {
                            $css .= 'width: ' . $section_tab_icon_width_sm . 'px;';
                        } else {
                            $css .= 'width: 100%;';
                        }
                        if($section_tab_icon_height_sm) {
                            $css .= 'height: ' . $section_tab_icon_height_sm . 'px;';
                        } else {
                            $css .= 'height: 100%;';
                        }
                    } else {
                        $css .= 'width: 100%;';
                    }
                    if($section_icon_m_sm) {
                        $css .= 'margin: ' . $section_icon_m_sm . ';';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .item .icon-box {';
                    if($section_tab_icon_width_sm) {
                        $css .= 'width: ' . $section_tab_icon_width_sm . 'px;';
                    } else {
                        $css .= 'width: 100%;';
                    }
                    if($section_tab_icon_height_sm) {
                        $css .= 'height: ' . $section_tab_icon_height_sm . 'px;';
                    } else {
                        $css .= 'height: 100px;';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .s-li:hover .icon-box';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .icon-box';
                }
                $css .= ' {
                    width: ' . $section_tab_icon_width_hover_sm . 'px;
                    height: ' . $section_tab_icon_height_hover_sm . 'px;
                }
				' . $addonId . ' .section-5 .item .name {
				    font-size: ' . $section_tab_fontsize_sm . 'px;
					line-height: ' . $section_tab_lineHeight_sm . 'px;';
                    if($section_name_m_sm) {
                        $css .= 'margin: ' . $section_name_m_sm . ';';
                    }
                    if($section_name_p_sm) {
                        $css .= 'padding: ' . $section_name_p_sm . ';';
                    }
                $css .= '}
	            ' . $addonId . ' .section-5 .s-li:hover .name ';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .name';
                }
                $css .= '{';
                    if($section_tab_fontsize_hover_sm) {
                        $css .= 'font-size: ' . $section_tab_fontsize_hover_sm . 'px;';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .item .s-name {
				    font-size: ' . $section_tab_s_name_fontsize_sm . 'px;
					line-height: ' . $section_tab_s_name_lineHeight_sm . 'px;';
                $css .= '}
                ' . $addonId . ' .section-5 .s-li:hover .s-name ';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .name';
                }
                $css .= '{';
                    if($section_tab_s_name_fontsize_hover_sm) {
                        $css .= 'font-size: ' . $section_tab_s_name_fontsize_hover_sm . 'px;';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .item .line {
                    width: ' . $line_border_width_sm . 'px;';
                    if($line_border_color_style == 'color') {
                        $css .= 'border-bottom: ' . $line_border_color . ' ' . $line_border_style . ' ' . $line_border_height_sm . 'px;';
                    }
                    if($line_border_color_style == 'gradient') {
                        $css .= 'height:' . $line_border_height_sm . 'px;';
                    }
                    $css .= 'margin-bottom: ' . $section_line_m_sm . 'px;
                }
                ' . $addonId . ' .section-5 .s-li:hover .line';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .line';
                }
                $css .= ' {
                    width: ' . $line_border_width_hover_sm . 'px;
                    ';
                    if($line_border_color_style == 'color') {
                        $css .= 'border-bottom: ' . $line_border_color_hover . ' ' . $line_border_style_hover . ' ' . $line_border_height_hover_sm . 'px;';
                    }
                    if($line_border_color_style == 'gradient') {
                        $css .= 'height:' . $line_border_height_hover_sm . 'px;';
                    }
                    $css .= '
                }
				' . $addonId . ' .section-5 .item .desc {
                    font-size: ' . $section_desc_fontsize_sm . 'px;
					line-height: ' . $section_desc_lineHeight_sm . 'px;';
                    if($section_desc_p_sm) {
                        $css .= 'padding: ' . $section_desc_p_sm . ';';
                    }
                    if($section_desc_height_sm && $section_desc_height_sm != 'auto') {
                        $css .= 'height: ' . $section_desc_height_sm . 'px;';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .s-li:hover .desc';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .desc';
                }
                $css .= ' {';
                    if($section_desc_fontsize_hover_sm) {
                        $css .= 'font-size: ' . $section_desc_fontsize_hover_sm . 'px;';
                    }
                $css .= '}
		        /* 更多按钮 */
                ' . $addonId . ' .section-5 .item .more-box {';
                    if($more_icon_m_sm) {
                        $css .= 'padding: ' . $more_icon_m_sm . ';';
                    }
                $css .= '}
                ' . $addonId . ' .section-5 .item .more-icon {';
                    if($more_icon_width_sm && $more_icon_width_sm != 'auto') {
                        $css .= 'width: ' . $more_icon_width_sm . 'px;';
                    }
                $css .= '}
		        /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_sm .  $swiper_button_top_unit .';
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_sm . 'px;
                    height: ' . $swiper_button_height_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_sm . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_sm . 'px;
                }
                /* 选项卡 */
                ' . $addonId . ' .menu-tab-box {
                    border-width: ' . $tab_box_border_width_sm . ';
                    border-radius: ' . $tab_box_border_radius_sm . ';
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item {
			        font-size: ' . $tab_item_fontsize_sm . 'px;
			        line-height: ' . $tab_item_height_sm . 'px;;
                    width: calc(100% / ' . $change_tab_num_sm . ');
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item span:after {
                    bottom: -' . $tab_item_bor_h_active_sm . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item:hover {
                    font-size: ' . $tab_item_fontsize_hover_sm . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item.actives {
                    font-size: ' . $tab_item_fontsize_active_sm . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item.actives span:after {
                    height: ' . $tab_item_bor_h_active_sm . 'px;
                }
                /* 内容部分样式 */
                ' . $addonId . ' .section-content {';
                    if($item_content_p_sm) {
                        $css .= 'padding: ' . $item_content_p_sm . ';';
                    }
                $css .= '}
                ' . $addonId . ' .section-content .content-line {
                    ' . $lineW . ': ' . $item_content_between_sm . 'px;
                }
                ' . $addonId . ' .section-content .text-wrap .text-title {
                    font-size: ' . $item_content_t_fontsize_sm . 'px;
                    line-height: ' . $item_content_t_lineHeight_sm . 'px;
                    margin-bottom: ' . $item_content_t_mb_sm . 'px;';
                $css .= '}
                ' . $addonId . ' .section-content .text-wrap .text-desc {
                    font-size: ' . $item_content_d_fontsize_sm . 'px;
                    line-height: ' . $item_content_d_lineHeight_sm . 'px;
                }
                ' . $addonId . ' .section-content .image-wrap {
                    width: ' . $item_content_image_width_sm . ';
                    height: ' . $item_content_image_height_sm . ';
                    border-radius: ' . $item_content_img_border_radius_sm . 'px;
                }
			}
			@media (max-width: 767px) {';
                $css .= $addonId . ' .section-5 {';
                    if($is_swiper == 1 && $swiper_container_p_xs) {
                        $css .= 'padding: ' . $swiper_container_p_xs . ';';
                    }
                $css .= '}
			    ' . $addonId . ' .section-5 .s-li {';
                    $mg = 0;
                    if($section_tab_m_xs != ""){
                        $mg = $section_tab_m_xs * 2;
                    }
                    $css .= 'width: calc(100% / ' . $section_tab_num_xs . ' - ' . $mg .'px);';
                    if($section_tab_height_xs && $section_tab_height_xs != 'auto') {
                        $css .= 'height: ' . $section_tab_height_xs . 'px;';
                    }else {
                        $css .= 'height: auto;';
                    }
                    if($section_tab_m_xs && $is_swiper == 0) {
                        $css .= 'margin: ' . $section_tab_m_xs . 'px;';
                    }
                    if($section_tab_p_xs && !$is_bgImg) {
                        $css .= 'padding: ' . $section_tab_p_xs . ';';
                    }
                    if($is_shadow == 1) {
                        $css .= 'box-shadow: ' . $box_color . ' ' . $box_h_shadow_xs . 'px ' . $box_v_shadow_xs . 'px ' . $box_blur_xs . 'px ' . $box_spread_xs . 'px;';
                    }
                    if($is_border == 1) {
                        $css .= 'border: ' . $border_color . ' ' . $border_style . ' ' . $border_width_xs . 'px;';
                        $css .= 'border-radius: ' . $border_radius_xs . 'px;';
                    }
                $css .= '}';
                if($is_border == 1 && $animate_border_line != 'none') {
                    $css .= $addonId . ' .section-5 .s-li .border-line.border-line-01,
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-03
                    {
                        height: ' . $border_width_hover_xs . 'px;
                    }
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-02,
                    ' . $addonId . ' .section-5 .s-li .border-line.border-line-04
                    {
                        width: ' . $border_width_hover_xs . 'px;
                    }';
                }
                $css .= $addonId . ' .section-5 .item {';
                    if($section_tab_p_xs && $is_bgImg) {
                        $css .= 'padding: ' . $section_tab_p_xs . ';';
                    }
                $css .= '}';
                if($custom_border_width) {
                    foreach ($section_tab_item as $key => $tab) {
                        $css .= $addonId . ' .section-5 .s-li[data-index="' . $key. '"] {';
                        if($tab->c_border_width) {
                            $css .= 'border-width: ' . $tab->c_border_width->xs . ';';
                        }
                        $css .= '}';
                    }
                }
                $css .= $addonId . ' .section-5 .s-li:hover';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives';
                }
                $css .= ' {';
                    if($is_shadow_hover == 1) {
                        $css .= 'box-shadow: ' . $box_color_hover . ' ' . $box_h_shadow_hover_xs . 'px ' . $box_v_shadow_hover_xs . 'px ' . $box_blur_hover_xs . 'px ' . $box_spread_hover_xs . 'px;';
                    }
                    if($is_border_hover == 1) {
                        $css .= 'border: ' . $border_color_hover . ' ' . $border_style_hover . ' ' . $border_width_hover_xs . 'px;';
                        $css .= 'border-radius: ' . $border_radius_hover_xs . 'px;';
                    }
                $css .= '}
			    ' . $addonId . ' .section-5 .item .content-icon {';
                    if($content_style && ($content_style == "row" || $content_style == "row-reverse")) {
                        if($section_tab_icon_width_xs) {
                            $css .= 'width: ' . $section_tab_icon_width_xs . 'px;';
                        } else {
                            $css .= 'width: 100%;';
                        }
                        if($section_tab_icon_height_xs) {
                            $css .= 'height: ' . $section_tab_icon_height_xs . 'px;';
                        } else {
                            $css .= 'height: 100%;';
                        }
                    } else {
                        $css .= 'width: 100%;';
                    }
                    if($section_icon_m_xs) {
                        $css .= 'margin: ' . $section_icon_m_xs . ';';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .item .icon-box {';
                    if($section_tab_icon_width_xs) {
                        $css .= 'width: ' . $section_tab_icon_width_xs . 'px;';
                    } else {
                        $css .= 'width: 100%;';
                    }
                    if($section_tab_icon_height_xs) {
                        $css .= 'height: ' . $section_tab_icon_height_xs . 'px;';
                    } else {
                        $css .= 'height: 100px;';
                    }
                $css .= '}
			    ' . $addonId . ' .section-5 .s-li:hover .icon-box';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .icon-box';
                }
                $css .= ' {
                    width: ' . $section_tab_icon_width_hover_xs . 'px;
                    height: ' . $section_tab_icon_height_hover_xs . 'px;
                }
				' . $addonId . ' .section-5 .item .name {
				    font-size: ' . $section_tab_fontsize_xs . 'px;
					line-height: ' . $section_tab_lineHeight_xs . 'px;';
                    if($section_name_m_xs) {
                        $css .= 'margin: ' . $section_name_m_xs . ';';
                    }
                    if($section_name_p_xs) {
                        $css .= 'padding: ' . $section_name_p_xs . ';';
                    }
                $css .= '}
	            ' . $addonId . ' .section-5 .s-li:hover .name';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .name';
                }
                $css .= ' {';
                    if($section_tab_fontsize_hover_xs) {
                        $css .= 'font-size: ' . $section_tab_fontsize_hover_xs . 'px;';
                    }
                $css .= '}
				' . $addonId . ' .section-5 .item .s-name {
				    font-size: ' . $section_tab_s_name_fontsize_xs . 'px;
					line-height: ' . $section_tab_s_name_lineHeight_xs . 'px;';
                $css .= '}
                ' . $addonId . ' .section-5 .s-li:hover .s-name ';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .name';
                }
                $css .= '{';
                    if($section_tab_s_name_fontsize_hover_xs) {
                        $css .= 'font-size: ' . $section_tab_s_name_fontsize_hover_xs . 'px;';
                    }
                $css .= '}
			    ' . $addonId . ' .section-5 .item .line {
                    width: ' . $line_border_width_xs . 'px;';
                    if($line_border_color_style == 'color') {
                        $css .= 'border-bottom: ' . $line_border_color . ' ' . $line_border_style . ' ' . $line_border_height_xs . 'px;';
                    }
                    if($line_border_color_style == 'gradient') {
                        $css .= 'height:' . $line_border_height_xs . 'px;';
                    }
                    $css .= 'margin-bottom: ' . $section_line_m_xs . 'px;
                }
                ' . $addonId . ' .section-5 .s-li:hover .line';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .line';
                }
                $css .= ' {
                    width: ' . $line_border_width_hover_xs . 'px;
                    ';
                    if($line_border_color_style == 'color') {
                        $css .= 'border-bottom: ' . $line_border_color_hover . ' ' . $line_border_style_hover . ' ' . $line_border_height_hover_xs . 'px;';
                    }
                    if($line_border_color_style == 'gradient') {
                        $css .= 'height:' . $line_border_height_hover_xs . 'px;';
                    }
                    $css .= '
                }
                ' . $addonId . ' .section-5 .item .desc {
                    font-size: ' . $section_desc_fontsize_xs . 'px;
                    line-height: ' . $section_desc_lineHeight_xs . 'px;';
                    if($section_desc_p_xs) {
                        $css .= 'padding: ' . $section_desc_p_xs . ';';
                    }
                    if($section_desc_height_xs && $section_desc_height_xs != 'auto') {
                        $css .= 'height: ' . $section_desc_height_xs . 'px;';
                    }
                $css .= '}
                ' . $addonId . ' .section-5 .s-li:hover .desc';
                if($is_item_active == 1) {
                    $css .= ', ' . $addonId . ' .section-5 .s-li.actives .item .desc';
                }
                $css .= ' {';
                    if($section_desc_fontsize_hover_xs) {
                        $css .= 'font-size: ' . $section_desc_fontsize_hover_xs . 'px;';
                    }
                $css .= '}
                /* 更多按钮 */
                ' . $addonId . ' .section-5 .item .more-box {';
                    if($more_icon_m_xs) {
                        $css .= 'padding: ' . $more_icon_m_xs . ';';
                    }
                $css .= '}
                ' . $addonId . ' .section-5 .item .more-icon {';
                    if($more_icon_width_xs && $more_icon_width_xs != 'auto') {
                        $css .= 'width: ' . $more_icon_width_xs . 'px;';
                    }
                $css .= '}
			    /* 切换 配置样式 */
                ' . $addonId . ' .swiper-button {
                    top: ' . $swiper_button_top_xs .  $swiper_button_top_unit .';
                }
                ' . $addonId . ' .swiper-button:after {
                    width: ' . $swiper_button_width_xs . 'px;
                    height: ' . $swiper_button_height_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-prev {
                    left: ' . $swiper_button_left_xs . 'px;
                }
                ' . $addonId . ' .swiper-button-next {
                    right: ' . $swiper_button_left_xs . 'px;
                }
                /* 选项卡 */
                ' . $addonId . ' .menu-tab-box {
                    border-width: ' . $tab_box_border_width_xs . ';
                    border-radius: ' . $tab_box_border_radius_xs . ';
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item {
			        font-size: ' . $tab_item_fontsize_xs . 'px;
			        line-height: ' . $tab_item_height_xs . 'px;;
                    width: calc(100% / ' . $change_tab_num_xs . ');
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item span:after {
                    bottom: -' . $tab_item_bor_h_active_xs . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item:hover {
                    font-size: ' . $tab_item_fontsize_hover_xs . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item.actives {
                    font-size: ' . $tab_item_fontsize_active_xs . 'px;
                }
                ' . $addonId . ' .menu-tab-box .menu-tab-item.actives span:after {
                    height: ' . $tab_item_bor_h_active_xs . 'px;
                }
                /* 内容部分样式 */
                ' . $addonId . ' .section-content {
                    flex-direction: ' . $item_content_style_xs . ';';
                    if($item_content_p_xs) {
                        $css .= 'padding: ' . $item_content_p_xs . ';';
                    }
                $css .= '}
                ' . $addonId . ' .section-content .content-line {
                    ' . $lineW_xs . ': ' . $item_content_between_xs . 'px;
                }
                ' . $addonId . ' .section-content .text-wrap .text-title {
                    font-size: ' . $item_content_t_fontsize_xs . 'px;
                    line-height: ' . $item_content_t_lineHeight_xs . 'px;
                    margin-bottom: ' . $item_content_t_mb_xs . 'px;';
                $css .= '}
                ' . $addonId . ' .section-content .text-wrap .text-desc {
                    font-size: ' . $item_content_d_fontsize_xs . 'px;
                    line-height: ' . $item_content_d_lineHeight_xs . 'px;
                }
                ' . $addonId . ' .section-content .image-wrap {
                    width: ' . $item_content_image_width_xs . ';
                    height: ' . $item_content_image_height_xs . ';
                    border-radius: ' . $item_content_img_border_radius_xs . 'px;
                }';

                if($is_zhong_label==1){
                    $css .= $addonId . ' .section-5 .s-ul>li:after{
                        width: '.$jiantou_width.'px;
                        height: '.$jiantou_height.'px;
                        position: absolute;
                        left: '.$jiantou_right.'px;
                        top:'.$jiantou_top.'px;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                    }
                    ';
                    if($jiantou_nth_xs!=0){

                        $css .=$addonId . ' .section-5 .s-li:nth-child('.$jiantou_nth_xs.'):after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: '.$jiantou_nthright.'px;
                            top:'.$jiantou_nthtop.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                        }

                        '.$addonId . ' .section-5, ' . $addonId . ' .section-5 .s-ul {
                            display:block;
                            flex-wrap: inherit;
                        }
                        ' . $addonId . ' .section-5 .s-ul li{float:left;}
                        ' . $addonId . ' .section-5 .s-ul .s-li:nth-child('.$jiantou_nth_xs.') ~ .s-li {float:right;}';
                        $css .=$addonId . ' .section-5 .s-li:nth-of-type('.$jiantou_nth_xs.') ~ .s-li:after{
                            width: '.$jiantou_width.'px;
                            height: '.$jiantou_height.'px;
                            position: absolute;
                            left: -15px;
                            top:'.$jiantou_top.'px;
                            display: block;
                            content:"";
                            background:url("'.$jintou_icon.'")no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                        }

                        ';
                    }

                    $css .= $addonId . ' .section-5 .s-ul>li:last-child:after{
                        width: 0px!important;
                        height: 0px!important;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url("'.$jintou_icon.'")no-repeat;
                        transition: all .6s;
                    }';

                }

			$css .='}
			';

        return $css;
    }

    public static function getTemplate()
    {
        $output = '
        <!--<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,请预览查看效果</div>

        </div>-->
        <#
		var addonId = "#jwpf-addon-"+data.id;
		// 切换item
		var section_tab_item = data.section_tab_item || array();

		// 是否开启swiper
		var is_swiper = data.is_swiper || 0;
		// 是否开启切换按钮
		var is_swiper_button = data.is_swiper_button || 0;
		// 轮播框内边距
		var swiper_container_p = data.swiper_container_p || 0;
		// 上翻页按钮
		var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png";
		// 下翻页按钮
		var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png";
		// 切换按钮宽度
		var swiper_button_width = data.swiper_button_width || 24;
		// 切换按钮高度
		var swiper_button_height = data.swiper_button_height || 24;
		// 切换按钮上边距（百分比）
		var swiper_button_top = data.swiper_button_top || 48;
		// 切换按钮 上边距单位
		var swiper_button_top_unit = data.is_swiper_button_top_px && data.is_swiper_button_top_px == 1 ? "px" : "%";
		// 切换按钮两侧边距（px）
		var swiper_button_left = data.swiper_button_left || 10;

		// 菜单一行显示个数
		var section_tab_num = data.section_tab_num || 5;
		// 菜单高度
		var section_tab_height = data.section_tab_height || "auto";
		// 菜单外边距
		var section_tab_m = data.section_tab_m || 0;
		// 下划线下边距
		var section_line_m = data.section_line_m || 0;
		// 菜单内边距
		var section_tab_p = data.section_tab_p || 0;
		// 名称外边距
		var section_name_m = data.section_name_m;
		// 名称内边距
		var section_name_p = data.section_name_p;
		// 图标外边距
		var section_icon_m = data.section_icon_m;
		// 简介内边距
		var section_desc_p = data.section_desc_p;
		// 简介高度
		var section_desc_height = data.section_desc_height;

		// 排列方式
		var content_style = data.content_style || "column";
		// 图标对齐方式
		var content_icon_align = data.content_icon_align || "center";
		// 名称对齐方式
		var content_name_align = data.content_name_align || "center";
		// 副标题对齐方式
		var content_s_name_align = data.content_s_name_align || "center";
		// 简介对齐方式
		var content_desc_align = data.content_desc_align || "center";

		// 是否统一背景
		var is_unite_bgColor = data.is_unite_bgColor || 0;
        var bgColor_wz = data.bgColor_wz || 1;

		// 是否显示名称
		var is_show_name = data.is_show_name || 0;
		// 是否显示名称下划线
		var is_show_name_line = data.is_show_name_line || 0;
		// 是否显示简介
		var is_show_desc = data.is_show_desc || 0;
		// 是否显示更多按钮
		var is_show_more = data.is_show_more || 0;
		// 更多按钮
		var more_icon = data.more_icon || "https://oss.lcweb01.cn/joomla/20210817/cd03035e0bdb7530651387167c7645ed.png";
		// 更多按钮宽度
		var more_icon_width = data.more_icon_width;
		// 更多按钮外边距
		var more_icon_m = data.more_icon_m;
		// 更多按钮对齐方式
		var more_icon_align = data.more_icon_align || "flex-start";


		/* ---- 正常 ---- */
		// 名称文字大小
		var section_tab_fontsize = data.section_tab_fontsize || 18;
		// 名称文字行高
		var section_tab_lineHeight = data.section_tab_lineHeight || 32;
		// 名称文字颜色
		var section_tab_fontcolor = data.section_tab_fontcolor || "#ffffff";
		// 名称文字加粗
		var section_tab_fontW = data.section_tab_fontW || 0;
		// 副标题文字大小
		var section_tab_s_name_fontsize = data.section_tab_s_name_fontsize || 14;
		// 副标题文字行高
		var section_tab_s_name_lineHeight = data.section_tab_s_name_lineHeight || 32;
		// 副标题文字颜色
		var section_tab_s_name_fontcolor = data.section_tab_s_name_fontcolor || "#ffffff";
		// 简介文字大小
		var section_desc_fontsize = data.section_desc_fontsize || 16;
		// 简介文字行高
		var section_desc_lineHeight = data.section_desc_lineHeight || 32;
		// 简介文字颜色
		var section_desc_fontcolor = data.section_desc_fontcolor || "#ffffff";
		// 图标宽度
		var section_tab_icon_width = data.section_tab_icon_width;
		// 图标高度
		var section_tab_icon_height = data.section_tab_icon_height;

		// 下划线宽度
		var line_border_width = data.line_border_width || 1;
		// 下划线高度
		var line_border_height = data.line_border_height || 1;
		// 下划线对齐方式
		var line_border_align = data.line_border_align || "center";
		// 下划线背景样式
		var line_border_color_style = data.line_border_color_style || "color";
		// 下划线样式
		var line_border_style = data.line_border_style || "none";
		// 下划线颜色
		var line_border_color = data.line_border_color || "#ffffff";
		// 下划线渐变色
		var line_border_bgGradient = data.line_border_bgGradient || null;

		// 统一背景颜色
		var section_tab_bgColor = data.section_tab_bgColor || "#ffffff";

		// 开启投影
		var is_shadow = data.is_shadow || 0;
		// 投影颜色
		var box_color = data.box_color || "#ffffff";
		// 水平偏移
		var box_h_shadow = data.box_h_shadow || 1;
		// 垂直偏移
		var box_v_shadow = data.box_v_shadow || 1;
		// 模糊
		var box_blur = data.box_blur || 1;
		// 扩展
		var box_spread = data.box_spread || 1;

		// 使用边框
		var is_border = data.is_border || 0;
		// 边框颜色
		var border_color = data.border_color || "#ffffff";
		// 边框宽度
		var border_width = data.border_width || 1;
		// 自定义边框宽度
		var custom_border_width = data.custom_border_width || 0;
		// 边框宽度
		// var c_border_width = data.c_border_width || 0;
		// 边框样式
		var border_style = data.border_style || "none";
		// 边框半径
		var border_radius = data.border_radius || 10;

		/* ---- 滑过 ---- */
		// 名称文字大小
		var section_tab_fontsize_hover = data.section_tab_fontsize_hover;
		// 名称文字颜色
		var section_tab_fontcolor_hover = data.section_tab_fontcolor_hover;
		// 副标题文字大小
		var section_tab_s_name_fontsize_hover = data.section_tab_s_name_fontsize_hover;
		// 副标题文字颜色
		var section_tab_s_name_fontcolor_hover = data.section_tab_s_name_fontcolor_hover;
		// 简介文字大小
		var section_desc_fontsize_hover = data.section_desc_fontsize_hover;
		// 简介文字颜色
		var section_desc_fontcolor_hover = data.section_desc_fontcolor_hover;
		// 图标宽度
		var section_tab_icon_width_hover = data.section_tab_icon_width_hover;
		// 图标高度
		var section_tab_icon_height_hover = data.section_tab_icon_height_hover;

		// 下划线宽度
		var line_border_width_hover = data.line_border_width_hover || 1;
		// 下划线高度
		var line_border_height_hover = data.line_border_height_hover || 1;
		// 下划线样式
		var line_border_style_hover = data.line_border_style_hover || "none";
		// 下划线颜色
		var line_border_color_hover = data.line_border_color_hover || "#ffffff";
		// 下划线渐变色
		var line_border_bgGradient_hover = data.line_border_bgGradient_hover || null;

		// 统一背景颜色
		var section_tab_bgColor_hover = data.section_tab_bgColor_hover;

		// 开启投影
		var is_shadow_hover = data.is_shadow_hover || 0;
		// 投影颜色
		var box_color_hover = data.box_color_hover;
		// 水平偏移
		var box_h_shadow_hover = data.box_h_shadow_hover;
		// 垂直偏移
		var box_v_shadow_hover = data.box_v_shadow_hover;
		// 模糊
		var box_blur_hover = data.box_blur_hover;
		// 扩展
		var box_spread_hover = data.box_spread_hover;

		// 使用边框
		var is_border_hover = data.is_border_hover || 0;
		// 边框颜色
		var border_color_hover = data.border_color_hover;
		// 边框宽度
		var border_width_hover = data.border_width_hover;
		// 边框样式
		var border_style_hover = data.border_style_hover;
		// 边框半径
		var border_radius_hover = data.border_radius_hover;

		// 使用动画
		var is_animate_hover = data.is_animate_hover || 0;
		// 图标动画效果
		var animate_icon = data.animate_icon || "pulse";
		// 整个item动画效果
		var animate_menu = data.animate_menu || "translateY";
		// 移动距离
		var animate_translate = data.animate_translate || -12;

		// 选项卡相关
		var is_tab = data.is_tab || 0;
		// 选项卡列表
		var change_tab_item = data.change_tab_item || [];
		// 选项卡布局
		var tab_theme = data.tab_theme || "type01";
		// 选项卡一行显示个数
		var change_tab_num = data.change_tab_num || 4;
		// 选项卡高度
		var tab_item_height = data.tab_item_height || 67;
		// 选项卡对齐方式
		var tab_align = data.tab_align || "flex-start";

		// 边框颜色
		var tab_box_border_color = data.tab_box_border_color || "";
		// 边框宽度
		var tab_box_border_width = data.tab_box_border_width || 0;
		// 边框样式
		var tab_box_border_style = data.tab_box_border_style || "none";
		// 边框半径
		var tab_box_border_radius = data.tab_box_border_radius || 0;

		// 选项卡整体背景样式
		var tab_box_bg_style = data.tab_box_bg_style || "color";
		// 选项卡整体背景颜色
		var tab_box_bgColor = data.tab_box_bgColor || "";
		// 选项卡整体渐变色背景
		var tab_box_bgGradient = data.tab_box_bgGradient || null;

		/* 正常 */
		// 文字大小
		var tab_item_fontsize = data.tab_item_fontsize || 18;
		// 文字加粗
		var tab_item_fontW = data.tab_item_fontW || 0;
		// 文字颜色
		var tab_item_fontcolor = data.tab_item_fontcolor || "#979A9F";

		/* 滑过 */
		// 文字大小
		var tab_item_fontsize_hover = data.tab_item_fontsize_hover || 18;
		// 文字加粗
		var tab_item_fontW_hover = data.tab_item_fontW_hover || 0;
		// 文字颜色
		var tab_item_fontcolor_hover = data.tab_item_fontcolor_hover || "#111111";

		/* 选中 */
		// 文字大小
		var tab_item_fontsize_active = data.tab_item_fontsize_active || 18;
		// 文字加粗
		var tab_item_fontW_active = data.tab_item_fontW_active || 0;
		// 文字颜色
		var tab_item_fontcolor_active = data.tab_item_fontcolor_active || "#111111";
		// 下划线高度
		var tab_item_bor_h_active = data.tab_item_bor_h_active || 4;
		// 下划线颜色
		var tab_item_bor_c_active = data.tab_item_bor_c_active || "#B71B28";

		/*菜单项 内容配置*/
		// 菜单项内容边距
		var item_content_p = data.item_content_p || 0;
        // 内容布局方式（pc及平板）
		var item_content_style = data.item_content_style || "row";
        // 内容布局方式（手机端）
		var item_content_style_xs = data.item_content_style_xs || "column";
        // 图文之间间距
		var item_content_between = data.item_content_between || 154;

        // 内容整体背景颜色
        var item_content_bg = data.item_content_bg || "";
        // 内容部分图片边框半径
        var item_content_img_border_radius = data.item_content_img_border_radius || 4;

        // 内容标题文字大小
        var item_content_t_fontsize = data.item_content_t_fontsize || 24;
        // 内容标题文字加粗
        var item_content_t_fontW = data.item_content_t_fontW || 0;
        // 内容标题文字行高
        var item_content_t_lineHeight = data.item_content_t_lineHeight || 32;
        // 内容标题文字颜色
        var item_content_t_fontcolor = data.item_content_t_fontcolor || "#111111";
        // 内容标题下边距
        var item_content_t_mb = data.item_content_t_mb || 54;
        // 内容简介文字大小
        var item_content_d_fontsize = data.item_content_d_fontsize || 16;
        // 内容简介文字行高
        var item_content_d_lineHeight = data.item_content_d_lineHeight || 24;
        // 内容简介文字颜色
        var item_content_d_fontcolor = data.item_content_d_fontcolor || "#979A9F";

        // 内容图片宽度
		var item_content_image_width = data.item_content_image_width || 550;
        // 内容图片高度
		var item_content_image_height = data.item_content_image_height || 310;
        // 图片填充方式
		var item_image_fit = data.item_image_fit || "none";

        // 首字符放大
        var section_name_first = data.section_name_first || 0;
        var fontsize_first = data.fontsize_first || 45;
        var section_text_Height = data.section_text_Height || 0;

        // 图标定位
        var icon_zdywz = data.icon_zdywz || 0;
        var icon_top = data.icon_top || 0;
        var icon_left = data.icon_left || 0;

        // 下划线位置
        var line_border_position = data.line_border_position || "type01";
        // 图标位置
        var content_icon_position = data.content_icon_position || "type01";

		// 名称动画效果
		var animate_name = data.animate_name || "none";
		// 名称动画移动距离
		var animate_name_translate = data.animate_name_translate || -12;

		// 副标题动画效果
		var animate_s_name = data.animate_s_name || "none";
		// 副标题动画移动距离
		var animate_s_name_translate = data.animate_s_name_translate || -12;

		// 下划线动画效果
		var animate_name_line = data.animate_name_line || "none";
		// 下划线动画移动距离
		var animate_name_line_translate = data.animate_name_line_translate || -12;

		// 是否启用背景图片
		var is_bgImg = data.is_bgImg || 0;

		// 边框动画
		var animate_border_line = data.animate_border_line || "none";

        var is_zhong_label = data.is_zhong_label || 0;
        var jintou_icon = data.jintou_icon || "https://oss.lcweb01.cn/joomla/20220516/a778388170b2a96c37c594cfe88300bc.png";
        var jiantou_width = data.jiantou_width || "36";
        var jiantou_right = data.jiantou_right || "0";
        var jiantou_top = data.jiantou_top || "50";
        var jiantou_height = data.jiantou_height || "36";

        var jiantou_nth_md = data.jiantou_nth || "0";
        var jiantou_nth_sm = data.jiantou_nth_sm || "0";
        var jiantou_nth_xs = data.jiantou_nth_xs || "3";
        var jiantou_nthright = data.jiantou_nthright || 0;
        var jiantou_nthtop = data.jiantou_nthtop || "50";

        var section_tab_active = data.section_tab_active || 1;

		#>
        <style type="text/css">
			{{addonId}} * {
				margin: 0;
				padding: 0;
			}
            {{addonId}} .flex {
                display: flex;
                align-items: center;
            }
			{{addonId}} .section-5 {
				/*overflow: hidden;*/
				position: relative;
			}
			{{addonId}} .section-5, {{addonId}} .section-5 .s-ul {
				width: 100%;
			    display: flex;
			    display: -webkit-flex;
				/*align-items: center;*/
                flex-wrap: wrap;
				<# if(is_swiper == 1) { #>
				justify-content: space-between;
                <# } #>
				list-style: none;
			}
			<# if(is_swiper == 1) { #>
			{{addonId}} .section-5 {
			    <# if(swiper_container_p && _.isObject(swiper_container_p)) { #>
                padding: {{ swiper_container_p.md }};
                <# } else { #>
                padding: {{ swiper_container_p }}px;
                <# } #>
			}
			<# } #>
			{{addonId}} .section-5 .s-ul {

			}
			{{addonId}} .section-5 .s-li {
				<# if(section_tab_m && _.isObject(section_tab_m)) { #>
				width: calc(100% / {{ section_tab_num.md }} - {{ section_tab_m.md * 2 }}px);
                    <# if(is_swiper == 1) {//console.log(section_tab_item);
                    var letter = section_tab_m.md - section_tab_m.md / section_tab_num.md
                    #>
                    width: calc(100% / {{ section_tab_num.md }} - {{ letter }}px);
                    <# } #>
				<# } else { #>
				width: calc(100% / {{ section_tab_num.md }});
				<# } #>
				cursor: pointer;
				<# if(is_unite_bgColor) {
                    if(bgColor_wz==1 && !is_bgImg){ #>
                        background-color: {{section_tab_bgColor}} !important;
                    <# }
				} #>
                <# //if(icon_zdywz==1 || (data.animate_bg && data.animate_bg != "none")){ #>
                    position: relative;
                <# //} #>

				<# if(section_tab_height.md) { #>
				height: {{ section_tab_height.md }}px;
                <# } #>
				<# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 0) { #>
                margin: {{ section_tab_m.md }}px;
                <# } else if(is_swiper == 0) { #>
                margin: {{ section_tab_m }}px;
                <# } #>
                <# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 1) {#>
                margin-right: {{ section_tab_m.md }}px;
                <# } #>
				<# if(!is_bgImg) { #>
                    <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                    padding: {{ section_tab_p.md }};
                    <# } else { #>
                    padding: {{ section_tab_p }}px;
                    <# } #>
                <# } #>
				<# if(is_shadow == 1) { #>
				box-shadow: {{box_color}} {{box_h_shadow.md}}px {{box_v_shadow.md}}px {{box_blur.md}}px {{box_spread.md}}px;
				<# } #>
				<# if(is_border == 1) { #>
				border: {{border_color}} {{border_style}} {{border_width.md}}px;
				border-radius: {{border_radius.md}}px;
				<# } #>
				transition: all ease-in-out 300ms;
				<# if((data.animate_bg && data.animate_bg != "none") || is_bgImg == 1) {#>
				overflow: hidden;
			    <# } #>
			}
			/*边框动画*/
            <# if(is_border == 1 && animate_border_line != "none") { #>
                {{addonId}} .section-5 .s-li .border-line {
                    position: absolute;
                    background-color: {{border_color_hover}};
				    transition: all ease-in-out 300ms;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-01,
                {{addonId}} .section-5 .s-li .border-line.border-line-03
                {
                    width: 0px;
                    height: {{border_width_hover.md}}px;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-02,
                {{addonId}} .section-5 .s-li .border-line.border-line-04
                {
                    height: 0px;
                    width: {{border_width_hover.md}}px;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-02,
                {{addonId}} .section-5 .s-li .border-line.border-line-03
                {
                    bottom: 0;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-01,
                {{addonId}} .section-5 .s-li .border-line.border-line-04
                {
                    top: 0;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-01,
                {{addonId}} .section-5 .s-li .border-line.border-line-03,
                {{addonId}} .section-5 .s-li .border-line.border-line-04
                {
                    left: 0;
                }
                {{addonId}} .section-5 .s-li .border-line.border-line-02
                {
                    right: 0;
                }
                {{addonId}} .section-5 .s-li:hover .border-line.border-line-01,
                {{addonId}} .section-5 .s-li.actives .border-line.border-line-01,
                {{addonId}} .section-5 .s-li:hover .border-line.border-line-03,
                {{addonId}} .section-5 .s-li.actives .border-line.border-line-03
                {
                    width: 100%;
                }
                {{addonId}} .section-5 .s-li:hover .border-line.border-line-02,
                {{addonId}} .section-5 .s-li.actives .border-line.border-line-02,
                {{addonId}} .section-5 .s-li:hover .border-line.border-line-04,
                {{addonId}} .section-5 .s-li.actives .border-line.border-line-04
                {
                    height: 100%;
                }
            <# } #>
			<#if(is_swiper == 1) {#>
                {{addonId}} .section-5 .s-li:nth-child({{section_tab_num.md}}n) {
                    margin-right: 0;
                }
			<#}#>
			{{addonId}} .section-5 .item {
				<# if(content_style && (content_style == "row" || content_style == "row-reverse")) { #>
			    height: 100%;
				justify-content: flex-start !important;
			    <# } #>
				text-decoration: none;
				display: flex;
			    display: -webkit-flex;
				align-items: center;
				justify-content: center;
                position: relative;
                transition: all ease-in-out 300ms;
                z-index: 10;
				<# if(content_style) { #>
				flex-direction: {{ content_style }};
				<# } else { #>
				flex-direction: column;
				<# } #>
				<# if(is_bgImg) { #>
                    <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                    padding: {{ section_tab_p.md }};
                    <# } else { #>
                    padding: {{ section_tab_p }}px;
                    <# } #>
                <# } #>
                <# if(is_unite_bgColor) {
                    if(bgColor_wz==1 && is_bgImg == 1){ #>
                        background-color: {{section_tab_bgColor}} !important;
                    <# }
				} #>
			}
			<# if(is_bgImg == 1) {#>
			    {{addonId}} .section-5 .s-li::before {
                    content: "";
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    z-index: 1;
                    left: 0;
                    top: 0;
                    background-image: url();
                    background-size: cover;
                    transition: all ease-in-out 300ms;
                }
			    {{addonId}} .section-5 .s-li:hover::before,
			    {{addonId}} .section-5 .s-li.actives::before
			    {
			        <# if(data.is_ngImg_scale == 1) { #>
			        transform: scale(1.08);
			        <# } #>
			    }
            <# } #>
			<# if(data.animate_bg && data.animate_bg != "none") {#>
			    {{addonId}} .section-5 .s-li::after {
			        width: 100%;
                    height: 100%;
                    background: {{section_tab_bgColor_hover}};
                    left: 0;
                    top: 0;
                    content: "";
                    position: absolute;
                    z-index: 2;
				    transition: all ease-in-out {{ data.animate_bg_time || 300 }}ms;
				    <# if(data.animate_bg == "middleTopExpansion") {#>
                    transform: scale(1, 0);
			        <# } #>
				    <# if(data.animate_bg == "middleLeftExpansion") {#>
                    transform: scale(0, 1);
			        <# } #>
				    <# if(data.animate_bg == "bottomExpansion") {#>
                    top: inherit;
                    bottom: 0;
                    height: 0;
			        <# } #>
			    }
			    {{addonId}} .section-5 .s-li:hover::after,
			    {{addonId}} .section-5 .s-li.actives::after {
			        <# if(data.animate_bg == "middleTopExpansion" || data.animate_bg == "middleLeftExpansion") {#>
			            transform: scale(1, 1);
			        <# } #>
				    <# if(data.animate_bg == "bottomExpansion") {#>
                    height: 100%;
			        <# } #>
			    }
			<# } #>
            <# if(is_zhong_label==1){ #>
                {{addonId}} .section-5 .s-ul>li:after{
                        width: {{jiantou_width}}px;
                        height: {{jiantou_height}}px;
                        position: absolute;
                        left: {{jiantou_right}}px;
                        top:{{jiantou_top}}px;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                }

                <# if(jiantou_nth_md!=0){ #>

                    {{addonId}} .section-5 .s-li:nth-child({{jiantou_nth_md}}):after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: {{jiantou_nthright}}px;
                            top:{{jiantou_nthtop}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                    }
                    {{addonId}} .section-5, {{addonId}} .section-5 .s-ul {
                        display:block;
                        flex-wrap: inherit;
                    }
                    {{addonId}} .section-5 .s-ul li{float:left;}
                    {{addonId}} .section-5 .s-ul .s-li:nth-child({{jiantou_nth_md}}) ~ .s-li {float:right;}
                    {{addonId}} .section-5 .s-li:nth-of-type({{jiantou_nth_md}}) ~ .s-li:after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: -15px;
                            top:{{jiantou_top}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                    }


                <# } #>

                {{addonId}} .section-5 .s-ul li:last-child:after{
                        width: 0px;
                        height: 0px;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;
                }

                {{addonId}} .section-5 .s-li:hover a,
                {{addonId}} .section-5 .s-li.actives a{
                    <# if(is_unite_bgColor && !is_bgImg && section_tab_bgColor_hover) {
                        if(bgColor_wz==1 && (!data.animate_bg || data.animate_bg == "none")){ #>
                            background-color: {{section_tab_bgColor_hover}} !important;
                        <# }
                        if(data.animate_bg && data.animate_bg != "none"){ #>
                            background-color: transparent !important;
                        <# }
                    } #>
                    <# if(is_shadow_hover == 1) { #>
                    box-shadow: {{box_color_hover}} {{box_h_shadow_hover.md}}px {{box_v_shadow_hover.md}}px {{box_blur_hover.md}}px {{box_spread_hover.md}}px;
                    <# } #>
                    <# if(is_border_hover == 1 && animate_border_line == "none") { #>
                    border: {{border_color_hover}} {{border_style_hover}} {{border_width_hover.md}}px;
                    border-radius: {{border_radius_hover.md}}px;
                    <# } #>
                    <# if(is_animate_hover && animate_menu != "none") { #>
                    transform: {{animate_menu}}({{animate_translate}}px);
                    -webkit-transform: {{animate_menu}}({{animate_translate}}px);
                    <# } #>
                }
            <# }else{ #>
                {{addonId}} .section-5 .s-li:hover ,
                {{addonId}} .section-5 .s-li.actives {
                    <# if(is_unite_bgColor && !is_bgImg && section_tab_bgColor_hover) {
                        if(bgColor_wz==1 && (!data.animate_bg || data.animate_bg == "none")){ #>
                            background-color: {{section_tab_bgColor_hover}} !important;
                        <# }
                        if(data.animate_bg && data.animate_bg != "none"){ #>
                            background-color: transparent !important;
                        <# }
                    } #>
                    <# if(is_shadow_hover == 1) { #>
                    box-shadow: {{box_color_hover}} {{box_h_shadow_hover.md}}px {{box_v_shadow_hover.md}}px {{box_blur_hover.md}}px {{box_spread_hover.md}}px;
                    <# } #>
                    <# if(is_border_hover == 1 && animate_border_line == "none") { #>
                    border: {{border_color_hover}} {{border_style_hover}} {{border_width_hover.md}}px;
                    border-radius: {{border_radius_hover.md}}px;
                    <# } #>
                    <# if(is_animate_hover && animate_menu != "none") { #>
                    transform: {{animate_menu}}({{animate_translate}}px);
                    -webkit-transform: {{animate_menu}}({{animate_translate}}px);
                    <# } #>
                }
            <# } #>
			{{addonId}} .section-5 .s-li:hover .item,
			{{addonId}} .section-5 .s-li.actives .item {
			    <# if(is_bgImg == 1) {
                    if(is_unite_bgColor  && section_tab_bgColor_hover) {
                        if(bgColor_wz==1 && (!data.animate_bg || data.animate_bg == "none")){ #>
                            background-color: {{section_tab_bgColor_hover}} !important;
                        <# }
                        if(data.animate_bg && data.animate_bg != "none"){ #>
                            background-color: transparent !important;
                        <# }
                    }
                }#>
			}
			{{addonId}} .section-5 .item .content-icon {
				<# if(content_style && (content_style == "row" || content_style == "row-reverse")) { #>
					<# if(section_tab_icon_width.md) { #>
						width: {{section_tab_icon_width.md}}px;
					<# } else { #>
						width: 100%;
					<# } #>
					<# if(section_tab_icon_height.md) { #>
						height: {{section_tab_icon_height.md}}px;
					<# } else { #>
						height: 100%;
					<# } #>
				<# } else { #>
					width: 100%;
				<# } #>
				<# if(section_icon_m && _.isObject(section_icon_m)) { #>
                margin: {{ section_icon_m.md }};
                <# } else { #>
                margin: {{ section_icon_m }}px;
                <# } #>
                <# if(data.is_hidden_icon == 1) { #>
					overflow: hidden;
				<# } #>
                text-align: {{ content_icon_align }};
			}
			{{addonId}} .section-5 .item .icon-box {
				display: inline-block;
				transition: all ease-in-out {{ data.animate_time || "300"}}ms;
				<# if(section_tab_icon_width.md) { #>
				width: {{section_tab_icon_width.md}}px;
				<# } else { #>
				width: 100%;
				<# } #>
				<# if(section_tab_icon_height.md) { #>
				height: {{section_tab_icon_height.md}}px;
				<# } else { #>
				height: 100px;
				<# } #>
				background: url() no-repeat center;
				background-size: 100%;
                <# if(icon_zdywz==1){ #>
                    position: absolute;top:{{icon_top}}px;right:calc(100% / {{ section_tab_num.md }} - {{ section_tab_m.md * 2 }}px - {{icon_left}}px);
                <# } #>
			}
			{{addonId}} .section-5 .s-li:hover .icon-box,
			{{addonId}} .section-5 .s-li.actives .icon-box {
			    <# if(section_tab_icon_width_hover && section_tab_icon_width_hover.md) { #>
				width: {{section_tab_icon_width_hover.md}}px;
				<# } #>
			    <# if(section_tab_icon_height_hover && section_tab_icon_height_hover.md) { #>
				height: {{section_tab_icon_height_hover.md}}px;
				<# } #>
				<# if(is_animate_hover && animate_icon != "none") { #>
				-webkit-animation-name: {{animate_icon}};
				animation-name: {{animate_icon}};
				transform: {{animate_icon}};
				<# } #>
			}
			<# _.each(section_tab_item, function(accordion_item, key){
			    // console.log("key", key);
			#>
                <#if (is_swiper == 1 && (key + 1) > section_tab_num.md){ #>
                {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                    display: none;
                }
                <# } #>
			    <# if(is_unite_bgColor != 1) { #>
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                        <# if(accordion_item.item_bg_style == "color") { #>
                        background-color: {{accordion_item.bgColor}};
                        <# } #>
                        <# if(accordion_item.item_bg_style == "gradient" && _.isObject(accordion_item.bgGradient)) {
                            // console.log(accordion_item.bgGradient)
                        #>
                            background-image: {{accordion_item.bgGradient.type || "linear"}}-gradient(
                            <# if(accordion_item.bgGradient.type && accordion_item.bgGradient.type == "radial") { #>
                            at {{accordion_item.bgGradient.radialPos || "center center"}}
                            <# } else { #>
                            {{accordion_item.bgGradient.deg || 0}}deg
                            <# } #>
                            ,
                                {{accordion_item.bgGradient.color}} {{accordion_item.bgGradient.pos || 0}}%,
                                {{accordion_item.bgGradient.color2}} {{accordion_item.bgGradient.pos2 || 100}}%);
                        <# } #>
                    }
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}):hover,
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}).actives {
                        <# if(accordion_item.item_bg_style == "color" && accordion_item.hover_bgColor) { #>
                        background-color: {{accordion_item.hover_bgColor}};
                        <# } #>
                        <# if(accordion_item.item_bg_style == "gradient" && _.isObject(accordion_item.hover_bgGradient)) { #>
                            background-image: {{accordion_item.hover_bgGradient.type || "linear"}}-gradient(
                            <# if(accordion_item.hover_bgGradient.type && accordion_item.hover_bgGradient.type == "radial") { #>
                            at {{accordion_item.hover_bgGradient.radialPos || "center center"}}
                            <# } else { #>
                            {{accordion_item.hover_bgGradient.deg || 0}}deg
                            <# } #>
                            ,
                                {{accordion_item.hover_bgGradient.color}} {{accordion_item.hover_bgGradient.pos || 0}}%,
                                {{accordion_item.hover_bgGradient.color2}} {{accordion_item.hover_bgGradient.pos2 || 100}}%);
                        <# } #>
                    }
			    <# } #>
			    <# if(accordion_item.icon) { #>
                {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) .item .icon-box {
                    background-image: url({{accordion_item.icon}}) !important;
                }
                <# } #>
                <# if(accordion_item.hover_icon) { #>
                {{addonId}} .section-5 .s-li:nth-child({{key + 1}}):hover .icon-box,
                {{addonId}} .section-5 .s-li:nth-child({{key + 1}}).actives .icon-box {
                    background-image: url({{accordion_item.hover_icon}}) !important;
                }
                <# } #>
                <# if(custom_border_width) { #>
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                        <# if(accordion_item.c_border_width && _.isObject(accordion_item.c_border_width)) { #>
                        border-width: {{ accordion_item.c_border_width.md }};
                        <# } else { #>
                        border-width: {{ accordion_item.c_border_width }}px;
                        <# } #>
                    }
                <# } #>
                <# if(is_bgImg == 1) { #>
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}})::before {
                        <# if(accordion_item.bgImg) { #>
                        background-image: url(\'{{ accordion_item.bgImg }}\');
                        <# } #>
                    }
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}):hover::before {
                        <# if(accordion_item.hover_bgImg) { #>
                        background-image: url(\'{{ accordion_item.hover_bgImg }}\');
                        <# } #>
                    }
                <# } #>
			<# }) #>
			{{addonId}} .section-5 .item .info {
			    width: 100%;
			}
			{{addonId}} .section-5 .item .name {
				color: {{ section_tab_fontcolor }};
				background-color: {{ data.section_tab_name_bgColor }};
				background-image: url(\'{{ data.section_tab_name_bgImg }}\');
				font-size: {{ section_tab_fontsize.md }}px;
				line-height: {{ section_tab_lineHeight.md }}px;
                width: 100%;
				transition: all ease-in-out 300ms;
                text-align: {{ content_name_align }};
                <# if(section_tab_fontW == 1) { #>
                font-weight: bold;
                <# } #>
                <# if(section_name_m && _.isObject(section_name_m)) { #>
                margin: {{ section_name_m.md }};
                <# } else { #>
                margin: {{ section_name_m }}px;
                <# } #>
                <# if(section_name_p && _.isObject(section_name_p)) { #>
                padding: {{ section_name_p.md }};
                <# } else { #>
                padding: {{ section_name_p }}px;
                <# } #>
                <# if(is_unite_bgColor) { 
                    if(bgColor_wz==2){ #>
                        background-color: {{section_tab_bgColor}} !important;
                   <# }
                } #>
                <# if(section_text_Height) { #>
                    height:{{section_text_Height}}px;
                <# } #>
			}
			<# if(content_name_align && content_name_align == "type01") { #>
			    {{addonId}} .section-5 .s-li:nth-child(2n) .item .name {
			        text-align: right;
			    }
			<# } #>
			{{addonId}} .section-5 .item .name b {
			    <# if(data.section_item_name_b_bgImg) { #>
			        display: inline-block;
				    background-image: url(\'{{ data.section_item_name_b_bgImg }}\');
				    background-size: contain;
                    background-repeat: no-repeat;
                    text-align: center;
                    width: 26px;
                    height: 26px;
                    line-height: 26px;
                    margin-right: 5px;
                <# } #>
			}
			{{addonId}} .section-5 .s-li:hover .name,
			{{addonId}} .section-5 .s-li.actives .name
			{
				background-color: {{ data.section_tab_name_bgColor_hover }};
				<#if (data.section_tab_name_bgImg_hover) {#>
				background-image: url(\'{{ data.section_tab_name_bgImg_hover }}\');
                <# } #>
				<# if(section_tab_fontcolor_hover) { #>
				color: {{ section_tab_fontcolor_hover }};
                <# } #>
				<# if(section_tab_fontsize_hover) { #>
				font-size: {{ section_tab_fontsize_hover.md }}px;
				<# } #>
                <# if(is_unite_bgColor) { 
                    if(bgColor_wz==2){ #>
                        background-color: {{section_tab_bgColor_hover}} !important;
                    <# }
                } #>
                <# if(animate_name && animate_name != "none") { #>
                transform: {{animate_name}}({{animate_name_translate}}px);
				-webkit-transform: {{animate_name}}({{animate_name_translate}}px);
                <# } #>
			}
			{{addonId}} .section-5 .item .s-name {
				color: {{ section_tab_s_name_fontcolor }};
				font-size: {{ section_tab_s_name_fontsize.md }}px;
				line-height: {{ section_tab_s_name_lineHeight.md }}px;
                width: 100%;
				transition: all ease-in-out 300ms;
                text-align: {{ content_s_name_align }};
			}
			{{addonId}} .section-5 .s-li:hover .s-name,
			{{addonId}} .section-5 .s-li.actives .s-name
			{
				<# if(section_tab_s_name_fontcolor_hover) { #>
				color: {{ section_tab_s_name_fontcolor_hover }};
                <# } #>
				<# if(section_tab_s_name_fontsize_hover) { #>
				font-size: {{ section_tab_fontsize_hover.md }}px;
				<# } #>
                <# if(animate_s_name && animate_s_name != "none") { #>
                transform: {{animate_s_name}}({{animate_s_name_translate}}px);
				-webkit-transform: {{animate_s_name}}({{animate_s_name_translate}}px);
                <# } #>
			}
            
            <# if(section_name_first==1) { #>
                {{addonId}} .section-5 .item p.name:first-letter{
                    font-size: {{fontsize_first}}px;
                    position:relative;z-index:1;bottom:0px;line-height:0px;
                } 
            <# } #>

			{{addonId}} .section-5 .item .line-box {
			    text-align: {{ line_border_align }};
			}
			{{addonId}} .section-5 .item .line {
				display: inline-block;
				transition: all ease-in-out 300ms;
				width: {{ line_border_width.md }}px;
				<# if(line_border_color_style == "color") { #>
				    border-bottom: {{ line_border_color }} {{ line_border_style }} {{ line_border_height.md }}px;
				<# } #>
				<# if(line_border_color_style == "gradient") { #>
				    height: {{line_border_height.md}}px;
				    border-radius: {{line_border_height.md}}px;
				    <# if(line_border_bgGradient && _.isObject(line_border_bgGradient)) { #>
                        background-image: {{line_border_bgGradient.type || "linear"}}-gradient(
                        <# if(line_border_bgGradient.type && line_border_bgGradient.type == "radial") { #>
                        at {{line_border_bgGradient.radialPos || "center center"}}
                        <# } else { #>
                        {{line_border_bgGradient.deg || 0}}deg
                        <# } #>
                        , 
                            {{line_border_bgGradient.color}} {{line_border_bgGradient.pos || 0}}%, 
                            {{line_border_bgGradient.color2}} {{line_border_bgGradient.pos2 || 100}}%);
                    <# } #>
				<# } #>
				<# if(section_line_m && _.isObject(section_line_m)) { #>
                margin-bottom: {{ section_line_m.md }}px;
                <# } else { #>
                margin-bottom: {{ section_line_m }}px;
                <# } #>
			}
			{{addonId}} .section-5 .s-li:hover .line,
			{{addonId}} .section-5 .s-li.actives .line
			{
				width: {{ line_border_width_hover.md }}px;
				<# if(line_border_color_style == "color") { #>
				    border-bottom: {{ line_border_color_hover }} {{ line_border_style_hover }} {{ line_border_height_hover.md }}px;
				<# } #>
				<# if(line_border_color_style == "gradient") { #>
				    height: {{line_border_height_hover.md}}px;
				    <# if(line_border_bgGradient_hover && _.isObject(line_border_bgGradient_hover)) { #>
                        background-image: {{line_border_bgGradient_hover.type || "linear"}}-gradient(
                        <# if(line_border_bgGradient_hover.type && line_border_bgGradient_hover.type == "radial") { #>
                        at {{line_border_bgGradient_hover.radialPos || "center center"}}
                        <# } else { #>
                        {{line_border_bgGradient_hover.deg || 0}}deg
                        <# } #>
                        , 
                            {{line_border_bgGradient_hover.color}} {{line_border_bgGradient_hover.pos || 0}}%, 
                            {{line_border_bgGradient_hover.color2}} {{line_border_bgGradient_hover.pos2 || 100}}%);
                    <# } #>
				<# } #>
                <# if(animate_name_line && animate_name_line != "none") { #>
                transform: {{animate_name_line}}({{animate_name_line_translate}}px);
				-webkit-transform: {{animate_name_line}}({{animate_name_line_translate}}px);
                <# } #>
			}
			{{addonId}} .section-5 .item .desc {
				color: {{ section_desc_fontcolor }};
				font-size: {{ section_desc_fontsize.md }}px;
				line-height: {{ section_desc_lineHeight.md }}px;
                width: 100%;
				transition: all ease-in-out 300ms;
                text-align: {{ content_desc_align }};
                <# if(section_desc_p && _.isObject(section_desc_p)) { #>
                padding: {{ section_desc_p.md }};
                <# } else { #>
                padding: {{ section_desc_p }}px;
                <# } #>
                <# if(section_desc_height) { #>
			    height: {{ section_desc_height.md }}px;
			    <# } else { #>
                height: auto;
                <# } #>
                <# if(is_unite_bgColor) { 
                    if(bgColor_wz==3){ #>
                        background-color: {{section_tab_bgColor}} !important;
                   <# }
                } #>
			}
			{{addonId}} .section-5 .s-li:hover .desc,
			{{addonId}} .section-5 .s-li.actives .desc
			{
				<# if(section_desc_fontcolor_hover) { #>
				color: {{ section_desc_fontcolor_hover }};
                <# } #>
				<# if(section_desc_fontsize_hover) { #>
				font-size: {{ section_desc_fontsize_hover.md }}px;
				<# } #>
                <# if(is_unite_bgColor) { 
                    if(bgColor_wz==3){ #>
                        background-color: {{section_tab_bgColor_hover}} !important;
                   <# }
                } #>
			}
			/* 更多按钮 */
			{{addonId}} .section-5 .item .more-box {
			    <# if(more_icon_m && _.isObject(more_icon_m)) { #>
                padding: {{ more_icon_m.md }};
                <# } else { #>
                padding: {{ more_icon_m }}px;
                <# } #>
                display: flex;
                justify-content: {{ more_icon_align }};
				transition: all ease-in-out 300ms;
				<# if(data.is_show_more_normal == 1) { #>
				opacity: 0;
				<# } #>
			}
			{{addonId}} .section-5 .s-li:hover .item .more-box,
			{{addonId}} .section-5 .s-li.actives .item .more-box
            {
			    opacity: 1;
			}
			{{addonId}} .section-5 .item .more-icon {
			    <# if(more_icon_width) { #>
			    width: {{ more_icon_width.md }}px;
			    <# } else { #>
                width: auto;
                <# } #>
			}
			/* 切换 配置样式 */
	        {{addonId}} .swiper-button {
	            width: auto;
	            height: auto;
                top: {{swiper_button_top.md}}{{swiper_button_top_unit}};
	        }
	        {{addonId}} .swiper-button:after {
	            content: "";
	            background: url() no-repeat center;
                background-size: cover;
                width: {{swiper_button_width.md}}px;
                height: {{swiper_button_height.md}}px;
	        }
	        {{addonId}} .swiper-button-prev,{{addonId}} .swiper-container-rtl .swiper-button-next {
	            left: {{swiper_button_left.md}}px;
	            background-image: none;
	        }
	        {{addonId}} .swiper-button-prev:after,{{addonId}} .swiper-container-rtl .swiper-button-next:after {
                background-image: url({{swiper_button_prev}});
            }
            {{addonId}} .swiper-button-next,{{addonId}} .swiper-container-rtl .swiper-button-prev {
	            right: {{swiper_button_left.md}}px;
	            background-image: none;
	        }
	        {{addonId}} .swiper-button-next:after,{{addonId}} .swiper-container-rtl .swiper-button-prev:after {
	            background-image: url({{swiper_button_next}});
	        }
			/*选项卡*/
			{{addonId}} .menu-tab-box {
			    display: flex;
			    display: -webkit-flex;
			    align-items: center;
			    flex-wrap: wrap;
			    justify-content: {{ tab_align }};
			    border-color: {{ tab_box_border_color }};
			    border-width: {{ tab_box_border_width.md || "0px" }};
			    border-style: {{ tab_box_border_style }};
			    border-radius: {{ tab_box_border_radius.md || 0 }};
			    <# if(tab_box_bg_style == "color") { #>
                background-color: {{tab_box_bgColor}};
                <# } #>
                <# if(tab_box_bg_style == "gradient" && _.isObject(tab_box_bgGradient)) { #>
                    background-image: {{tab_box_bgGradient.type || "linear"}}-gradient(
                    <# if(tab_box_bgGradient.type && tab_box_bgGradient.type == "radial") { #>
                    at {{tab_box_bgGradient.radialPos || "center center"}}
                    <# } else { #>
                    {{tab_box_bgGradient.deg || 0}}deg
                    <# } #>
                    , 
                        {{tab_box_bgGradient.color}} {{tab_box_bgGradient.pos || 0}}%, 
                        {{tab_box_bgGradient.color2}} {{tab_box_bgGradient.pos2 || 100}}%);
                <# } #>
			    margin-bottom: 35px;
			}
			{{addonId}} .menu-tab-box .menu-tab-item {
			    font-size: {{ tab_item_fontsize.md || 18 }}px;
			    line-height: {{ tab_item_height.md || 67 }}px;
                <# if(tab_item_fontW == 1) { #>
                font-weight: bold;
                <# } #>
                color: {{ tab_item_fontcolor }};
                width: calc(100% / {{ change_tab_num.md || 4 }});
                text-align: center;
                position: relative;
                cursor: pointer;
                transition: all ease-in-out 300ms;
			}
			{{addonId}} .menu-tab-box .menu-tab-item span {
                position: relative;
                display: inline-block;
			}
			{{addonId}} .menu-tab-box .menu-tab-item span:after {
			    content: "";
			    width: 100%;
			    height: 0px;
                position: absolute;
                left: 0;
                right: 0;
                bottom: -{{ tab_item_bor_h_active.md }}px;
                margin: auto;
                transition: all ease-in-out 300ms;
			}
			{{addonId}} .menu-tab-box .menu-tab-item:hover{
			    font-size: {{ tab_item_fontsize_hover.md || 18 }}px;
                <# if(tab_item_fontW_hover == 1) { #>
                font-weight: bold;
                <# } #>
                color: {{ tab_item_fontcolor_hover }};
			}
			{{addonId}} .menu-tab-box .menu-tab-item.actives {
			    font-size: {{ tab_item_fontsize_active.md || 18 }}px;
                <# if(tab_item_fontW_active == 1) { #>
                font-weight: bold;
                <# } #>
                color: {{ tab_item_fontcolor_active }};
			}
			{{addonId}} .menu-tab-box .menu-tab-item.actives span:after {			    
			    height: {{ tab_item_bor_h_active.md }}px;
                background: {{ tab_item_bor_c_active }};
			}
			/* 内容部分样式 */
			{{addonId}} .section-content {
			    display: flex;
			    flex-direction: {{item_content_style}};
			    align-items: center;
                justify-content: space-between;
                background-color: {{item_content_bg}};
                <# if(item_content_p && _.isObject(item_content_p)) { #>
                padding: {{ item_content_p.md }};
                <# } else { #>
                padding: 95px 0;
                <# } #>
			}
			{{addonId}} .section-content-box.hidden {
			    display: none;
			}
			{{addonId}} .section-content .text-wrap { 
			    flex: 1;
			}
			{{addonId}} .section-content .content-line { 
			    <# 
			        var lineW = "";
			        if (item_content_style == "row" || item_content_style == "row-reverse") { 
			            lineW = "width";
			        } else {
			            lineW = "height";
                    } 
                #>
                {{lineW}}: {{ item_content_between.md || 154 }}px;
			}
			{{addonId}} .section-content .text-wrap .text-title {
			    font-size: {{ item_content_t_fontsize.md || 24 }}px;
                line-height: {{ item_content_t_lineHeight.md || 32 }}px;
                color: {{ item_content_t_fontcolor }};
                margin-bottom: {{ item_content_t_mb.md || 54 }}px;
                <# if(item_content_t_fontW == 1) { #>
                font-weight: bold;
                <# } #>
                text-align: {{data.item_content_title_align}};
			}
			{{addonId}} .section-content .text-wrap .text-desc {
                font-size: {{ item_content_d_fontsize.md || 16 }}px;
                color: {{ item_content_d_fontcolor }};
                line-height: {{ item_content_d_lineHeight.md || 24 }}px;
			}
			{{addonId}} .section-content .image-wrap {
			    width: {{item_content_image_width.md ? item_content_image_width.md + "px" : "100%"}};
                height: {{item_content_image_height.md ? item_content_image_height.md + "px" : "100%"}};
                border-radius: {{ item_content_img_border_radius.md || 4 }}px;
                overflow: hidden;
			}
			{{addonId}} .section-content .image-wrap img {
			    width: 100%;
			    height: 100%;
			    object-fit: {{item_image_fit}};
			}
			@media (min-width: 768px) and (max-width: 991px) {
                <# if(is_swiper == 1) { #>
                {{addonId}} .section-5 {
                    <# if(swiper_container_p && _.isObject(swiper_container_p)) { #>
                    padding: {{ swiper_container_p.sm }};
                    <# } else { #>
                    padding: {{ swiper_container_p }}px;
                    <# } #>
                }
                <# } #>

                <# if(is_zhong_label==1){ #>
                    {{addonId}} .section-5 .s-ul>li:after{
                        width: {{jiantou_width}}px;
                        height: {{jiantou_height}}px;
                        position: absolute;
                        left: {{jiantou_right}}px;
                        top:{{jiantou_top}}px;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                    }
                    
                    <# if(jiantou_nth_sm!=0){ #>

                        {{addonId}} .section-5 .s-li:nth-child({{jiantou_nth_sm}}):after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: {{jiantou_nthright}}px;
                            top:{{jiantou_nthtop}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                        }
                        {{addonId}} .section-5, {{addonId}} .section-5 .s-ul {
                            display:block;
                            flex-wrap: inherit;
                        }
                        {{addonId}} .section-5 .s-ul li{float:left;}
                        {{addonId}} .section-5 .s-ul .s-li:nth-child({{jiantou_nth_sm}}) ~ .s-li {float:right;}
                        {{addonId}} .section-5 .s-li:nth-of-type({{jiantou_nth_sm}}) ~ .s-li:after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: -15px;
                            top:{{jiantou_top}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                        }
                    

                    <# } #>

                    {{addonId}} .section-5 .s-ul li:last-child:after{
                        width: 0px;
                        height: 0px;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;
                    }

                <# } #>

			    {{addonId}} .section-5 .s-li {
				    <# if(section_tab_m && _.isObject(section_tab_m)) { #>
                        width: calc(100% / {{ section_tab_num.sm }} - {{ section_tab_m.sm * 2 }}px);
                        <# if(is_swiper == 1) {//console.log(section_tab_item);
                        var letter = section_tab_m.sm - section_tab_m.sm / section_tab_num.sm
                        #>
                        width: calc(100% / {{ section_tab_num.sm }} - {{ letter }}px);
                        <# } #>
					<# } else { #>
					width: calc(100% / {{ section_tab_num.sm }});
					<# } #>	
				    <# if(section_tab_height.sm) { #>
					height: {{ section_tab_height.sm }}px;				
	                <# } #>
					<# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 0) { #>
	                margin: {{ section_tab_m.sm }}px;
	                <# } else if(is_swiper == 0) { #>
                    margin: {{ section_tab_m }}px;
                    <# } #>
                    <# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 1) {#>
                    margin-right: {{ section_tab_m.sm }}px;
                    <# } #>
                    <# if(!is_bgImg) { #>
                        <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                        padding: {{ section_tab_p.sm }};
                        <# } else { #>
                        padding: {{ section_tab_p }}px;
                        <# } #>
	                <# } #>
                    <# if(is_shadow == 1) { #>
                    box-shadow: {{box_color}} {{box_h_shadow.sm}}px {{box_v_shadow.sm}}px {{box_blur.sm}}px {{box_spread.sm}}px;
                    <# } #>
                    <# if(is_border == 1) { #>
                    border-width: {{border_width.sm}}px;
				    border-radius: {{border_radius.sm}}px;
                    <# } #>
				}
                /*边框动画*/
                <# if(is_border == 1 && animate_border_line != "none") { #>
                    {{addonId}} .section-5 .s-li .border-line.border-line-01,
                    {{addonId}} .section-5 .s-li .border-line.border-line-03
                    {
                        height: {{border_width_hover.sm}}px;
                    }
                    {{addonId}} .section-5 .s-li .border-line.border-line-02,
                    {{addonId}} .section-5 .s-li .border-line.border-line-04
                    {
                        width: {{border_width_hover.sm}}px;
                    }
                <# } #>
				<#if(is_swiper == 1) {#>
                {{addonId}} .section-5 .s-li:nth-child({{section_tab_num.sm}}n) {
                    margin-right: 0;
                }
                <#}#>
                {{addonId}} .section-5 .item {
                    <# if(is_bgImg == 1) { #>
                        <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                        padding: {{ section_tab_p.sm }};
                        <# } else { #>
                        padding: {{ section_tab_p }}px;
                        <# } #>
	                <# } #>
                }
                <# if(custom_border_width) { #>
                    <# _.each(section_tab_item, function(accordion_item, key){ 
                        // console.log("key", key);
                    #>
                        {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                            <# if(accordion_item.c_border_width && _.isObject(accordion_item.c_border_width)) { #>
                            border-width: {{ accordion_item.c_border_width.sm }};
                            <# } else { #>
                            border-width: {{ accordion_item.c_border_width }}px;
                            <# } #>
                        }
			        <# }) #>
                <# } #>
				{{addonId}} .section-5 .s-li:hover,
				{{addonId}} .section-5 .s-li.actives
				{
					<# if(is_shadow_hover == 1) { #>
					box-shadow: {{box_color_hover}} {{box_h_shadow_hover.sm}}px {{box_v_shadow_hover.sm}}px {{box_blur_hover.sm}}px {{box_spread_hover.sm}}px;
					<# } #>
					<# if(is_border_hover == 1) { #>
					border: {{border_color_hover}} {{border_style_hover}} {{border_width_hover.sm}}px;
					border-radius: {{border_radius_hover.sm}}px;
					<# } #>
				}
				{{addonId}} .section-5 .item .content-icon {
					<# if(content_style && (content_style == "row" || content_style == "row-reverse")) { #>				
						<# if(section_tab_icon_width.sm) { #>
							width: {{section_tab_icon_width.sm}}px;
						<# } else { #>
							width: 100%;
						<# } #>
						<# if(section_tab_icon_height.sm) { #>
                            height: {{section_tab_icon_height.sm}}px;
                        <# } else { #>
                            height: 100%;
                        <# } #>
					<# } else { #>
						width: 100%;
					<# } #>
					<# if(section_icon_m && _.isObject(section_icon_m)) { #>
	                margin: {{ section_icon_m.sm }};
	                <# } else { #>
	                margin: {{ section_icon_m }}px;
	                <# } #>
				}
				{{addonId}} .section-5 .item .icon-box {
					<# if(section_tab_icon_width.sm) { #>
					width: {{section_tab_icon_width.sm}}px;
					<# } else { #>
					width: 100%;
					<# } #>
					<# if(section_tab_icon_height.sm) { #>
					height: {{section_tab_icon_height.sm}}px;
					<# } else { #>
					height: 100px;
					<# } #>
				}
				{{addonId}} .section-5 .s-li:hover .icon-box,
				{{addonId}} .section-5 .s-li.actives .icon-box
				{
				    <# if(section_tab_icon_width_hover && section_tab_icon_width_hover.sm) { #>
                    width: {{section_tab_icon_width_hover.sm}}px;
                    <# } #>
                    <# if(section_tab_icon_height_hover && section_tab_icon_height_hover.sm) { #>
                    height: {{section_tab_icon_height_hover.sm}}px;
                    <# } #>
				}
				<# _.each(section_tab_item, function(accordion_item, key){ #>
                    <#if (is_swiper == 1 && (key + 1) > section_tab_num.sm){ #>
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                        display: none;
                    }
                    <# } #>
                <# }) #>
				{{addonId}} .section-5 .item .name {
				    font-size: {{ section_tab_fontsize.sm }}px;
				    line-height: {{ section_tab_lineHeight.sm }}px;
				    <# if(section_name_m && _.isObject(section_name_m)) { #>
	                margin: {{ section_name_m.sm }};
	                <# } else { #>
	                margin: {{ section_name_m }}px;
	                <# } #>
				    <# if(section_name_p && _.isObject(section_name_p)) { #>
	                padding: {{ section_name_p.sm }};
	                <# } else { #>
	                padding: {{ section_name_p }}px;
	                <# } #>
				}
				{{addonId}} .section-5 .s-li:hover .name,
				{{addonId}} .section-5 .s-li.actives .name
				{
					<# if(section_tab_fontsize_hover) { #>
					font-size: {{ section_tab_fontsize_hover.sm }}px;
					<# } #>
				}
                {{addonId}} .section-5 .item .s-name {
                    font-size: {{ section_tab_s_name_fontsize.sm }}px;
                    line-height: {{ section_tab_s_name_lineHeight.sm }}px;
                }
                {{addonId}} .section-5 .s-li:hover .s-name,
                {{addonId}} .section-5 .s-li.actives .s-name
                {
                    <# if(section_tab_s_name_fontsize_hover) { #>
                    font-size: {{ section_tab_fontsize_hover.sm }}px;
                    <# } #>
                }
				{{addonId}} .section-5 .item .line {
                    width: {{ line_border_width.sm }}px;
                    <# if(line_border_color_style == "color") { #>
                        border-bottom: {{ line_border_color }} {{ line_border_style }} {{ line_border_height.sm }}px;
                    <# } #>
                    <# if(line_border_color_style == "gradient") { #>
                        height: {{line_border_height.sm}}px;
				    <# } #>
                    <# if(section_line_m && _.isObject(section_line_m)) { #>
                    margin-bottom: {{ section_line_m.sm }}px;
                    <# } else { #>
                    margin-bottom: {{ section_line_m }}px;
                    <# } #>
                }
                {{addonId}} .section-5 .s-li:hover .line,
                {{addonId}} .section-5 .s-li.actives .line
                {
                    width: {{ line_border_width_hover.sm }}px;
                    <# if(line_border_color_style == "color") { #>
                        border-bottom: {{ line_border_color_hover }} {{ line_border_style_hover }} {{ line_border_height_hover.sm }}px;
                    <# } #>
                    <# if(line_border_color_style == "gradient") { #>
                        height: {{line_border_height_hover.sm}}px;
				    <# } #>
                }
				{{addonId}} .section-5 .item .desc {
                    font-size: {{ section_desc_fontsize.sm }}px;
                    line-height: {{ section_desc_lineHeight.sm }}px;
                    <# if(section_desc_p && _.isObject(section_desc_p)) { #>
                    padding: {{ section_desc_p.sm }};
                    <# } else { #>
                    padding: {{ section_desc_p }}px;
                    <# } #>
                    <# if(section_desc_height) { #>
                    height: {{ section_desc_height.sm }}px;
                    <# } else { #>
                    /*height: auto;*/
                    <# } #>
                }
                {{addonId}} .section-5 .s-li:hover .desc,
                {{addonId}} .section-5 .s-li.actives .desc
                {
					<# if(section_desc_fontsize_hover) { #>
					font-size: {{ section_desc_fontsize_hover.sm }}px;
					<# } #>
				}
				/* 更多按钮 */
                {{addonId}} .section-5 .item .more-box {
                    <# if(more_icon_m && _.isObject(more_icon_m)) { #>
                    padding: {{ more_icon_m.sm }};
                    <# } else { #>
                    padding: {{ more_icon_m }}px;
                    <# } #>
                }
                {{addonId}} .section-5 .item .more-icon {
                    <# if(more_icon_width) { #>
                    width: {{ more_icon_width.sm }}px;
                    <# } else { #>
                    
                    <# } #>
                }
				/* 切换 配置样式 */
                {{addonId}} .swiper-button {
                    top: {{swiper_button_top.sm}}{{swiper_button_top_unit}};
                }
                {{addonId}} .swiper-button:after {
                    width: {{swiper_button_width.sm}}px;
                    height: {{swiper_button_height.sm}}px;
                }
                {{addonId}} .swiper-button-prev {
                    left: {{swiper_button_left.sm}}px;
                }
                {{addonId}} .swiper-button-next {
                    right: {{swiper_button_left.sm}}px;
                }
                /* 选项卡 */
                {{addonId}} .menu-tab-box {
                    border-width: {{ tab_box_border_width.sm || "0px" }};
                    border-radius: {{ tab_box_border_radius.sm || 0 }};
                }
                {{addonId}} .menu-tab-box .menu-tab-item {
			        font-size: {{ tab_item_fontsize.sm }}px;
			        line-height: {{ tab_item_height.sm }}px;
                    width: calc(100% / {{ change_tab_num.sm }});
                }
                {{addonId}} .menu-tab-box .menu-tab-item span:after {
                    bottom: -{{ tab_item_bor_h_active.sm }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item:hover {
                    font-size: {{ tab_item_fontsize_hover.sm }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item.actives {
                    font-size: {{ tab_item_fontsize_active.sm }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item.actives span:after {			    
                    height: {{ tab_item_bor_h_active.sm }}px;
                }
                /* 内容部分样式 */
                {{addonId}} .section-content {
                    <# if(item_content_p && _.isObject(item_content_p)) { #>
                    padding: {{ item_content_p.sm }};
                    <# } #>
                }
                {{addonId}} .section-content .content-line { 
                    <# 
                        var lineW = "";
                        if (item_content_style == "row" || item_content_style == "row-reverse") { 
                            lineW = "width";
                        } else {
                            lineW = "height";
                        } 
                    #>
                    {{lineW}}: {{ item_content_between.sm }}px;
                }
                {{addonId}} .section-content .text-wrap .text-title {
                    font-size: {{ item_content_t_fontsize.sm }}px;
                    line-height: {{ item_content_t_lineHeight.sm }}px;
                    margin-bottom: {{ item_content_t_mb.sm }}px;
                }
                {{addonId}} .section-content .text-wrap .text-desc {
                    font-size: {{ item_content_d_fontsize.sm }}px;
                    line-height: {{ item_content_d_lineHeight.sm }}px;
                }
                {{addonId}} .section-content .image-wrap {
                    width: {{item_content_image_width.sm ? item_content_image_width.sm + "px" : "100%"}};
                    height: {{item_content_image_height.sm ? item_content_image_height.sm + "px" : "100%"}};
                    border-radius: {{ item_content_img_border_radius.sm }}px;
                }
			}
			@media (max-width: 767px) {

                <# if(is_zhong_label==1){ #>
                    {{addonId}} .section-5 .s-ul>li:after{
                        width: {{jiantou_width}}px;
                        height: {{jiantou_height}}px;
                        position: absolute;
                        left: {{jiantou_right}}px;
                        top:{{jiantou_top}}px;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;
                        background-size: 100% 100%;
                    }
                    
                    <# if(jiantou_nth_xs!=0){ #>

                        {{addonId}} .section-5 .s-li:nth-child({{jiantou_nth_xs}}):after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: {{jiantou_nthright}}px;
                            top:{{jiantou_nthtop}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(90deg);
                        }

                        {{addonId}} .section-5, {{addonId}} .section-5 .s-ul {
                            display:block;
                            flex-wrap: inherit;
                        }
                        {{addonId}} .section-5 .s-ul li{float:left;}
                        {{addonId}} .section-5 .s-ul .s-li:nth-child({{jiantou_nth_xs}}) ~ .s-li {float:right;}
                        {{addonId}} .section-5 .s-li:nth-of-type({{jiantou_nth_xs}}) ~ .s-li:after{
                            width: {{jiantou_width}}px;
                            height: {{jiantou_height}}px;
                            position: absolute;
                            left: -15px;
                            top:{{jiantou_top}}px;
                            display: block;
                            content:"";
                            background:url({{jintou_icon}})no-repeat;
                            transition: all .6s;
                            background-size: 100% 100%;
                            transform: rotate(180deg);
                        }

                        
                    <# } #>

                    {{addonId}} .section-5 .s-ul>li:last-child:after{
                        width: 0px!important;
                        height: 0px!important;
                        position: absolute;
                        right: 0;
                        top: 45%;
                        display: block;
                        content:"";
                        background:url({{jintou_icon}})no-repeat;
                        transition: all .6s;

                    }
                <# } #>


			    <# if(is_swiper == 1) { #>
                {{addonId}} .section-5 {
                    <# if(swiper_container_p && _.isObject(swiper_container_p)) { #>
                    padding: {{ swiper_container_p.xs }};
                    <# } else { #>
                    padding: {{ swiper_container_p }}px;
                    <# } #>
                }
                <# } #>
			    {{addonId}} .section-5 .s-li {
				    <# if(section_tab_m && _.isObject(section_tab_m)) { #>
					    width: calc(100% / {{ section_tab_num.xs }} - {{ section_tab_m.xs * 2 }}px);
					    <# if(is_swiper == 1) {//console.log(section_tab_item);
                        var letter = section_tab_m.xs - section_tab_m.xs / section_tab_num.xs
                        #>
                        width: calc(100% / {{ section_tab_num.xs }} - {{ letter }}px);
                        <# } #>
					<# } else { #>
					width: calc(100% / {{ section_tab_num.xs }});
					<# } #>	
				    <# if(section_tab_height.xs) { #>
					height: {{ section_tab_height.xs }}px;				
	                <# } #>
					<# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 0) { #>
                    margin: {{ section_tab_m.xs }}px;
                    <# } else if(is_swiper == 0) { #>
                    margin: {{ section_tab_m }}px;
                    <# } #>
                    <# if(section_tab_m && _.isObject(section_tab_m) && is_swiper == 1) {#>
                    margin-right: {{ section_tab_m.xs }}px;
                    <# } #>
					<# if(!is_bgImg) { #>
                        <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                        padding: {{ section_tab_p.xs }};
                        <# } else { #>
                        padding: {{ section_tab_p }}px;
                        <# } #>
	                <# } #>
                    <# if(is_shadow == 1) { #>
                    box-shadow: {{box_color}} {{box_h_shadow.xs}}px {{box_v_shadow.xs}}px {{box_blur.xs}}px {{box_spread.xs}}px;
                    <# } #>
                    <# if(is_border == 1) { #>
                    border-width: {{border_width.xs}}px;
				    border-radius: {{border_radius.xs}}px;
                    <# } #>
				}
                /*边框动画*/
                <# if(is_border == 1 && animate_border_line != "none") { #>
                    {{addonId}} .section-5 .s-li .border-line.border-line-01,
                    {{addonId}} .section-5 .s-li .border-line.border-line-03
                    {
                        height: {{border_width_hover.xs}}px;
                    }
                    {{addonId}} .section-5 .s-li .border-line.border-line-02,
                    {{addonId}} .section-5 .s-li .border-line.border-line-04
                    {
                        width: {{border_width_hover.xs}}px;
                    }
                <# } #>
				<#if(is_swiper == 1) {#>
                {{addonId}} .section-5 .s-li:nth-child({{section_tab_num.xs}}n) {
                    margin-right: 0;
                }
                <#}#>
                {{addonId}} .section-5 .item {
                    <# if(is_bgImg == 1) { #>
                        <# if(section_tab_p && _.isObject(section_tab_p)) { #>
                        padding: {{ section_tab_p.xs }};
                        <# } else { #>
                        padding: {{ section_tab_p }}px;
                        <# } #>
	                <# } #>
                }
                <# if(custom_border_width) { #>
                    <# _.each(section_tab_item, function(accordion_item, key){ 
                        // console.log("key", key);
                    #>
                        {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                            <# if(accordion_item.c_border_width && _.isObject(accordion_item.c_border_width)) { #>
                            border-width: {{ accordion_item.c_border_width.xs }};
                            <# } else { #>
                            border-width: {{ accordion_item.c_border_width }}px;
                            <# } #>
                        }
			        <# }) #>
                <# } #>
				{{addonId}} .section-5 .s-li:hover,
				{{addonId}} .section-5 .s-li.actives
				{
					<# if(is_shadow_hover == 1) { #>
					box-shadow: {{box_color_hover}} {{box_h_shadow_hover.xs}}px {{box_v_shadow_hover.xs}}px {{box_blur_hover.xs}}px {{box_spread_hover.xs}}px;
					<# } #>
					<# if(is_border_hover == 1) { #>
					border: {{border_color_hover}} {{border_style_hover}} {{border_width_hover.xs}}px;
					border-radius: {{border_radius_hover.xs}}px;
					<# } #>
				}
				{{addonId}} .section-5 .item .content-icon {
					<# if(content_style && (content_style == "row" || content_style == "row-reverse")) { #>				
						<# if(section_tab_icon_width.xs) { #>
							width: {{section_tab_icon_width.xs}}px;
						<# } else { #>
							width: 100%;
						<# } #>
						<# if(section_tab_icon_height.xs) { #>
                            height: {{section_tab_icon_height.xs}}px;
                        <# } else { #>
                            height: 100%;
                        <# } #>
					<# } else { #>
						width: 100%;
					<# } #>
					<# if(section_icon_m && _.isObject(section_icon_m)) { #>
	                margin: {{ section_icon_m.xs }};
	                <# } else { #>
	                margin: {{ section_icon_m }}px;
	                <# } #>
				}
				{{addonId}} .section-5 .item .icon-box {
					<# if(section_tab_icon_width.xs) { #>
					width: {{section_tab_icon_width.xs}}px;
					<# } else { #>
					width: 100%;
					<# } #>
					<# if(section_tab_icon_height.xs) { #>
					height: {{section_tab_icon_height.xs}}px;
					<# } else { #>
					height: 100px;
					<# } #>
				}
				{{addonId}} .section-5 .s-li:hover .icon-box,
				{{addonId}} .section-5 .s-li.actives .icon-box
				{
				    <# if(section_tab_icon_width_hover && section_tab_icon_width_hover.xs) { #>
                    width: {{section_tab_icon_width_hover.xs}}px;
                    <# } #>
                    <# if(section_tab_icon_height_hover && section_tab_icon_height_hover.xs) { #>
                    height: {{section_tab_icon_height_hover.xs}}px;
                    <# } #>
				}
				<# _.each(section_tab_item, function(accordion_item, key){ #>
                    <#if (is_swiper == 1 && (key + 1) > section_tab_num.xs){ #>
                    {{addonId}} .section-5 .s-li:nth-child({{key + 1}}) {
                        display: none;
                    }
                    <# } #>
                <# }) #>
				{{addonId}} .section-5 .item .name {
				    font-size: {{ section_tab_fontsize.xs }}px;
				    line-height: {{ section_tab_lineHeight.xs }}px;
				    <# if(section_name_m && _.isObject(section_name_m)) { #>
	                margin: {{ section_name_m.xs }};
	                <# } else { #>
	                margin: {{ section_name_m }}px;
	                <# } #>
				    <# if(section_name_p && _.isObject(section_name_p)) { #>
	                padding: {{ section_name_p.xs }};
	                <# } else { #>
	                padding: {{ section_name_p }}px;
	                <# } #>
				}
				{{addonId}} .section-5 .s-li:hover .name,
				{{addonId}} .section-5 .s-li.actives .name
				{
					<# if(section_tab_fontsize_hover) { #>
					font-size: {{ section_tab_fontsize_hover.xs }}px;
					<# } #>
				}
                {{addonId}} .section-5 .item .s-name {
                    font-size: {{ section_tab_s_name_fontsize.xs }}px;
                    line-height: {{ section_tab_s_name_lineHeight.xs }}px;
                }
                {{addonId}} .section-5 .s-li:hover .s-name,
                {{addonId}} .section-5 .s-li.actives .s-name
                {
                    <# if(section_tab_s_name_fontsize_hover) { #>
                    font-size: {{ section_tab_fontsize_hover.xs }}px;
                    <# } #>
                }
				{{addonId}} .section-5 .item .line {
                    width: {{ line_border_width.xs }}px;
                    <# if(line_border_color_style == "color") { #>
                        border-bottom: {{ line_border_color }} {{ line_border_style }} {{ line_border_height.xs }}px;
                    <# } #>
                    <# if(line_border_color_style == "gradient") { #>
                        height: {{line_border_height.xs}}px;
				    <# } #>
                    <# if(section_line_m && _.isObject(section_line_m)) { #>
                    margin-bottom: {{ section_line_m.xs }}px;
                    <# } else { #>
                    margin-bottom: {{ section_line_m }}px;
                    <# } #>
                }
                {{addonId}} .section-5 .s-li:hover .line,
                {{addonId}} .section-5 .s-li.actives .line
                {
                    width: {{ line_border_width_hover.xs }}px;
                    <# if(line_border_color_style == "color") { #>
                        border-bottom: {{ line_border_color_hover }} {{ line_border_style_hover }} {{ line_border_height_hover.xs }}px;
                    <# } #>
                    <# if(line_border_color_style == "gradient") { #>
                        height: {{line_border_height_hover.xs}}px;
				    <# } #>
                }
				{{addonId}} .section-5 .item .desc {
                    font-size: {{ section_desc_fontsize.xs }}px;
                    line-height: {{ section_desc_lineHeight.xs }}px;
                    <# if(section_desc_p && _.isObject(section_desc_p)) { #>
                    padding: {{ section_desc_p.xs }};
                    <# } else { #>
                    padding: {{ section_desc_p }}px;
                    <# } #>
                    <# if(section_desc_height) { #>
                    height: {{ section_desc_height.xs }}px;
                    <# } else { #>
                    /*height: auto;*/
                    <# } #>
                }
                {{addonId}} .section-5 .s-li:hover .desc,
                {{addonId}} .section-5 .s-li.actives .desc
                {
					<# if(section_desc_fontsize_hover) { #>
					font-size: {{ section_desc_fontsize_hover.xs }}px;
					<# } #>
				}
				/* 更多按钮 */
                {{addonId}} .section-5 .item .more-box {
                    <# if(more_icon_m && _.isObject(more_icon_m)) { #>
                    padding: {{ more_icon_m.xs }};
                    <# } else { #>
                    padding: {{ more_icon_m }}px;
                    <# } #>
                }
                {{addonId}} .section-5 .item .more-icon {
                    <# if(more_icon_width) { #>
                    width: {{ more_icon_width.xs }}px
                    <# } else { #>
                    <# } #>
                }
				/* 切换 配置样式 */
                {{addonId}} .swiper-button {
                    top: {{swiper_button_top.xs}}{{swiper_button_top_unit}};
                }
                {{addonId}} .swiper-button:after {
                    width: {{swiper_button_width.xs}}px;
                    height: {{swiper_button_height.xs}}px;
                }
                {{addonId}} .swiper-button-prev {
                    left: {{swiper_button_left.xs}}px;
                }
                {{addonId}} .swiper-button-next {
                    right: {{swiper_button_left.xs}}px;
                }
                /* 选项卡 */
                {{addonId}} .menu-tab-box {
                    border-width: {{ tab_box_border_width.xs || "0px" }};
                    border-radius: {{ tab_box_border_radius.xs || 0 }};
                }
                {{addonId}} .menu-tab-box .menu-tab-item {
                    font-size: {{ tab_item_fontsize.xs }}px;
			        line-height: {{ tab_item_height.xs }}px;
                    width: calc(100% / {{ change_tab_num.xs }});
                }
                {{addonId}} .menu-tab-box .menu-tab-item span:after {
                    bottom: -{{ tab_item_bor_h_active.xs }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item:hover {
                    font-size: {{ tab_item_fontsize_hover.xs }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item.actives {
                    font-size: {{ tab_item_fontsize_active.xs }}px;
                }
                {{addonId}} .menu-tab-box .menu-tab-item.actives span:after {			    
                    height: {{ tab_item_bor_h_active.xs }}px;
                }
                /* 内容部分样式 */
                {{addonId}} .section-content {
			        flex-direction: {{item_content_style_xs}};
                    <# if(item_content_p && _.isObject(item_content_p)) { #>
                    padding: {{ item_content_p.xs }};
                    <# } #>
                }
                {{addonId}} .section-content .content-line { 
                    <# 
                        var lineW_xs = "";
                        if (item_content_style_xs == "row" || item_content_style_xs == "row-reverse") { 
                            lineW_xs = "width";
                        } else {
                            lineW_xs = "height";
                        } 
                    #>
                    {{lineW_xs}}: {{ item_content_between.xs }}px;
                }
                {{addonId}} .section-content .text-wrap .text-title {
                    font-size: {{ item_content_t_fontsize.xs }}px;
                    line-height: {{ item_content_t_lineHeight.xs }}px;
                    margin-bottom: {{ item_content_t_mb.xs }}px;
                }
                {{addonId}} .section-content .text-wrap .text-desc {
                    font-size: {{ item_content_d_fontsize.xs }}px;
                    line-height: {{ item_content_d_lineHeight.xs }}px;
                }
                {{addonId}} .section-content .image-wrap {
                    width: {{item_content_image_width.xs ? item_content_image_width.xs + "px" : "100%"}};
                    height: {{item_content_image_height.xs ? item_content_image_height.xs + "px" : "100%"}};
                    border-radius: {{ item_content_img_border_radius.xs }}px;
                }
			}
		</style>
		<# if(is_tab == 1 && change_tab_item.length > 0) { #>
		<div class="menu-tab-box">
		    <# _.each(change_tab_item, function(tab_item, key){ 
		        var active = "";
                if(key == (section_tab_active - 1)) {
                    active = "actives";
                }
		    #>
                <div class="menu-tab-item {{active}}"><span>{{ tab_item.title }}</span></div>
            <# }); #>
		</div>
		<# } #>
		<# if(is_swiper == 1) { #>
		<p class="alert alert-warning" style="margin-bottom: 10px;padding:.75rem 1.25rem;">编辑页仅为布局样式，请在预览页面中查看该插件切换效果</p>
		<# } #>
		<# if(is_tab == 1 && change_tab_item.length > 0) { #>
		<# _.each(change_tab_item, function(tab_item, tab_key){ 
		    var display = "none";
		    if(tab_key == (section_tab_active - 1)) {
		        display = "block";
		    }
		#>
            <div class="section-5-box" style="display: {{display}};">
                <div class="section-5">
                    <ul class="s-ul">
                    <# _.each(section_tab_item, function(accordion_item, key){ 
                        var actives = "";
                        if(data.is_item_active == 1 && key == 0){
                            actives = "actives"
                        }
                    #>
                        <# if(tab_item.start_item_num && tab_item.end_item_num && ((key + 1) >= tab_item.start_item_num && (key + 1) <= tab_item.end_item_num)) { #>
                        <li class="s-li {{actives}}">
                            <a class="item {{actives}}">
                                <# if(accordion_item.icon && content_icon_position == "type01") { #>
                                <div class="content-icon" >
                                    <i class="icon-box jwpf-animated "></i>
                                </div>
                                <# } #>
                                <# if(is_show_name == 1 || is_show_desc == 1){ #>
                                    <div class="info">
                                        <# if(is_show_name_line == 1 && line_border_position == "type02"){ #>
                                            <div class="line-box">
                                                <span class="line"></span>
                                            </div>
                                        <# } #>
                                        <# if(is_show_name == 1){ #>
                                            <# if(content_icon_position == "type03"){ #>
                                            <div class="flex">
                                                <div class="content-icon" style="width: auto;">
                                                    <i class="icon-box jwpf-animated "></i>
                                                </div>
                                            <# } #>
                                            <p class="name"<# if(content_icon_position == "type03"){ #> style="width: auto;"<# } #>><b>{{accordion_item.botname}}</b>{{accordion_item.name}}</p>
                                            <# if(content_icon_position == "type03"){ #>
                                            </div>
                                            <# } #>
                                        <# } #>
                                        <# if(data.is_show_s_name == 1){ #>
                                            <p class="s-name">{{{accordion_item.s_name}}}</p>
                                        <# } #>
                                        <# if(accordion_item.icon && content_icon_position == "type02") { #>
                                            <div class="content-icon">
                                                <i class="icon-box jwpf-animated "></i>
                                            </div>
                                        <# } #>
                                        <# if(is_show_name_line == 1 && line_border_position == "type01"){ #>
                                            <div class="line-box">
                                                <span class="line"></span>
                                            </div>
                                        <# } #>
                                        <# if(is_show_desc == 1){ #>
                                            <p class="desc">{{{accordion_item.desc}}}</p>
                                        <# } #>
                                        <# if(is_show_more == 1){ #>
                                            <div class="more-box">
                                                <!--<span class="more-icon"></span>-->
                                                <img src=\'{{ more_icon }}\' alt="" class="more-icon" />
                                            </div>
                                        <# } #>
                                    </div>
                                <# } #>
                            </a>
                            <# if(animate_border_line != "none") { #>
                            <div class="border-line border-line-01"></div><!--上-->
                            <div class="border-line border-line-02"></div><!--右-->
                            <div class="border-line border-line-03"></div><!--下-->
                            <div class="border-line border-line-04"></div><!--左-->
                            <# } #>
                        </li>
                        <# } #>
                    <# }); #>
                    </ul>
                </div>
                <# if(is_swiper == 1 && is_swiper_button == 1) { #>
                <div class="swiper-button swiper-button-prev"></div><!--左箭头。如果放置在swiper-container外面，需要自定义样式。-->
                <div class="swiper-button swiper-button-next"></div><!--右箭头。如果放置在swiper-container外面，需要自定义样式。-->
                <# } #>
                <# if(data.is_show_content == 1) { #>
                    <# _.each(section_tab_item, function(accordion_item, key){ 
                        var show = "";
                        if(key != (section_tab_active - 1)){
                            show = "hidden"
                        }
                    #>
                    <div class="section-content-box {{show}}">
                        <div class="section-content">
                            <div class="text-wrap">
                                <div class="text-title">{{{ accordion_item.content_title }}}</div>
                                <div class="text-desc">{{{ accordion_item.content_desc }}}</div>
                            </div>
                            <div class="content-line"></div>
                            <div class="image-wrap">
                            <# if(accordion_item.content_img) { #>
                                <img src=\'{{ accordion_item.content_img }}\' class="content-img" />
                            <# } #>
                            </div>
                        </div>
                    </div>
                    <# }); #>
                <# } #>
            </div>
        <# }); #>
		<# } else { #>
        <div class="section-5">
            <ul class="s-ul">
            <# _.each(section_tab_item, function(accordion_item, key){ 
                var actives = "";
                if(data.is_item_active == 1 && key == (section_tab_active - 1)){
                    actives = "actives"
                }
            #>
                <li class="s-li {{actives}}">
                    <a class="item {{actives}}">
                        <# if(accordion_item.icon && content_icon_position == "type01") { #>
                        <div class="content-icon">
                            <i class="icon-box jwpf-animated "></i>
                        </div>
                        <# } #>
                        <# if(is_show_name == 1 || is_show_desc == 1){ #>
	                        <div class="info">
	                            <# if(is_show_name_line == 1 && line_border_position == "type02"){ #>
                                    <div class="line-box">
                                        <span class="line"></span>
                                    </div>
	                            <# } #>
                                <# if(is_show_name == 1){ #>
                                    <# if(content_icon_position == "type03"){ #>
                                    <div class="flex">
                                        <div class="content-icon" style="width: auto;">
                                            <i class="icon-box jwpf-animated "></i>
                                        </div>
                                    <# } #>
                                    <p class="name"<# if(content_icon_position == "type03"){ #> style="width: auto;"<# } #>><b>{{accordion_item.botname}}</b>{{accordion_item.name}}</p>
                                    <# if(content_icon_position == "type03"){ #>
                                    </div>
                                    <# } #>
                                <# } #>
	                            <# if(data.is_show_s_name == 1){ #>
	                                <p class="s-name">{{{accordion_item.s_name}}}</p>
	                            <# } #>
	                            <# if(accordion_item.icon && content_icon_position == "type02") { #>
                                    <div class="content-icon">
                                        <i class="icon-box jwpf-animated "></i>
                                    </div>
                                <# } #>
	                            <# if(is_show_name_line == 1 && line_border_position == "type01"){ #>
                                    <div class="line-box">
                                        <span class="line"></span>
                                    </div>
	                            <# } #>
	                            <# if(is_show_desc == 1){ #>
	                                <p class="desc">{{{accordion_item.desc}}}</p>
	                            <# } #>
	                            <# if(is_show_more == 1){ #>
                                    <div class="more-box">
                                        <!--<span class="more-icon"></span>-->
                                        <img src=\'{{ more_icon }}\' alt="" class="more-icon" />
                                    </div>
	                            <# } #>
	                        </div>
                        <# } #>
                    </a>
                    <# if(animate_border_line != "none") { #>
                    <div class="border-line border-line-01"></div><!--上-->
                    <div class="border-line border-line-02"></div><!--右-->
                    <div class="border-line border-line-03"></div><!--下-->
                    <div class="border-line border-line-04"></div><!--左-->
                    <# } #>
                </li>
            <# }); #>
            </ul>
		</div>
        <# if(is_swiper == 1 && is_swiper_button == 1) { #>
        <div class="swiper-button swiper-button-prev"></div><!--左箭头。如果放置在swiper-container外面，需要自定义样式。-->
        <div class="swiper-button swiper-button-next"></div><!--右箭头。如果放置在swiper-container外面，需要自定义样式。-->
        <# } #>
		<# if(data.is_show_content == 1) { #>
            <# _.each(section_tab_item, function(accordion_item, key){ 
                var show = "";
                if(key != (section_tab_active - 1)){
                    show = "hidden"
                }
            #>
            <div class="section-content-box {{show}}">
                <div class="section-content">
                    <div class="text-wrap">
                        <div class="text-title">{{{ accordion_item.content_title }}}</div>
                        <div class="text-desc">{{{ accordion_item.content_desc }}}</div>
                    </div>
                    <div class="content-line"></div>
                    <div class="image-wrap">
                    <# if(accordion_item.content_img) { #>
                        <img src=\'{{ accordion_item.content_img }}\' class="content-img" />
                    <# } #>
                    </div>
                </div>
            </div>
            <# }); #>
        <# } #>
		<# } #>
		';

        return $output;
    }
}
