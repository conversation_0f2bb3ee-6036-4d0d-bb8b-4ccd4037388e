<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonImage_carousel extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;
		$class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
		$image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : 'layout2';
		$carousel_autoplay = (isset($settings->carousel_autoplay) && $settings->carousel_autoplay) ? $settings->carousel_autoplay : 0;
		$carousel_speed = (isset($settings->carousel_speed) && $settings->carousel_speed) ? $settings->carousel_speed : 2500;
		$carousel_interval = (isset($settings->carousel_interval) && $settings->carousel_interval) ? $settings->carousel_interval : 4500;
		$carousel_margin = (isset($settings->carousel_margin) && $settings->carousel_margin) ? $settings->carousel_margin : 0;
		$carousel_center_padding = (isset($settings->carousel_center_padding) && $settings->carousel_center_padding) ? $settings->carousel_center_padding : 180;
		$carousel_center_padding_sm = (isset($settings->carousel_center_padding_sm) && $settings->carousel_center_padding_sm) ? $settings->carousel_center_padding_sm : 90;
		$carousel_center_padding_xs = (isset($settings->carousel_center_padding_xs) && $settings->carousel_center_padding_xs) ? $settings->carousel_center_padding_xs : 50;
		$carousel_item_number = (isset($settings->carousel_item_number) && $settings->carousel_item_number) ? $settings->carousel_item_number : 3;
		$carousel_item_number_sm = (isset($settings->carousel_item_number_sm) && $settings->carousel_item_number_sm) ? $settings->carousel_item_number_sm : 3;
		$carousel_item_number_xs = (isset($settings->carousel_item_number_xs) && $settings->carousel_item_number_xs) ? $settings->carousel_item_number_xs : 1;
		$carousel_height = (isset($settings->carousel_height) && $settings->carousel_height) ? $settings->carousel_height : 500;
		$carousel_height_sm = (isset($settings->carousel_height_sm) && $settings->carousel_height_sm) ? $settings->carousel_height_sm : 400;
		$carousel_height_xs = (isset($settings->carousel_height_xs) && $settings->carousel_height_xs) ? $settings->carousel_height_xs : 300;

		$carousel_bullet = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 0;
		$carousel_overlay = (isset($settings->carousel_overlay) && $settings->carousel_overlay) ? $settings->carousel_overlay : 0;
		$carousel_fade = (isset($settings->carousel_fade) && $settings->carousel_fade) ? $settings->carousel_fade : 0;
        $carousel_radius = (isset($settings->carousel_radius) && $settings->carousel_radius) ?  $settings->carousel_radius : "0";
        $image_zsy_w_h = (isset($settings->image_zsy_w_h) && $settings->image_zsy_w_h) ?  $settings->image_zsy_w_h : "";
        $image_style = (isset($settings->image_style) && $settings->image_style) ?  $settings->image_style : "none";

        //Arrow style
		$carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 0;
		$arrow_icon = (isset($settings->arrow_icon) && $settings->arrow_icon) ? $settings->arrow_icon : 'angle';
		$left_arrow = '';
		$right_arrow = '';
		if ($arrow_icon == 'long_arrow') {
			$left_arrow = 'fa-long-arrow-left';
			$right_arrow = 'fa-long-arrow-right';
		} else {
			$left_arrow = 'fa-angle-left';
			$right_arrow = 'fa-angle-right';
		}

		//Output
		$output = '<div  id="lbwidth" class="jwpf-addon jwpf-carousel-extended' . $class . ' jwpf-image-carousel-' . $image_carousel_layout . '" data-left-arrow="' . $left_arrow . '" data-right-arrow="' . $right_arrow . '" data-arrow="' . $carousel_arrow . '" data-dots="' . $carousel_bullet . '" data-image-layout="' . $image_carousel_layout . '" data-autoplay="' . $carousel_autoplay . '" data-speed="' . $carousel_speed . '" data-interval="' . $carousel_interval . '" data-margin="' . $carousel_margin . '" ' . (($image_carousel_layout == 'layout3' || $image_carousel_layout == 'layout4') ? 'data-padding="' . $carousel_center_padding . '"' : '') . ' ' . (($image_carousel_layout == 'layout3' || $image_carousel_layout == 'layout4') ? 'data-padding-sm="' . $carousel_center_padding_sm . '"' : '') . ' ' . (($image_carousel_layout == 'layout3' || $image_carousel_layout == 'layout4') ? 'data-padding-xs="' . $carousel_center_padding_xs . '"' : '') . ' data-height="' . $carousel_height . '" data-height-sm="' . $carousel_height_sm . '" data-height-xs="' . $carousel_height_xs . '" data-item-number="' . $carousel_item_number . '" data-item-number-sm="' . $carousel_item_number_sm . '" data-item-number-xs="' . $carousel_item_number_xs . '"' . ($image_carousel_layout == 'layout1' && $carousel_fade ? ' data-fade="' . $carousel_fade . '"' : '') . '>';
		if (isset($settings->jw_image_carousel_item) && is_array($settings->jw_image_carousel_item)) {
            $arr = [];
			foreach ($settings->jw_image_carousel_item as $item_key => $carousel_item) {

				// 2021.09.16
                // 轮播来源
                $text_from = (isset($carousel_item->text_from) && $carousel_item->text_from) ? $carousel_item->text_from : 0;
                // 轮播内容
                $text_id = (isset($carousel_item->text_id) && $carousel_item->text_id) ? $carousel_item->text_id : null;
                // 2021.09.16
                if($carousel_item->text_from && $carousel_item->text_from == 1 && $carousel_item->text_id) {
                    $infos = JwPageFactoryBase::getBannerById($text_id);
                    if($infos){
                        $carousel_item->image_carousel_img=$infos->image_intro;
                        $carousel_item->item_title=$infos->banner_title;
                        $carousel_item->item_subtitle=$infos->second_title;
                        $carousel_item->item_description=$infos->introtext;
                    }
                }

                $arr[] = $carousel_item->image_carousel_img;
				$output .= '<div class="jwpf-carousel-extended-item">';
				if (isset($carousel_item->image_carousel_img_link) && $carousel_item->image_carousel_img_link) {
					$output .= '<a href="' . $carousel_item->image_carousel_img_link . '" ' . ($carousel_item->link_open_new_window ? ' rel="noopener noreferrer" target="_blank"' : '') . '>';
				}
                $output .= '<img style="object-fit:'.$image_style.'"  src="' . $carousel_item->image_carousel_img . '" id="oneimg" alt="' . (isset($carousel_item->item_title) ? $carousel_item->item_title : '') . '">';
				if ($carousel_overlay) {
					$output .= '<div class="jwpf-carousel-extended-item-overlay"></div>';
				}
				if ($image_carousel_layout != '') {
					$output .= '<div class="jwpf-carousel-extended-content-wrap">';
					if (isset($carousel_item->item_title)) {
						$output .= '<div class="jwpf-carousel-extended-heading">';
						$output .= $carousel_item->item_title;
						$output .= '</div>';
					}
					if (isset($carousel_item->item_subtitle)) {
						$output .= '<div class="jwpf-carousel-extended-subheading">';
						$output .= $carousel_item->item_subtitle;
						$output .= '</div>';
					}
					if (isset($carousel_item->item_description)) {
						$output .= '<div class="jwpf-carousel-extended-description">';
						$output .= $carousel_item->item_description;
						$output .= '</div>';
					}
					$output .= '</div>';
				}
				if (isset($carousel_item->image_carousel_img_link) && $carousel_item->image_carousel_img_link) {
					$output .= '</a>';
				}
				$output .= '</div>';
			}
		}
        if($image_zsy_w_h != '')
        {
            $output .='
                    <script>
                    window.onload=function(){
                        var content = document.getElementById("lbwidth");
                    var str = content.offsetWidth/'.$image_zsy_w_h.';
                    document.querySelector(".jwpf-carousel-extended-list").style.height=+str+"px";
                    document.querySelector(".jwpf-carousel-extended-outer-stage").style.height=+str+"px";
                    }
                    
                    </script>
            ';
        }
		$output .= '</div>';

		return $output;

	}

	public function scripts()
	{
		return array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/jw_carousel.js');
	}

	public function css()
	{
		$settings = $this->addon->settings;
		$css = '';
		$image_carousel_layout = (isset($settings->image_carousel_layout) && $settings->image_carousel_layout) ? $settings->image_carousel_layout : '';
		$overlay_gradient = (isset($settings->overlay_gradient) && $settings->overlay_gradient) ? $settings->overlay_gradient : '';

		//Arrow Style
		$carousel_arrow = (isset($settings->carousel_arrow) && $settings->carousel_arrow) ? $settings->carousel_arrow : 1;
		$arrow_style = '';
		$arrow_style .= (isset($settings->arrow_height) && $settings->arrow_height) ? "height: " . $settings->arrow_height . "px;" : "";
		$arrow_style .= (isset($settings->arrow_height) && $settings->arrow_height) ? "line-height: " . (($settings->arrow_height) - ($settings->arrow_border_width) * 2 ) . "px;" : "";

		$arrow_style .= (isset($settings->arrow_width) && $settings->arrow_width) ? "width: " . $settings->arrow_width . "px;" : "";
		$arrow_style .= (isset($settings->arrow_background) && $settings->arrow_background) ? "background: " . $settings->arrow_background . ";" : "";
		$arrow_style .= (isset($settings->arrow_color) && $settings->arrow_color) ? "color: " . $settings->arrow_color . ";" : "";
		$arrow_style .= (isset($settings->arrow_font_size) && $settings->arrow_font_size) ? "font-size: " . $settings->arrow_font_size . "px;" : "";
		$arrow_style .= (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? "border-width: " . $settings->arrow_border_width . "px;" : "";
		$arrow_style .= (isset($settings->arrow_border_width) && $settings->arrow_border_width) ? "border-style: solid;" : "";
		$arrow_style .= (isset($settings->arrow_border_color) && $settings->arrow_border_color) ? "border-color: " . $settings->arrow_border_color . ";" : "";
		$arrow_style .= (isset($settings->arrow_border_radius) && $settings->arrow_border_radius) ? "border-radius: " . $settings->arrow_border_radius . "px;" : "";
		//Arrow hover style
		$arrow_hover_style = '';
		$arrow_hover_style .= (isset($settings->arrow_hover_background) && $settings->arrow_hover_background) ? "background-color: " . $settings->arrow_hover_background . ";" : "";
		$arrow_hover_style .= (isset($settings->arrow_hover_color) && $settings->arrow_hover_color) ? "color: " . $settings->arrow_hover_color . ";" : "";
		$arrow_hover_style .= (isset($settings->arrow_hover_border_color) && $settings->arrow_hover_border_color) ? "border-color: " . $settings->arrow_hover_border_color . ";" : "";

		if ($carousel_arrow) {
			if ($arrow_style) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-nav-control .nav-control {';
				$css .= $arrow_style;
				if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti != '') {
					$css .= 'margin-top: ' . $settings->arrow_position_verti . '%;';
				}
				if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori != '') {
					$css .= 'margin-left: ' . $settings->arrow_position_hori . 'px;';
				}
				if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori != '') {
					$css .= 'margin-right: ' . $settings->arrow_position_hori . 'px;';
				}
				$css .= '}';
			}

			if ($arrow_hover_style) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-nav-control .nav-control:hover{';
				$css .= $arrow_hover_style;
				$css .= '}';
			}
		}
		//Bullet Style
		$carousel_bullet = (isset($settings->carousel_bullet) && $settings->carousel_bullet) ? $settings->carousel_bullet : 1;
		$bullet_style = '';
		$bullet_style .= (isset($settings->bullet_height) && $settings->bullet_height) ? "height: " . $settings->bullet_height . "px;" : "";
		$bullet_style .= (isset($settings->bullet_height) && $settings->bullet_height) ? "line-height: " . (($settings->bullet_height) - ($settings->bullet_border_width)) . "px;" : "";

		$bullet_style .= (isset($settings->bullet_width) && $settings->bullet_width) ? "width: " . $settings->bullet_width . "px;" : "";
		$bullet_style .= (isset($settings->bullet_background) && $settings->bullet_background) ? "background: " . $settings->bullet_background . ";" : "";
		$bullet_style .= (isset($settings->bullet_border_width) && $settings->bullet_border_width) ? "border-width: " . $settings->bullet_border_width . "px;" : "";
		$bullet_style .= (isset($settings->bullet_border_width) && $settings->bullet_border_width) ? "border-style: solid;" : "";
		$bullet_style .= (isset($settings->bullet_border_color) && $settings->bullet_border_color) ? "border-color: " . $settings->bullet_border_color . ";" : "";
		$bullet_style .= (isset($settings->bullet_border_radius) && $settings->bullet_border_radius != '') ? "border-radius: " . $settings->bullet_border_radius . "px;" : "";
		//Bullet hover style
		$bullet_active_style = (isset($settings->bullet_active_background) && $settings->bullet_active_background) ? "background: " . $settings->bullet_active_background . ";" : "";

		if ($carousel_bullet) {
			if ($bullet_style) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots ul li {';
				$css .= $bullet_style;
				$css .= '}';
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots {';
				if (isset($settings->bullet_position_verti) && $settings->bullet_position_verti != '') {
					$css .= 'bottom: ' . $settings->bullet_position_verti . '%;';
				}
				if (isset($settings->bullet_position_hori) && $settings->bullet_position_hori != '') {
					$css .= 'left: ' . $settings->bullet_position_hori . 'px;';
				}
				$css .= '}';
			}

			if ($bullet_active_style) {
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots ul li:hover span,';
				$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots ul li.active span{';
				$css .= $bullet_active_style;
				$css .= '}';
			}
		}

		//Overaly
		$gradient_color1 = (isset($overlay_gradient->color) && $overlay_gradient->color) ? $overlay_gradient->color : 'rgba(59, 25, 208, 0.5)';
		$gradient_color2 = (isset($overlay_gradient->color2) && $overlay_gradient->color2) ? $overlay_gradient->color2 : 'rgba(255, 79, 226, 0.5)';
		$degree = $overlay_gradient->deg;
		$type = $overlay_gradient->type;
		$radialPos = (isset($overlay_gradient->radialPos) && $overlay_gradient->radialPos) ? $overlay_gradient->radialPos : 'Center Center';
		$radial_angle1 = (isset($overlay_gradient->pos) && $overlay_gradient->pos) ? $overlay_gradient->pos : '2';
		$radial_angle2 = (isset($overlay_gradient->pos2) && $overlay_gradient->pos2) ? $overlay_gradient->pos2 : '99';
		$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-item-overlay {';
		if ($type !== 'radial') {
			$css .= 'background: -webkit-linear-gradient(' . $degree . 'deg, ' . $gradient_color1 . ' ' . $radial_angle1 . '%, ' . $gradient_color2 . ' ' . $radial_angle2 . '%) transparent;';
			$css .= 'background: linear-gradient(' . $degree . 'deg, ' . $gradient_color1 . ' ' . $radial_angle1 . '%, ' . $gradient_color2 . ' ' . $radial_angle2 . '%) transparent;';
		} else {
			$css .= 'background: -webkit-radial-gradient(at ' . $radialPos . ', ' . $gradient_color1 . ' ' . $radial_angle1 . '%, ' . $gradient_color2 . ' ' . $radial_angle2 . '%) transparent;';
			$css .= 'background: radial-gradient(at ' . $radialPos . ', ' . $gradient_color1 . ' ' . $radial_angle1 . '%, ' . $gradient_color2 . ' ' . $radial_angle2 . '%) transparent;';
		}
		$css .= '}';

		//Content Alignment
		$item_content_verti_align = (isset($settings->item_content_verti_align) && $settings->item_content_verti_align) ? $settings->item_content_verti_align : "";
		$item_content_hori_align = (isset($settings->item_content_hori_align) && $settings->item_content_hori_align) ? $settings->item_content_hori_align : "";

		if ($item_content_verti_align || $item_content_hori_align) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-content-wrap {';
			if ($item_content_verti_align === "top") {
				$css .= 'justify-content: flex-start;';
			} else if ($item_content_verti_align === "bottom") {
				$css .= 'justify-content: flex-end;';
			}
			if ($item_content_hori_align === "left") {
				$css .= 'align-items: flex-start;';
				$css .= 'text-align: left;';
			} else if ($item_content_hori_align === "right") {
				$css .= 'align-items: flex-end;';
				$css .= 'text-align: right;';
			}
			$css .= '}';
		}

		//Content title style
		$title_style = '';
		$title_style .= (isset($settings->content_title_fontsize) && $settings->content_title_fontsize) ? 'font-size:' . $settings->content_title_fontsize . 'px;' : '';
		$title_style .= (isset($settings->content_title_lineheight) && $settings->content_title_lineheight) ? 'line-height:' . $settings->content_title_lineheight . 'px;' : '';
		$title_style .= (isset($settings->content_title_letterspace) && $settings->content_title_letterspace) ? 'letter-spacing:' . $settings->content_title_letterspace . ';' : '';
		$title_style .= (isset($settings->content_title_text_color) && $settings->content_title_text_color) ? 'color:' . $settings->content_title_text_color . ';' : '';
		$title_style .= (isset($settings->content_title_margin) && trim($settings->content_title_margin)) ? 'margin:' . $settings->content_title_margin . ';' : '';

		$content_title_font_style = (isset($settings->content_title_font_style) && $settings->content_title_font_style) ? $settings->content_title_font_style : '';
		if (isset($content_title_font_style->underline) && $content_title_font_style->underline) {
			$title_style .= 'text-decoration:underline;';
		}
		if (isset($content_title_font_style->italic) && $content_title_font_style->italic) {
			$title_style .= 'font-style:italic;';
		}
		if (isset($content_title_font_style->uppercase) && $content_title_font_style->uppercase) {
			$title_style .= 'text-transform:uppercase;';
		}
		if (isset($content_title_font_style->weight) && $content_title_font_style->weight) {
			$title_style .= 'font-weight:' . $content_title_font_style->weight . ';';
		}

		if ($title_style) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-heading {';
			$css .= $title_style;
			$css .= '}';
		}

		//Content subtitle style
		$subtile_style = '';
		$subtile_style .= (isset($settings->content_subtitle_fontsize) && $settings->content_subtitle_fontsize) ? 'font-size:' . $settings->content_subtitle_fontsize . 'px;' : '';
		$subtile_style .= (isset($settings->content_subtitle_lineheight) && $settings->content_subtitle_lineheight) ? 'line-height:' . $settings->content_subtitle_lineheight . 'px;' : '';
		$subtile_style .= (isset($settings->content_subtitle_letterspace) && $settings->content_subtitle_letterspace) ? 'letter-spacing:' . $settings->content_subtitle_letterspace . ';' : '';
		$subtile_style .= (isset($settings->content_subtitle_text_color) && $settings->content_subtitle_text_color) ? 'color:' . $settings->content_subtitle_text_color . ';' : '';

		$content_subtitle_font_style = (isset($settings->content_subtitle_font_style) && $settings->content_subtitle_font_style) ? $settings->content_subtitle_font_style : '';

		if (isset($content_subtitle_font_style->underline) && $content_subtitle_font_style->underline) {
			$subtile_style .= 'text-decoration:underline;';
		}
		if (isset($content_subtitle_font_style->italic) && $content_subtitle_font_style->italic) {
			$subtile_style .= 'font-style:italic;';
		}
		if (isset($content_subtitle_font_style->uppercase) && $content_subtitle_font_style->uppercase) {
			$subtile_style .= 'text-transform:uppercase;';
		}
		if (isset($content_subtitle_font_style->weight) && $content_subtitle_font_style->weight) {
			$subtile_style .= 'font-weight:' . $content_subtitle_font_style->weight . ';';
		}

		if ($subtile_style) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-subheading {';
			$css .= $subtile_style;
			$css .= '}';
		}

		//Descrition style
		$description_style = '';
		$description_style .= (isset($settings->description_fontsize) && $settings->description_fontsize) ? 'font-size:' . $settings->description_fontsize . 'px;' : '';
		$description_style .= (isset($settings->description_lineheight) && $settings->description_lineheight) ? 'line-height:' . $settings->description_lineheight . 'px;' : '';
		$description_style .= (isset($settings->description_letterspace) && $settings->description_letterspace) ? 'letter-spacing:' . $settings->description_letterspace . ';' : '';
		$description_style .= (isset($settings->description_text_color) && $settings->description_text_color) ? 'color:' . $settings->description_text_color . ';' : '';
		$description_style .= (isset($settings->description_margin) && trim($settings->description_margin)) ? 'margin:' . $settings->description_margin . ';' : '';

		$description_font_style = (isset($settings->description_font_style) && $settings->description_font_style) ? $settings->description_font_style : '';
		if (isset($description_font_style->underline) && $description_font_style->underline) {
			$description_style .= 'text-decoration:underline;';
		}
		if (isset($description_font_style->italic) && $description_font_style->italic) {
			$description_style .= 'font-style:italic;';
		}
		if (isset($description_font_style->uppercase) && $description_font_style->uppercase) {
			$description_style .= 'text-transform:uppercase;';
		}
		if (isset($description_font_style->weight) && $description_font_style->weight) {
			$description_style .= 'font-weight:' . $description_font_style->weight . ';';
		}

		if ($description_style) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-description {';
			$css .= $description_style;
			$css .= '}';
		}

		//Carousel transition speed
		$carousel_speed = (isset($settings->carousel_speed)) ? $settings->carousel_speed : 2500;
		if ($image_carousel_layout == 'layout3' || $image_carousel_layout == 'layout4') {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-center .jwpf-carousel-extended-item .jwpf-addon-wrapper {';
			$css .= 'transition: all ' . $carousel_speed . 'ms ease 0s;';
			$css .= '}';
		}
		$arrow_height = (isset($settings->arrow_height) && $settings->arrow_height) ? $settings->arrow_height : "";
		if ($arrow_height) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-nav-control {';
			$css .= 'top: -' . $arrow_height . 'px;';
			$css .= '}';
		}

		//Responsive tablet
		$title_style_sm = '';
		$title_style_sm .= (isset($settings->content_title_fontsize_sm) && $settings->content_title_fontsize_sm) ? 'font-size:' . $settings->content_title_fontsize_sm . 'px;' : '';
		$title_style_sm .= (isset($settings->content_title_margin_sm) && trim($settings->content_title_margin_sm)) ? 'margin:' . $settings->content_title_margin_sm . ';' : '';

		$subtile_style_sm = '';
		$subtile_style_sm .= (isset($settings->content_subtitle_fontsize_sm) && $settings->content_subtitle_fontsize_sm) ? 'font-size:' . $settings->content_subtitle_fontsize_sm . 'px;' : '';

		$description_style_sm = '';
		$description_style_sm .= (isset($settings->description_fontsize_sm) && $settings->description_fontsize_sm) ? 'font-size:' . $settings->description_fontsize_sm . 'px;' : '';
		$description_style_sm .= (isset($settings->description_margin_sm) && trim($settings->description_margin_sm)) ? 'margin:' . $settings->description_margin_sm . ';' : '';

		$css .= '@media (min-width: 768px) and (max-width: 991px) {';

		$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots {';
		if (isset($settings->bullet_position_verti) && $settings->bullet_position_verti_sm) {
			$css .= 'bottom: ' . $settings->bullet_position_verti_sm . '%;';
		}
		if (isset($settings->bullet_position_hori) && $settings->bullet_position_hori_sm) {
			$css .= 'left: ' . $settings->bullet_position_hori_sm . 'px;';
		}
		$css .= '}';
		$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-nav-control .nav-control {';
		$css .= $arrow_style;
		if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti_sm) {
			$css .= 'margin-top: ' . $settings->arrow_position_verti_sm . '%;';
		}
		if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori_sm) {
			$css .= 'margin-left: ' . $settings->arrow_position_hori_sm . 'px;';
		}
		if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori_sm) {
			$css .= 'margin-right: ' . $settings->arrow_position_hori_sm . 'px;';
		}
		$css .= '}';

		if ($title_style_sm) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-heading {';
			$css .= $title_style_sm;
			$css .= '}';
		}
		if ($subtile_style_sm) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-subheading {';
			$css .= $subtile_style_sm;
			$css .= '}';
		}

		if ($description_style_sm) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-description {';
			$css .= $description_style_sm;
			$css .= '}';
		}

		$css .= '}';

		//Responsive mobile
		$title_style_xs = '';
		$title_style_xs .= (isset($settings->content_title_fontsize_xs) && $settings->content_title_fontsize_xs) ? 'font-size:' . $settings->content_title_fontsize_xs . 'px;' : '';
		$title_style_xs .= (isset($settings->content_title_margin_xs) && trim($settings->content_title_margin_xs)) ? 'margin:' . $settings->content_title_margin_xs . ';' : '';

		$subtile_style_xs = '';
		$subtile_style_xs .= (isset($settings->content_subtitle_fontsize_xs) && $settings->content_subtitle_fontsize_xs) ? 'font-size:' . $settings->content_subtitle_fontsize_xs . 'px;' : '';

		$description_style_xs = '';
		$description_style_xs .= (isset($settings->description_fontsize_xs) && $settings->description_fontsize_xs) ? 'font-size:' . $settings->description_fontsize_xs . 'px;' : '';
		$description_style_xs .= (isset($settings->description_margin_xs) && trim($settings->description_margin_xs)) ? 'margin:' . $settings->description_margin_xs . ';' : '';

		$css .= '@media (max-width: 767px) {';

		$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-dots {';
		if (isset($settings->bullet_position_verti) && $settings->bullet_position_verti_xs) {
			$css .= 'bottom: ' . $settings->bullet_position_verti_xs . '%;';
		}
		if (isset($settings->bullet_position_hori) && $settings->bullet_position_hori_xs) {
			$css .= 'left: ' . $settings->bullet_position_hori_xs . 'px;';
		}
		$css .= '}';
		$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-nav-control .nav-control {';
		$css .= $arrow_style;
		if (isset($settings->arrow_position_verti) && $settings->arrow_position_verti_xs) {
			$css .= 'margin-top: ' . $settings->arrow_position_verti_xs . '%;';
		}
		if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori_xs) {
			$css .= 'margin-left: ' . $settings->arrow_position_hori_xs . 'px;';
		}
		if (isset($settings->arrow_position_hori) && $settings->arrow_position_hori_xs) {
			$css .= 'margin-right: ' . $settings->arrow_position_hori_xs . 'px;';
		}
		$css .= '}';

		if ($title_style_xs) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-heading {';
			$css .= $title_style_xs;
			$css .= '}';
		}
		if ($subtile_style_xs) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-subheading {';
			$css .= $subtile_style_xs;
			$css .= '}';
		}

		if ($description_style_xs) {
			$css .= '#jwpf-addon-' . $this->addon->id . ' .jwpf-carousel-extended-description {';
			$css .= $description_style_xs;
			$css .= '}';
		}

		$css .= '}';

        $carousel_radius = (isset($settings->carousel_radius) && $settings->carousel_radius) ?  $settings->carousel_radius : "0";

        $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-image-carousel-layout1 .jwpf-carousel-extended-content-wrap,
          #jwpf-addon-' . $this->addon->id . ' .jwpf-image-carousel-layout1 .jwpf-carousel-extended-item img,
          #jwpf-addon-' . $this->addon->id . ' .jwpf-image-carousel-layout1 .jwpf-carousel-extended-item-overlay{
            border-radius: '.$carousel_radius.'px;
        }';
        $css.='#jwpf-addon-' . $this->addon->id . ' .jwpf-image-carousel-layout1 .jwpf-carousel-extended-item img{
            border-radius: '.$carousel_radius.'px;
        }';



//       旋转木马的宽度，解决嵌套在选项卡中没有宽度的问题
        $carousel_width = (isset($settings->carousel_width) && $settings->carousel_width) ? $settings->carousel_width : '';
        $carousel_width_sm = (isset($settings->carousel_width_sm) && $settings->carousel_width_sm) ? $settings->carousel_width_sm : '';
        $carousel_width_xs = (isset($settings->carousel_width_xs) && $settings->carousel_width_xs) ? $settings->carousel_width_xs : '';

        $css.='#jwpf-addon-' . $this->addon->id . ' #lbwidth{
            width: '.$carousel_width.'px;
        }
         @media and (max-width: 992px) and (min-width:768px){
            #jwpf-addon-' . $this->addon->id . ' #lbwidth{
                width: '.$carousel_width_sm.'px;
            }
         }
          @media and (max-width:768px){
            #jwpf-addon-' . $this->addon->id . ' #lbwidth{
                width: '.$carousel_width_xs.'px;
            }
         }';

        return $css;
	}

	public static function getTemplate()
	{
		$company_id             = $_GET['company_id'] ?? 0;
        $site_id                = $_GET['site_id'] ?? 0;
        $infoss = JwPageFactoryBase::getBannerLists($site_id, $company_id);
        $atts=json_encode($infoss);

		$output = '
        <style  type="text/css">
            <# if(data.carousel_arrow){ #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-nav-control .nav-control {
                    <# if(data.arrow_height){ #>
                        height: {{data.arrow_height}}px;
                        line-height: {{ data.arrow_height - data.arrow_border_width * 2 }}px;
                    <# } #>
                    background: {{data.arrow_background}};
                    <# if(data.arrow_border_width){ #>
                        border-width: {{data.arrow_border_width}}px;
                        border-style: solid;
                    <# } #>
                    border-color: {{data.arrow_border_color}};
                    border-radius: {{data.arrow_border_radius}}px;
                    color: {{data.arrow_color}};
                    font-size: {{data.arrow_font_size}}px;
                    <# if(_.isObject(data.arrow_position_verti)) { #>
                        margin-top: {{data.arrow_position_verti.md}}%;
                    <# } else { #>
                        margin-top: {{data.arrow_position_verti}}%;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-left: {{data.arrow_position_hori.md}}px;
                    <# } else { #>
                        margin-left: {{data.arrow_position_hori}}px;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-right: {{data.arrow_position_hori.md}}px;
                    <# } else { #>
                        margin-right: {{data.arrow_position_hori}}px;
                    <# } #>
                    width: {{data.arrow_width}}px;
                }

                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-nav-control .nav-control:hover {
                    background-color: {{data.arrow_hover_background}};
                    color: {{data.arrow_hover_color}};
                    border-color: {{data.arrow_hover_border_color}};
                }

                <# if(data.arrow_height){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-nav-control {
                        top: -{{data.arrow_height}}px;
                    }
                <# }
            } #>
            <# if(data.carousel_bullet){ #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots {
                    <# if(_.isObject(data.bullet_position_verti)) { #>
                        bottom: {{data.bullet_position_verti.md}}%;
                    <# } else { #>
                        bottom: {{data.bullet_position_verti}}%;
                    <# }
                    if(_.isObject(data.bullet_position_hori)) {
                    #>
                        left: {{data.bullet_position_hori.md}}px;
                    <# } else { #>
                        left: {{data.bullet_position_hori}}px;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots ul li {
                    <# if(data.bullet_height){ #>
                        height: {{data.bullet_height}}px;
                        line-height: {{(data.bullet_height)-(data.bullet_border_width)}}px;
                    <# } #>
                    background: {{data.bullet_background}};
                    <# if(data.bullet_border_width){ #>
                        border-width: {{data.bullet_border_width}}px;
                        border-style: solid;
                    <# } #>
                    border-color: {{data.bullet_border_color}};
                    border-radius: {{data.bullet_border_radius}}px;
                    width: {{data.bullet_width}}px;
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots ul li:hover span,
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots ul li.active span {
                    background: {{data.bullet_active_background}};
                }
            <# } #>

            <# if(data.image_carousel_layout == "layout3" || data.image_carousel_layout == "layout4"){ #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-center .jwpf-carousel-extended-item .jwpf-addon-wrapper {
                    transition: all {{data.carousel_speed}}ms ease 0s;
                }
            <# }
             if(data.overlay_gradient){
                var gradient_color1 = (!_.isEmpty(data.overlay_gradient.color) && data.overlay_gradient.color) ? data.overlay_gradient.color : "rgba(59, 25, 208, 0.5)";
                var gradient_color2 = (!_.isEmpty(data.overlay_gradient.color2) && data.overlay_gradient.color2) ? data.overlay_gradient.color2 : "rgba(255, 79, 226, 0.5)";
                 var degree = data.overlay_gradient.deg;
                 var type = data.overlay_gradient.type;
                 var radialPos = (!_.isEmpty(data.overlay_gradient.radialPos) && data.overlay_gradient.radialPos) ? data.overlay_gradient.radialPos : "Center Center";
                var radial_angle1 = (!_.isEmpty(data.overlay_gradient.pos) && data.overlay_gradient.pos) ? data.overlay_gradient.pos : "2";
                var radial_angle2 = (!_.isEmpty(data.overlay_gradient.pos2) && data.overlay_gradient.pos2) ? data.overlay_gradient.pos2 : "99";
            }
            
            let image_style = (!_.isEmpty(data.image_style) && data.image_style) ? data.image_style : "none";

            #>
            #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-item-overlay {
				<# if(type!=="radial"){ #>
					background: -webkit-linear-gradient({{degree}}deg, {{gradient_color1}} {{radial_angle1}}%, {{gradient_color2}} {{radial_angle2}}%) transparent;
					background: linear-gradient({{degree}}deg, {{gradient_color1}} {{radial_angle1}}%, {{gradient_color2}} {{radial_angle2}}%) transparent;
				<# } else { #>
					background: -webkit-radial-gradient(at {{radialPos}}, {{gradient_color1}} {{radial_angle1}}%, {{gradient_color2}} {{radial_angle2}}%) transparent;
					background: radial-gradient(at {{radialPos}}, {{gradient_color1}} {{radial_angle1}}%, {{gradient_color2}} {{radial_angle2}}%) transparent;
				<# } #>
            }

            <# if(data.item_content_verti_align || data.item_content_hori_align) { #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-content-wrap {
                    <# if(data.item_content_verti_align === "top"){ #>
                        justify-content: flex-start;
                    <# } else if(data.item_content_verti_align === "bottom"){ #>
                        justify-content: flex-end;
                    <# } #>
                    <# if(data.item_content_hori_align === "left"){ #>
                        align-items: flex-start;
                        text-align: left;
                    <# } else if(data.item_content_hori_align === "right"){ #>
                        align-items: flex-end;
                        text-align: right;
                    <# } #>
                }
            <# } #>
            
            #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-heading {
                <# if(_.isObject(data.content_title_fontsize)){ #>
                    font-size:{{data.content_title_fontsize.md}}px;
                <# } else { #>
                    font-size:{{data.content_title_fontsize}}px;
                <# } #>
                letter-spacing:{{data.content_title_letterspace}};
                color:{{data.content_title_text_color}};
                line-height:{{data.content_title_lineheight}}px;
                <# if(_.isObject(data.content_title_margin)) { #>
                    margin:{{data.content_title_margin.md}};
                <# } else { #>
                    margin:{{data.content_title_margin}};
                <# }
                if(_.isObject(data.content_title_font_style)) {
                    if(data.content_title_font_style.underline) {
                #>
                        text-decoration:underline;
                    <# }
                    if(data.content_title_font_style.italic) {
                    #>
                        font-style:italic;
                    <# }
                    if(data.content_title_font_style.uppercase) {
                    #>
                        text-transform:uppercase;
                    <# }
                    if(data.content_title_font_style.weight) {
                    #>
                        font-weight:{{data.content_title_font_style.weight}};
                    <# }
                } #>
            }
            
            #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-subheading {
                <# if(_.isObject(data.content_subtitle_fontsize)){ #>
                    font-size:{{data.content_subtitle_fontsize.md}}px;
                <# } else { #>
                    font-size:{{data.content_subtitle_fontsize}}px;
                <# } #>
                letter-spacing:{{data.content_subtitle_letterspace}};
                color:{{data.content_subtitle_text_color}};
                line-height:{{data.content_subtitle_lineheight}}px;
                <# if(_.isObject(data.content_subtitle_font_style)) {
                    if(data.content_subtitle_font_style.underline) {
                    #>
                        text-decoration:underline;
                    <# }
                    if(data.content_subtitle_font_style.italic) {
                    #>
                        font-style:italic;
                    <# }
                    if(data.content_subtitle_font_style.uppercase) {
                    #>
                        text-transform:uppercase;
                    <# }
                    if(data.content_subtitle_font_style.weight) {
                    #>
                        font-weight:{{data.content_subtitle_font_style.weight}};
                    <# }
                } #>
            }

            #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-description {
                <# if(_.isObject(data.description_fontsize)){ #>
                    font-size:{{data.description_fontsize.md}}px;
                <# } else { #>
                    font-size:{{data.description_fontsize}}px;
                <# } #>
                letter-spacing:{{data.description_letterspace}};
                color:{{data.description_text_color}};
                line-height:{{data.description_lineheight}}px;
                <# if(_.isObject(data.description_margin)) { #>
                    margin:{{data.description_margin.md}};
                <# } else { #>
                    margin:{{data.description_margin}};
                <# } #>
                <# if(_.isObject(data.description_font_style)) {
                    if(data.description_font_style.underline) {
                    #>
                        text-decoration:underline;
                    <# }
                    if(data.description_font_style.italic) {
                    #>
                        font-style:italic;
                    <# }
                    if(data.description_font_style.uppercase) {
                    #>
                        text-transform:uppercase;
                    <# }
                    if(data.description_font_style.weight) {
                    #>
                        font-weight:{{data.description_font_style.weight}};
                    <# }
                } #>
            }
            
           
            #jwpf-addon-{{ data.id }} .jwpf-image-carousel-layout1 .jwpf-carousel-extended-content-wrap,
            #jwpf-addon-{{ data.id }} .jwpf-image-carousel-layout1 .jwpf-carousel-extended-item img,
            #jwpf-addon-{{ data.id }} .jwpf-image-carousel-layout1 .jwpf-carousel-extended-item-overlay{
                border-radius:{{data.carousel_radius||0}}px;
            }
            
            @media (min-width: 768px) and (max-width: 991px) {
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-nav-control .nav-control {
                    <# if(_.isObject(data.arrow_position_verti)) { #>
                        margin-top: {{data.arrow_position_verti.sm}}%;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-left: {{data.arrow_position_hori.sm}}px;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-right: {{data.arrow_position_hori.sm}}px;
                    <# } #>
                }
                <# if(data.carousel_bullet){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots {
                        <# if(_.isObject(data.bullet_position_verti)) { #>
                            bottom: {{data.bullet_position_verti.sm}}%;
                        <# }
                        if(_.isObject(data.bullet_position_hori)) {
                        #>
                            left: {{data.bullet_position_hori.sm}}px;
                        <# } #>
                    }
                <# } #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-heading {
                    <# if(_.isObject(data.content_title_fontsize)){ #>
                        font-size:{{data.content_title_fontsize.sm}}px;
                    <# } #>
                    <# if(_.isObject(data.content_title_margin)) { #>
                        margin:{{data.content_title_margin.sm}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-subheading {
                    <# if(_.isObject(data.content_subtitle_fontsize)){ #>
                        font-size:{{data.content_subtitle_fontsize.sm}}px;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-description {
                    <# if(_.isObject(data.description_fontsize)){ #>
                        font-size:{{data.description_fontsize.sm}}px;
                    <# } #>
                    <# if(_.isObject(data.description_margin)) { #>
                        margin:{{data.description_margin.sm}};
                    <# } #>
                }
            }
            @media (max-width: 767px) {
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-nav-control .nav-control {
                    <# if(_.isObject(data.arrow_position_verti)) { #>
                        margin-top: {{data.arrow_position_verti.xs}}%;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-left: {{data.arrow_position_hori.xs}}px;
                    <# }
                    if(_.isObject(data.arrow_position_hori)) {
                    #>
                        margin-right: {{data.arrow_position_hori.xs}}px;
                    <# } #>
                }
                <# if(data.carousel_bullet){ #>
                    #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-dots {
                        <# if(_.isObject(data.bullet_position_verti)) { #>
                            bottom: {{data.bullet_position_verti.xs}}%;
                        <# }
                        if(_.isObject(data.bullet_position_hori)) {
                        #>
                            left: {{data.bullet_position_hori.xs}}px;
                        <# } #>
                    }
                <# } #>
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-heading {
                    <# if(_.isObject(data.content_title_fontsize)){ #>
                        font-size:{{data.content_title_fontsize.xs}}px;
                    <# } #>
                    <# if(_.isObject(data.content_title_margin)) { #>
                        margin:{{data.content_title_margin.xs}};
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-subheading {
                    <# if(_.isObject(data.content_subtitle_fontsize)){ #>
                        font-size:{{data.content_subtitle_fontsize.xs}}px;
                    <# } #>
                }
                #jwpf-addon-{{ data.id }} .jwpf-carousel-extended-description {
                    <# if(_.isObject(data.description_fontsize)){ #>
                        font-size:{{data.description_fontsize.xs}}px;
                    <# } #>
                    <# if(_.isObject(data.description_margin)) { #>
                        margin:{{data.description_margin.xs}};
                    <# } #>
                }
            }
            
            /*旋转木马宽度，解决嵌套在选项卡中没有宽度*/
            <# 
                let carousel_width_style = _.isObject(data.carousel_width) ? data.carousel_width.md : data.carousel_width;
                let carousel_width_style_sm = _.isObject(data.carousel_width) ? data.carousel_width.sm : data.carousel_width;
                let carousel_width_style_xs = _.isObject(data.carousel_width) ? data.carousel_width.xs : data.carousel_width;
            #>
            #jwpf-addon-{{ data.id }} .jwpf-addon{
                width:{{carousel_width_style}}px;
            }
            @media screen and (max-width:992px) and (min-width:768px){
                #jwpf-addon-{{ data.id }} .jwpf-addon{
                    width:{{carousel_width_style_sm}}px;
                }
            }
            @media screen and (max-width:768px){
                #jwpf-addon-{{ data.id }} .jwpf-addon{
                    width:{{carousel_width_style_xs}}px;
                }
            }
        </style>
        <#
	        var infoss = ' . $atts . ';
            if(data.carousel_height)
	        {
                var carousel_height = data.carousel_height.md || 500;
    	        var carousel_height_sm = data.carousel_height.sm || 400;
    	        var carousel_height_xs = data.carousel_height.xs || 300;
            }
			
	        var left_arrow ="";
	        var right_arrow = "";
	        if(data.arrow_icon=="long_arrow"){
	            left_arrow ="fa-long-arrow-left";
	            right_arrow = "fa-long-arrow-right";
	        } else {
	            left_arrow ="fa-angle-left";
	            right_arrow = "fa-angle-right";
	        }
	        let carousel_item_number = 3;
	        let carousel_item_number_sm = 3;
	        let carousel_item_number_xs = 1;
	        if(_.isObject(data.carousel_item_number)){
	            carousel_item_number = data.carousel_item_number.md
	            carousel_item_number_sm = data.carousel_item_number.sm
	            carousel_item_number_xs = data.carousel_item_number.xs
	        }
	       if(data.carousel_center_padding)
           {
                var carousel_center_padding = +data.carousel_center_padding.md || 180;
                var carousel_center_padding_sm = +data.carousel_center_padding.sm || 90;
                var carousel_center_padding_xs = +data.carousel_center_padding.xs || 50;
           }
        #>
            <div class="jwpf-addon jwpf-carousel-extended {{data.class}} jwpf-image-carousel-{{data.image_carousel_layout}}" data-left-arrow="{{left_arrow}}" data-right-arrow="{{right_arrow}}" data-arrow="{{data.carousel_arrow}}" data-dots="{{data.carousel_bullet}}" data-image-layout="{{data.image_carousel_layout}}" data-autoplay="{{data.carousel_autoplay}}" data-speed="{{data.carousel_speed}}" data-interval="{{data.carousel_interval}}" data-margin="{{data.carousel_margin}}" {{{(data.image_carousel_layout == "layout3" || data.image_carousel_layout == "layout4") ? `data-padding="${carousel_center_padding}"` : ""}}} {{{(data.image_carousel_layout == "layout3" || data.image_carousel_layout == "layout4") ? `data-padding-sm="${carousel_center_padding_sm}"` : ""}}} {{{(data.image_carousel_layout == "layout3" || data.image_carousel_layout == "layout4") ? `data-padding-xs="${carousel_center_padding_xs}"` : ""}}} data-height="{{carousel_height}}" data-height-sm="{{carousel_height_sm}}" data-height-xs="{{carousel_height_xs}}" data-item-number="{{carousel_item_number || 3}}" data-item-number-sm="{{carousel_item_number_sm || 3}}" data-item-number-xs="{{carousel_item_number_xs || 1}}" {{{data.image_carousel_layout == "layout1" && data.carousel_fade ?  `data-fade="${data.carousel_fade}"` : ""}}}>
                <# if(_.isArray(data.jw_image_carousel_item)){
                    _.each(data.jw_image_carousel_item, function(carousel_item){
                #>
                    <# if(carousel_item.text_from && carousel_item.text_from == 1 && carousel_item.text_id) {
                        let newArr = infoss.filter(item => item.id == carousel_item.text_id)
                        var item_ = ""
                        if(newArr.length > 0) {
                            item_ = newArr[0]
                            carousel_item.item_title = item_.banner_title
                            carousel_item.item_subtitle = item_.second_title
                            carousel_item.image_carousel_img = item_.image_intro
                            carousel_item.item_description = item_.introtext
                        }
                        
                    } #>

                        <div class="jwpf-carousel-extended-item">
                            <# if(!_.isEmpty(carousel_item.image_carousel_img_link) && carousel_item.image_carousel_img_link){ #>
                                <a href="{{carousel_item.image_carousel_img_link}}" {{(carousel_item.link_open_new_window ? `target="_blank"` : "")}}>
                            <# }
                            if(carousel_item.image_carousel_img && carousel_item.image_carousel_img.indexOf("http://") == -1 && carousel_item.image_carousel_img.indexOf("https://") == -1){
                            #>
                                <img style="object-fit:{{image_style}}" src=\'{{ pagefactory_base + carousel_item.image_carousel_img }}\' alt="{{ carousel_item.item_title }}">
                            <# } else if(carousel_item.image_carousel_img){ #>
                                <img style="object-fit:{{image_style}}" src=\'{{ carousel_item.image_carousel_img }}\' alt="{{ carousel_item.item_title }}">
                            <# }
                            if(data.carousel_overlay){
                            #>
                                <div class="jwpf-carousel-extended-item-overlay"></div>
                            <# }
                            if(data.image_carousel_layout !== ""){
                            #>
                                <div class="jwpf-carousel-extended-content-wrap">
                                
                                    <# if(carousel_item.item_title){ #>
                                        <div class="jwpf-carousel-extended-heading">
                                            {{carousel_item.item_title}}
                                        </div>
                                    <# } 
                                    if(carousel_item.item_subtitle) { 
                                    #>
                                        <div class="jwpf-carousel-extended-subheading">
                                            {{carousel_item.item_subtitle}}
                                        </div>
                                    <# } 
                                    if(carousel_item.item_description) {
                                    #>
                                        <div class="jwpf-carousel-extended-description">
                                            {{carousel_item.item_description}}
                                        </div>
                                    <# } #>
                                </div>
                            <# }
                            if(!_.isEmpty(carousel_item.image_carousel_img_link) && carousel_item.image_carousel_img_link){
                            #>
                                </a>
                            <# } #>
                        </div>
                    <#
                    })
                } #>
            </div>
        ';
		return $output;
	}
}
