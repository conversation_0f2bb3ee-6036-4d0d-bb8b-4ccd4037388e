<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>or<PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonM_pro_tab extends JwpagefactoryAddons
{
    public function render()
    {
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $bg_color = (isset($settings->bg_color) && $settings->bg_color) ? $settings->bg_color : '';
        $bg_image = (isset($settings->bg_image) && $settings->bg_image) ? $settings->bg_image : '';
        $left_title_color = (isset($settings->left_title_color) && $settings->left_title_color) ? $settings->left_title_color : '';
        $left_title_fontSize = (isset($settings->left_title_fontSize) && $settings->left_title_fontSize) ? $settings->left_title_fontSize : '';






        $output .= "<style>
        * {
            padding: 0;
            margin: 0;
        }

        a {
            text-decoration: none;
            color: #333;
        }

        img {
            vertical-align: middle;
        }

        ${addon_id}  .tabBox {
            display: flex;
        }

        ${addon_id}  .leftBox {
            width: 20%;
            margin: 0 20px;
            box-sizing: border-box;
        }

        ${addon_id} .rightBox {
            width: 80%;
        }

        ${addon_id} .left_item {
            display: flex;
        }

        ${addon_id}  .proTitle {
            background-color:  ${bg_color} ;
            color: {$left_title_color};
            background-image: url(${bg_image});
            background-repeat: no-repeat;
            background-size: cover;
            background-attachment: inherit;
            background-position: 50% 50%;
            box-shadow: 0 0 0 0 #ffffff;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: {$left_title_fontSize}px;
            font-weight: bold;
        }

        ${addon_id}  .left_item {
            cursor: pointer;
            width: 100%;
            height: 45px;
            background: {$settings->left_tab_bgcolor};
            position: relative;
            font-size: 16px;
            line-height: 45px;
            color: {$settings->left_tab_fontcolor};
            font-weight: bold;
            padding-left: 25px;
            box-sizing: border-box;
        }

        ${addon_id}  .line {
            margin-right: 10px;
        }

        ${addon_id}  .yuan {
            width: 20px;
            height: 20px;
            background: #fff;
            position: absolute;
            top: 0;
            right: 20px;
            bottom: 0;
            margin: auto;
            border-radius: 50%;
        }

        ${addon_id}  .left_item:hover {
            background: {$settings->left_tab_bgcolor_hover};
            color: {$settings->left_tab_fontcolor_hover};
        }

        ${addon_id}  .right_item {
            display: none;
            height:100%;
        }

        ${addon_id}  .right_item_item {
            width: calc((100% - 60px)/3);
            float: left;
            margin-right: 30px;
            margin-bottom: 15px;
            display: block;
            text-decoration: none;
            border: 2px solid {$settings->right_con_bordercolor};
            box-sizing: border-box;
        }

        ${addon_id}  .right_item_item:nth-child(3n) {
            margin-right: 0px;
        }

        ${addon_id}  .right_item_item img {
            width: 100%;
            height: 230px;
        }

        ${addon_id}   .right_item_item_title {
            width: 100%;
            line-height: 50px;
            background: {$settings->right_con_bgcolor};
            color: {$settings->right_con_fontcolor};
            text-align: center;
            font-size: {$settings->right_con_fontSize}px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        ${addon_id} .right_item_item:hover {
            border: 2px solid {$settings->right_con_bordercolor_hover};
        }

        ${addon_id} .right_item_item:hover .right_item_item_title {
            background: {$settings->right_con_bgcolor_hover};
            color: {$settings->right_con_fontcolor_hover};
        }

        </style>";
        $output .= "<div id='${addon_id}'>
        <div class='tabBox'>
        <div class='leftBox'>
            <div class='proTitle'>
                {$settings->admin_label}
            </div>
            <div class='left_itemBox'>
                <div class='left_item'>
                    <div class='line'>一</div>
                    <div class='text'>防水涂料</div>
                    <div class='yuan'></div>
                </div>
                <div class='left_item'>
                    <div class='line'>一</div>
                    <div class='text'>防水涂料</div>
                    <div class='yuan'></div>
                </div>
            </div>
        </div>
        <div class='rightBox'>
            <div class='right_item' style='display: block;'>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青</div>
                </a>
            </div>
            <div class='right_item'>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青1</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青1</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青1</div>
                </a>
                <a href='#' class='right_item_item'>
                    <img src='https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg'
                        alt=''>
                    <div class='right_item_item_title'>氯丁胶乳化沥青1</div>
                </a>
            </div>
        </div>
    </div>
        </div>
        <script>
        jQuery('.left_item').click(function () {
            jQuery('.right_item').eq(jQuery(this).index()).show().siblings().hide()
        })

        </script>
        ";
        return $output;
    }

    public function css()
    {

    }



    public static function getTemplate()
	{
        
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        var bg_color = (!_.isEmpty(data.bg_color) && data.bg_color) ? data.bg_color : "";
        var bg_image = (!_.isEmpty(data.bg_image) && data.bg_image) ? data.bg_image : "";
        var left_title_color = (!_.isEmpty(data.left_title_color) && data.left_title_color) ? data.left_title_color : "";
        var left_title_fontSize = (!_.isEmpty(data.left_title_fontSize) && data.left_title_fontSize) ? data.left_title_fontSize : "";

        #>
        <style>
        * {
            padding: 0;
            margin: 0;
        }

        a {
            text-decoration: none;
            color: #333;
        }

        img {
            vertical-align: middle;
        }

        {{addonId}}   .tabBox {
            display: flex;
        }

        {{addonId}}   .leftBox {
            width: 20%;
            margin: 0 20px;
            box-sizing: border-box;
        }

        {{addonId}}   .rightBox {
            width: 80%;
        }

        {{addonId}}  .left_item {
            display: flex;
        }

        {{addonId}}   .proTitle {
            color: {{left_title_color}};
            background-color:{{bg_color}};
            background-image: url({{bg_image}});
            background-repeat: no-repeat;
            background-size: cover;
            background-attachment: inherit;
            background-position: 50% 50%;
            box-shadow: 0 0 0 0 #ffffff;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: {{left_title_fontSize}}px;
            font-weight: bold;
        }

        {{addonId}}   .left_item {
            cursor: pointer;
            width: 100%;
            height: 45px;
            background: {{data.left_tab_bgcolor}};
            position: relative;
            font-size: 16px;
            line-height: 45px;
            color: {{data.left_tab_fontcolor}};
            font-weight: bold;
            padding-left: 25px;
            box-sizing: border-box;
        }

        {{addonId}}  .line {
            margin-right: 10px;
        }

        {{addonId}}   .yuan {
            width: 20px;
            height: 20px;
            background: #fff;
            position: absolute;
            top: 0;
            right: 20px;
            bottom: 0;
            margin: auto;
            border-radius: 50%;
        }

        {{addonId}}   .left_item:hover {
            background: {{data.left_tab_bgcolor_hover}};
            color: {{data.left_tab_fontcolor_hover}};
        }

        {{addonId}}   .right_item {
            display: none;
            height:100%;
        }

        {{addonId}}   .right_item_item {
            width: calc((100% - 60px)/3);
            float: left;
            margin-right: 30px;
            margin-bottom: 15px;
            display: block;
            text-decoration: none;
            border: 2px solid {{data.right_con_bordercolor}};
            box-sizing: border-box;
        }

        {{addonId}}   .right_item_item:nth-child(3n) {
            margin-right: 0px;
        }

        {{addonId}}   .right_item_item img {
            width: 100%;
            height: 230px;
        }

        {{addonId}}    .right_item_item_title {
            width: 100%;
            line-height: 50px;
            background: {{data.right_con_bgcolor}};
            color: {{data.right_con_fontcolor}};
            text-align: center;
            font-size: {{data.right_con_fontSize}}px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        {{addonId}}   .right_item_item:hover {
            border: 2px solid {{data.right_con_bordercolor_hover}};
        }

        {{addonId}}   .right_item_item:hover .right_item_item_title {
            background: {{data.right_con_bgcolor_hover}};
            color: {{data.right_con_fontcolor_hover}};
        }

           
        </style>
        <div  id="jwpf-addon-{{data.id}}">
        <div class="tabBox">
        <div class="leftBox">
            <div class="proTitle">
                {{data.admin_label}}
            </div>
            <div class="left_itemBox">
                <div class="left_item">
                    <div class="line">一</div>
                    <div class="text">防水涂料</div>
                    <div class="yuan"></div>
                </div>
                <div class="left_item">
                    <div class="line">一</div>
                    <div class="text">防水涂料</div>
                    <div class="yuan"></div>
                </div>
            </div>
        </div>
        <div class="rightBox">
            <div class="right_item" style="display: block;">
                <a href="#" class="right_item_item">
                    <img src="https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg"
                        alt="">
                    <div class="right_item_item_title">氯丁胶乳化沥青</div>
                </a>
                <a href="#" class="right_item_item">
                    <img src="https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg"
                        alt="">
                    <div class="right_item_item_title">氯丁胶乳化沥青</div>
                </a>
                <a href="#" class="right_item_item">
                    <img src="https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg"
                        alt="">
                    <div class="right_item_item_title">氯丁胶乳化沥青</div>
                </a>
                <a href="#" class="right_item_item">
                    <img src="https://znjz.obs.cn-north-4.myhuaweicloud.com:443/user3271/web/20210325/5e26899a77f4388917e7e4f19c8186ab.jpeg"
                        alt="">
                    <div class="right_item_item_title">氯丁胶乳化沥青</div>
                </a>
            </div>
        </div>
        </div>
        </div>
        ';
        return $output;
    }

}