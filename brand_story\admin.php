<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'brand_story',
        'title' => JText::_('品牌故事'),
        'desc' => JText::_(''),
        'category' => '其他',
        'attr' => array(
            'general' => array(
                'brand_type' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'values' => array(
                        'type1' => '布局1',
                    ),
                    'std' => 'type1'
                ),

                
            ),
        ),
    )
);
