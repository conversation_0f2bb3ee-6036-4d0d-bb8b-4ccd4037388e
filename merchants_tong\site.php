<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 14:00:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Jo<PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonMerchants_tong extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $page_view_name = isset($_GET['view']);
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/articles.php';
        require_once $article_helper;
        $items = JwpagefactoryHelperArticles::getmerchantstong( $company_id, $site_id);
        foreach($items as $k => $v)
        {
            $output .=''.$v->merchants_tong.'';
        }
    //    $output .='<script src="https://hm.baidu.com/hm.js?e0f423dda76a514cd2c4eff74b55ff96"></script>';


        return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
    public static function getTemplate()
    {
        return '<div>本段文字用于编辑模式下占位，预览模式下不显示</div>';
    }
}