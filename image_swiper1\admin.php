<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'image_swiper1',
        'title' => JText::_('多图轮播'),
        'desc' => JText::_('多图轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                // 'image_carousel_layout' => array(
                //     'type' => 'select',
                //     'title' => JText::_('布局方式'),
                //     'desc' => JText::_('布局方式'),
                //     'depends' => array(
                //         array('carousel_options', '=', 'elements'),
                //     ),
                //     'values' => array(
                //         'layout1' => '普通轮播',
                //         'layout2' => '一次显示多个slides',
                //         'layout3' => '异形的slide',
                //         'layout4' => '布局4',
                //     ),
                //     'std' => 'layout1',
                // ),
                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播元素样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/5e0d011143cede56a3f9fe2c888120fb.jpeg',
                        )
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

                        //                        字还是图片
                        'item_content_type' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => '文字',
                                    'value' => 'font'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                ),
                            ),
                            'tabs' => true,
                        ),
                         'item_subtitle' => array(
                             'type' => 'text',
                             'title' => JText::_('轮播项标题'),
                             'desc' => JText::_('轮播项标题'),
                             'depends' => array(
                                 array('item_content_type', '=', 'font')
                             )
                         ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('轮播项描述'),
                            'desc' => JText::_('轮播项描述'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'carousel_item_img' => array(
                            'type' => 'media',
                            'title' => '轮播图中间图片链接',
                            'desc' => '轮播图中间图片链接',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            )
                        ),
                        'carousel_item_img_width' => array(
                            'type' => 'slider',
                            'title' => '轮播图中间图片宽度所占比',
                            'desc' => '轮播图中间图片宽度所占比',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'max' => 100
                        ),
                    ),
                ),
                'carousel_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播容器左边距'),
                    'desc' => JText::_('轮播容器左边距'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('paved', '=', 0),
                    ),
                    'min' => 0,
                    'max' => 1000,
                    'std' => 0,
                    'responsive' => true,
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度（仅可调整pc）'),
                    'desc' => JText::_('轮播区域高度（仅可调整pc）'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => 800,
                ),
                'carousel_view_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播图片区域宽度'),
                    'desc' => JText::_('轮播图片区域宽度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('paved', '=', 0),
                    ),
                    'min' => 100,
                    'max' => 2000,
                    'std' => array(
                        'md' => 800,
                        'sm' => 600,
                        'xs' => 300
                    ),
                    'responsive' => true,
                ),
                'carousel_view_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播图片高度'),
                    'desc' => JText::_('轮播图片高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => 504,
                    'responsive' => true,
                ),
                'carousel_view_left' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播图片区域左边距'),
                    'desc' => JText::_('轮播图片区域左边距'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('paved', '=', 0),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => 0,
                    'responsive' => true,
                ),
                // 'carousel_item_number' => array(
                //     'type' => 'slider',
                //     'title' => JText::_('一次显示的slides'),
                //     'desc' => JText::_('一次显示的slides'),
                //     'min' => 1,
                //     'max' => 6,
                //     'responsive' => true,
                //     'std' => array('md' => 3, 'sm' => 2, 'xs' => 1),
                //     'depends' => array(
                //         array('carousel_options', '=', 'elements'),
                //         array('image_carousel_layout', '!=', 'layout1'),
                //     ),
                // ),
                'carousel_margin' => array(
                    'type' => 'number',
                    'title' => JText::_('slide之间的距离'),
                    'desc' => JText::_('slide之间的距离'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                        array('image_carousel_layout', '!=', 'layout1'),
                    ),
                    'std' => 15,
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 1
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 2500
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 4500
                ),


//                 'item_content_verti_align' => array(
//                     'type' => 'select',
//                     'title' => JText::_('内容垂直对齐'),
//                     'desc' => JText::_('内容垂直对齐'),
//                     'depends' => array(
//                         array('carousel_options', '=', 'item_style'),
//                     ),
//                     'values' => array(
//                         'top' => JText::_('顶部对齐'),
//                         'middle' => JText::_('中间对齐'),
//                         'bottom' => JText::_('底部对齐'),
//                     ),
//                     'std' => 'middle',
//                 ),
//                 'item_content_hori_align' => array(
//                     'type' => 'select',
//                     'title' => JText::_('内容水平对齐'),
//                     'desc' => JText::_('内容水平对齐'),
//                     'depends' => array(
//                         array('carousel_options', '=', 'item_style'),
//                     ),
//                     'values' => array(
//                         'left' => JText::_('左对齐'),
//                         'center' => JText::_('居中'),
//                         'right' => JText::_('右对齐'),
//                     ),
//                     'std' => 'center',
//                 ),
                'content_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('内容布局'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std' => 'title_style',
                    'values' => array(
                         array(
                             'label' => '序号',
                             'value' => 'title_style'
                         ),
                         array(
                             'label' => '标题',
                             'value' => 'subtitle_style'
                         ),
                        array(
                            'label' => '描述',
                            'value' => 'desc_style'
                        ),
                        array(
                            'label' => '介绍',
                            'value' => 'intro_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'content' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
//                Title style
                 'content_title_fontsize' => array(
                     'type' => 'slider',
                     'title' => JText::_('序号字体大小'),
                     'std' => 60,
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'responsive' => true,
                     'max' => 100,
                 ),
                 'content_title_lineheight' => array(
                     'type' => 'slider',
                     'title' => JText::_('序号行高'),
                     'std' => '70',
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'max' => 100,
                 ),
                 'content_title_font_family' => array(
                     'type' => 'fonts',
                     'title' => JText::_('字体'),
                     'selector' => array(
                         'type' => 'font',
                         'font' => '{{ VALUE }}',
                         'css' => '.jwpf-carousel-extended-heading { font-family: "{{ VALUE }}"; }'
                     ),
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),
                 'content_title_font_style' => array(
                     'type' => 'fontstyle',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),
                 'content_title_letterspace' => array(
                     'type' => 'select',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                     'values' => array(
                         '-10px' => '-10px',
                         '-9px' => '-9px',
                         '-8px' => '-8px',
                         '-7px' => '-7px',
                         '-6px' => '-6px',
                         '-5px' => '-5px',
                         '-4px' => '-4px',
                         '-3px' => '-3px',
                         '-2px' => '-2px',
                         '-1px' => '-1px',
                         '0px' => 'Default',
                         '1px' => '1px',
                         '2px' => '2px',
                         '3px' => '3px',
                         '4px' => '4px',
                         '5px' => '5px',
                         '6px' => '6px',
                         '7px' => '7px',
                         '8px' => '8px',
                         '9px' => '9px',
                         '10px' => '10px'
                     ),
                     'std' => '0px',
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),
                 'content_title_text_color' => array(
                     'type' => 'color',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                     'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'std'=>'#C9BD97',
                 ),
                 'content_title_margin' => array(
                     'type' => 'margin',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                     'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                     'placeholder' => '10',
                     'depends' => array(
                         array('content_style', '=', 'title_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'max' => 400,
                     'responsive' => true,
                     'std'=>'-30px 0 0 -6px'
                 ),

                //Subtitle style
                'content_subtitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => '16',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'content_subtitle_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '40',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                ),
                'content_subtitle_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-subheading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_subtitle_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'content_subtitle_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'content_subtitle_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'#666666'
                ),

                //Description style
                 'description_fontsize' => array(
                     'type' => 'slider',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                     'std' => '18',
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'responsive' => true,
                     'max' => 400,
                 ),
                 'description_lineheight' => array(
                     'type' => 'slider',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                     'std' => '40',
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'max' => 400,
                 ),
                 'description_font_family' => array(
                     'type' => 'fonts',
                     'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                     'selector' => array(
                         'type' => 'font',
                         'font' => '{{ VALUE }}',
                         'css' => '.jwpf-carousel-extended-description { font-family: "{{ VALUE }}"; }'
                     ),
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),

                 'description_font_style' => array(
                     'type' => 'fontstyle',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),

                 'description_letterspace' => array(
                     'type' => 'select',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                     'values' => array(
                         '-10px' => '-10px',
                         '-9px' => '-9px',
                         '-8px' => '-8px',
                         '-7px' => '-7px',
                         '-6px' => '-6px',
                         '-5px' => '-5px',
                         '-4px' => '-4px',
                         '-3px' => '-3px',
                         '-2px' => '-2px',
                         '-1px' => '-1px',
                         '0px' => 'Default',
                         '1px' => '1px',
                         '2px' => '2px',
                         '3px' => '3px',
                         '4px' => '4px',
                         '5px' => '5px',
                         '6px' => '6px',
                         '7px' => '7px',
                         '8px' => '8px',
                         '9px' => '9px',
                         '10px' => '10px'
                     ),
                     'std' => '0px',
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                 ),

                 'description_text_color' => array(
                     'type' => 'color',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                     'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'std'=>'#333'
                 ),
                 'description_margin' => array(
                     'type' => 'margin',
                     'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                     'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                     'placeholder' => '10',
                     'depends' => array(
                         array('content_style', '=', 'desc_style'),
                         array('carousel_options', '=', 'item_style'),
                     ),
                     'max' => 400,
                     'responsive' => true
                 ),
                'introduce' => array(
                    'type' => 'separator',
                    'title' => JText::_('介绍'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                        array('content_style', '=', 'intro_style'),
                    ),
                ),

                /*'introduce_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('介绍宽度'),
                    'desc' => JText::_('介绍宽度'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>556,
                    'max' => 1000,
                    'responsive' => true
                ),*/
                'introduce_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('介绍高度'),
                    'desc' => JText::_('介绍高度'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 1000,
                    'std'=>array('md'=>529,'sm'=>400,'xs'=>288),
                    'responsive' => true
                ),
                'introduce_content' => array(
                    'type' => 'text',
                    'title' => JText::_('介绍内容'),
                    'desc' => JText::_('介绍内容'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'中华巴洛克历史文化保护街区有大量的中华巴洛克建筑群、特色院落和胡同，是珍贵的历史文化资源。
      街区曾经是哈尔滨三大商业区之一，有浓厚的商业氛围、人文氛围和生活氛围，具备休闲消费营商环境基础，以及文化旅游发展的巨大潜力。',
                    'responsive' => true
                ),
                'introduce_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('介绍内容字号'),
                    'desc' => JText::_('介绍内容字号'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'16',
                    'responsive' => true
                ),
                'introduce_background_color' => array(
                    'type' => 'color',
                    'title' => JText::_('介绍背景色'),
                    'desc' => JText::_('介绍背景色'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'#f9f9f9'
                ),
                'introduce_background_image' => array(
                    'type' => 'media',
                    'title' => JText::_('介绍背景图'),
                    'desc' => JText::_('介绍背景图'),
                    'depends' => array(
                        array('content_style', '=', 'intro_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>''
                ),

                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器设置'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),


                // Arrow style
                'carousel_arrow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否展示翻页按钮'),
                    'desc' => JText::_('是否展示翻页按钮'),
                    'std' => 1,
                    'depends' => array(
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'arrow_position_verti' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮距容器顶端距离'),
                    'desc' => JText::_('翻页按钮距容器顶端距离'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => -100,
                    'max' => 100,
                    'std'=>0,
                    'responsive' => true,
                ),
                'arrow_position_hori' => array(
                    'type' => 'slider',
                    'title' => JText::_('翻页按钮距容器左右边界距离'),
                    'desc' => JText::_('翻页按钮距容器左右边界距离'),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => -200,
                    'max' => 200,
                    'std'=>0,
                    'responsive' => true,
                ),
                'carousel_arrow_type' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮类型'),
                    'desc' => JText::_('翻页按钮类型'),
                    'std' => 'icon',
                    'values'=>array(
                        array(
                            'label'=>'箭头图标',
                            'value'=>'icon'
                        ),
                        array(
                            'label'=>'图片',
                            'value'=>'img'
                        )
                    ),
                    'depends' => array(
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('carousel_arrow','=',1)
                    ),
                    'tabs'=>true
                ),
                'arrow_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('翻页按钮风格'),
                    'std' => 'normal_arrow',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_arrow'
                        ),
                        array(
                            'label' => '鼠标移入',
                            'value' => 'hover_arrow'
                        )
                    ),
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'arrow_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 60,
                ),
                'arrow_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                    ),
                    'std' => 60,
                ),
                'arrow_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('箭头图标大小'),
                    'max' => 100,
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'min'=>0,
                    'std' => 0,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮圆角'),
                    'desc' => JText::_('按钮圆角'),
                    'max' => 100,
                    'min'=>0,
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                //Arrow hover
                /*'arrow_hover_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    ),
                    'std' => 60,
                ),
                'arrow_hover_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 200,
                    'min' => 10,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    ),
                    'std' => 60,
                ),*/
                'arrow_hover_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('箭头颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_hover_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_hover_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮边框宽度'),
                    'std' => '',
                    'min'=>0,
                    'max'=>20,
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_hover_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮圆角'),
                    'desc' => JText::_('按钮圆角'),
                    'max' => 100,
                    'min'=>0,
                    'std' => '',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','icon')
                    )
                ),
                'arrow_img' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'desc' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/normal_left.png',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'normal_arrow'),
                        array('carousel_arrow_type','=','img')
                    )
                ),
                'arrow_img_active' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'desc' => JText::_('按钮图片（请上传左翻页按钮）'),
                    'std' => '/components/com_jwpagefactory/addons/image_swiper/assets/images/active_left.png',
                    'depends' => array(
                        array('carousel_arrow', '=', 1),
//                        array('carousel_navigation', '=', 'arrow_controller'),
                        array('carousel_options', '=', 'elements'),
                        array('arrow_style', '=', 'hover_arrow'),
                        array('carousel_arrow_type','=','img')
                    )
                ),
                'single_style' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否单图轮播'),
                    'desc' => JText::_('是否单图轮播'),
                    'std' => 1,
                    'depends' => array(
                        array('image_carousel_layout', '=', 'layout2'),
                    ),
                ),
            ),
        )
    )
);
