<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'page_anchor',
        'title' => '页面锚点',
        'desc' => '',
        'category' => '常用插件',
        'attr' => array(
            'general' => array(
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'media_bj' => array(
                    'type' => 'select',
                    'title' => '选择布局',
                    'desc' => '',
                    'values' => array(
                        'ym1' => '页面锚点',
                        'ym2' => '轮播锚点',
                    ),
                    'std' => 'ym1',
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '锚点内容'
                ),
                // 布局样式
                'media_style' => array(
                    'type' => 'select',
                    'title' => '选择左侧样式',
                    'desc' => '',
                    'values' => array(
                        'style1' => '样式一',
                        'style2' => '样式二',
                    ),
                    'std' => 'style1',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                // 切换item
                'section_tab_item' => array(
                    'title' => '内容列表',
                    'attr' => array(
                        'name' => array(
                            'type' => 'text',
                            'title' => '名称',
                            'std' => '公司简介',
                        ),
                        'icon_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '图标',
                            'std' => '',
                        ),
                        'icon_img_hover' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '鼠标移入图标',
                            'std' => '',
                        ),
                        'detail_page_type' => array(
                            'type' => 'select',
                            'title' => '跳转方式',
                            'desc' => '跳转方式',
                            'values' => array(
                                'Internal_pages' => '内部页面',
                                'external_links' => '外部链接',
                            ),
                            'std' => 'Internal_pages'
                        ),
                        // 新加跳转的链接地址
                        'detail_url' => array(
                            'type' => 'text',
                            'title' => '链接跳转地址',
                            'desc' => '链接必须以http://或https://开始',
                            'placeholder' => 'http://',
                            'std' => '',
                            'depends' => array(
                                array('detail_page_type', '=', 'external_links'),
                            )
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '内部页面链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('detail_page_type', '=', 'Internal_pages'),
                            )
                        ),
                        'title_id' => array(
                            'type' => 'text',
                            'title' => JText::_('标题跳转ID（容器章节ID）'),
                            'std' => '这里可以填写跳转ID',
                            'depends' => array(
                                array('detail_page_type', '=', 'Internal_pages'),
                            )
                        ),
                        'is_checkbox' => array(
                            'type' => 'checkbox',
                            'title' => '是否选中',
                            'std' => 0,
                        ),
                        'is_hover_img' => array(
                            'type' => 'checkbox',
                            'title' => '标题划过隐藏文字，展示图片',
                            'std' => 0,
                        ),
                        'hover_bg_img' => array(
                            'type' => 'media',
                            'title' => JText::_('图片'),
                            'depends' => array(
                                array('is_hover_img', '=', '1'),
                            ),
                            'std' => 'https://oss.lcweb01.cn/joomla/20220301/b5dda18263f088e3f61f6d2063888e5a.png'
                        ),
                    ),
                    'std' => array(
                        array(
                            'name' => '公司简介',
                        ),
                        array(
                            'name' => '资质荣誉',
                        ),
                        array(
                            'name' => '营销团队',
                        ),
                        array(
                            'name' => '合作伙伴',
                        ),
                        array(
                            'name' => '集团大事记',
                        ),
                        array(
                            'name' => '其他产业',
                        ),
                    ),
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                //轮播锚点
                'section_tab_item1' => array(
                    'title' => '内容列表',
                    'attr' => array(
                        'name' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '公司简介',
                        ),
                        'icon_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '图标',
                            'std' => '',
                        ),
                        'icon_img_hover' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '点击切换的图标',
                            'std' => '',
                        ),
                        'cont' => array(
                            'type' => 'text',
                            'title' => '内容标题',
                            'std' => '',
                        ),
                        'need_content' => array(
                            'type' => 'checkbox',
                            'title' => '是否开启内容',
                            'desc' => '仅在左侧样式为样式二时可用',
                            'std' => '0',
                            'depends' => array(
                                array('need_content', '=', 1),
                            ),
                        ),
                        'custom_content' => array(
                            'type' => 'checkbox',
                            'title' => '是否开启自定义内容',
                            'desc' => '仅在左侧样式为样式二时可用',
                            'std' => '1',
                        ),
                        'cont_list' => array(
                            'type' => 'editor',
                            'title' => '内容',
                            'std' => '',
                            'depends' => array(
                                array('need_content', '=', 1),
                                array('custom_content', '=', 1),
                            ),
                        ),
                        'content_list_item' => array(
                            'title' => '内容列表项',
                            'attr' => array(
                                'text' => array(
                                    'type' => 'text',
                                    'title' => '内容列表项',
                                    'std' => '',
                                ),
                            ),
                            'std' => array(
                                'text' => '内容列表项'
                            ),
                            'depends' => array(
                                array('need_content', '=', 1),
                                array('custom_content', '=', 0),
                            )
                        ),
                        'cont_list_cols' => array(
                            'type' => 'slider',
                            'title' => '内容列数',
                            'desc' => '仅在左侧样式为样式二时可用，需要在内容中写入列表，并且设置列表风格',
                            'std' => 1,
                            'max' => 5,
                            'depends' => array(
                                array('need_content', '=', 1),
                            )
                        ),
                        'banner_img' => array(
                            'type' => 'media',
                            'format' => 'image',
                            'title' => '内容图片',
                            'std' => '',
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择了解更多的跳转链接',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),

                    ),
                    'std' => array(
                        array(
                            'name' => '智慧校园软件',
                            'cont' => '一站式优选智慧校园应用平台',

                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220118/01761e5d891a6f25c2f4e19e919dd2af.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220118/c766482bf7c95e6e7196eaa3a05b3425.png',
                            'banner_img' => 'https://oss.lcweb01.cn/joomla/20220118/13e4c364124e8c60255e1867d2e023fe.png',

                        ),
                        array(
                            'name' => '教学实训科研',
                            'cont' => '智慧教学、仿真实训、科研计算',

                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220118/8484269064408395b1249d2d4581029f.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220118/3854cf729f90ea2c880a038d790103a5.png',
                            'banner_img' => 'https://oss.lcweb01.cn/joomla/20220118/5104a9bb0ce248a78e51c557d5ee0ca0.png',
                        ),
                        array(
                            'name' => '智慧校园运营服务',
                            'cont' => '规范流程、专业技能、自动可视',

                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220118/40c2067d1b78600bdefe916d8df17c41.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220118/107e77edc60d6399916388f5af31e84a.png',
                            'banner_img' => 'https://oss.lcweb01.cn/joomla/20220118/7533dbd07124c612cc720b5befe37498.png',
                        ),
                        array(
                            'name' => '基础架构',
                            'cont' => '校园网、公有云、私有云、混合云',

                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220118/22fb527dbc261798d0a891803ca7318e.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220118/3d03b54f9a785b4da8e8200b20181567.png',
                            'banner_img' => 'https://oss.lcweb01.cn/joomla/20220118/b5257922baea99ce03fcf8be88219711.png',
                        ),
                        array(
                            'name' => '信息安全',
                            'cont' => '合规建设、安全运营、应急响应',

                            'icon_img' => 'https://oss.lcweb01.cn/joomla/20220118/48746c8d2a1c903ebb08f9de4c24ea02.png',
                            'icon_img_hover' => 'https://oss.lcweb01.cn/joomla/20220118/5040f4e81bb321e407250eb9fc29f45c.png',
                            'banner_img' => 'https://oss.lcweb01.cn/joomla/20220118/0caa4b7df625551441e1513606e58696.png',
                        ),
                    ),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                'tz_style' => array(
                    'type' => 'select',
                    'title' => '跳转方式',
                    'desc' => '',
                    'values' => array(
                        '_blank' => '新窗口',
                        '_self' => '当前页面',
                    ),
                    'std' => '_blank',
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '样式设置'
                ),
                'list_style' => array(
                    'type' => 'select',
                    'title' => '导航标题前的样式',
                    'desc' => '',
                    'values' => array(
                        'none' => '无样式',
                        'outside' => '圆点',
                        'square' => '方形',
                    ),
                    'std' => 'outside',
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'list_style_after' => array(
                    'type' => 'select',
                    'title' => '导航间隔样式',
                    'desc' => '',
                    'values' => array(
                        'none' => '无样式',
                        'one' => '竖线',
                    ),
                    'std' => 'none',
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),

                // 2022.3.3
                'list_xhx_dh' => array(
                    'type' => 'checkbox',
                    'title' => '开启下划线动画',
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'list_xhx_color' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#818181',
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'list_xhx_width' => array(
                    'type' => 'slider',
                    'title' => '下划线宽度',
                    'std' => '30',
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                //

                'nav_position' => array(
                    'type' => 'select',
                    'title' => '导航位置',
                    'desc' => '',
                    'values' => array(
                        'flex-start' => '左',
                        'center' => '中',
                        'flex-end' => '右',
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '导航标题文字大小',
                    'desc' => '',
                    'max' => 300,
                    'min' => 10,
                    'std' => 16,

                ),
                'item_width_bai' => array(
                    'type' => 'checkbox',
                    'title' => '宽度使用百分比',
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'item_width_bfb' => array(
                    'type' => 'slider',
                    'title' => '导航项宽度',
                    'std' => 33,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                        array('item_width_bai', '=', '1'),
                    ),
                ),
                'item_width' => array(
                    'type' => 'slider',
                    'title' => '导航项宽度',
                    'desc' => '',
                    'max' => 300,
                    'min' => 10,
                    'std' => 100,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                        array('item_width_bai', '!=', '1'),
                    ),
                ),
                'item_height' => array(
                    'type' => 'slider',
                    'title' => '导航项高度',
                    'desc' => '',
                    'max' => 300,
                    'min' => 10,
                    'std' => 54,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'item_text_height' => array(
                    'type' => 'slider',
                    'title' => '导航项文字高度',
                    'desc' => '',
                    'max' => 300,
                    'min' => 10,
                    'std' => 54,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'title_jj' => array(
                    'type' => 'slider',
                    'title' => '导航标题间距',
                    'desc' => '',
                    'max' => 300,
                    'min' => 0,
                    'std' => 50,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'title_bottom_icon' => array(
                    'type' => 'checkbox',
                    'title' => '开启划过下划线三角形',
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'title_underline' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航文字选中下划线',
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'nav_mounting' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航吸顶效果',
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'nav_mounting_top' => array(
                    'type' => 'slider',
                    'title' => '吸顶区块距顶部距离',
                    'desc' => '',
                    'max' => 300,
                    'min' => 10,
                    'std' => 0,
                    'depends' => array(
                        array('nav_mounting', '=', '1'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                'nav_mounting_js' => array(
                    'type' => 'checkbox',
                    'title' => '导航点击事件跳转',
                    'desc' => '开启后导航跳转只能在本页面，且不是链接跳转',
                    'std' => 0,
                    'depends' => array(
                        array('nav_mounting', '=', '1'),
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'nav_mounting_active' => array(
                    'type' => 'checkbox',
                    'title' => '开启导航吸顶滑动选中效果',
                    'std' => 0,
                    'depends' => array(
                        array('nav_mounting', '=', '1'),
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'style' => array(
                    'type' => 'buttons',
                    'title' => '样式选项',
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '滑过',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('media_bj', '!=', 'ym2'),
                    ),
                ),
                'list_style_color' => array(
                    'type' => 'color',
                    'title' => '导航标题前的样式颜色',
                    'desc' => '',
                    'std' => '#000000',
                    'depends' => array(
                        array('style', '=', 'normal'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                'list_style_color_hg' => array(
                    'type' => 'color',
                    'title' => '导航标题前的样式滑过颜色',
                    'desc' => '',
                    'std' => '#ff0000',
                    'depends' => array(
                        array('style', '=', 'hover'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                'title_color' => array(
                    'type' => 'color',
                    'title' => '导航标题颜色',
                    'desc' => '',
                    'std' => '#000000',
                    'depends' => array(
                        array('style', '=', 'normal')
                    )
                ),
                'title_color_hg' => array(
                    'type' => 'color',
                    'title' => '导航标题滑过颜色',
                    'desc' => '',
                    'std' => '#000000',
                    'depends' => array(
                        array('style', '=', 'hover')
                    )
                ),
                'title_border_color_hg' => array(
                    'type' => 'color',
                    'title' => '导航标题下划线滑过颜色',
                    'desc' => '',
                    'std' => '#ff0000',
                    'depends' => array(
                        array('style', '=', 'hover'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                'title_bg_color' => array(
                    'type' => 'color',
                    'title' => '导航标题背景颜色',
                    'desc' => '',
                    'std' => 'none',
                    'depends' => array(
                        array('style', '=', 'normal'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                'title_bg_color_hg' => array(
                    'type' => 'color',
                    'title' => '导航标题背景滑过颜色',
                    'desc' => '',
                    'std' => 'none',
                    'depends' => array(
                        array('style', '=', 'hover'),
                        array('media_bj', '!=', 'ym2'),

                    )
                ),
                //轮播锚点
                'titlemd_color_hg' => array(
                    'type' => 'color',
                    'title' => '导航标题滑过颜色',
                    'desc' => '',
                    'std' => '#1196DB',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),
                'active_img' => array(
                    'type' => 'media',
                    'title' => '导航选中文字下的图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220119/7bfdb787f28471bf6470e48b69c24a72.png',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),

                'dh_width' => array(
                    'type' => 'slider',
                    'title' => '导航宽度',
                    'max' => 2000,
                    'min' => 100,
                    'std' => 1200,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),

                'cont_color' => array(
                    'type' => 'color',
                    'title' => '内容背景色',
                    'desc' => '',
                    'std' => '#f0f0f0',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),
                'cont_text_color' => array(
                    'type' => 'color',
                    'title' => '内容文本颜色',
                    'std' => '#000',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),
                'cont_text_size' => array(
                    'type' => 'slider',
                    'title' => '内容文字大小',
                    'max' => 50,
                    'min' => 0,
                    'std' => 24,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                'gd_color' => array(
                    'type' => 'color',
                    'title' => '按钮背景色',
                    'std' => '#1196DB',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),
                'gd_color_active' => array(
                    'type' => 'color',
                    'title' => '鼠标移入按钮背景色',
                    'std' => '#1196DB',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    )
                ),
                'cont_left' => array(
                    'type' => 'slider',
                    'title' => '内容文本左侧距离（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => 0,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                'cont_img_width' => array(
                    'type' => 'slider',
                    'title' => '内容右侧图片大小（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => 35,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                'cont_img_width_phone' => array(
                    'type' => 'slider',
                    'title' => '移动端内容右侧图片大小（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => 35,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array('media_style', '=', 'style2'),
                    ),
                ),
                'img_left' => array(
                    'type' => 'slider',
                    'title' => '内容图片左侧距离（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => 55,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                    ),
                ),
                'img_left_phone' => array(
                    'type' => 'slider',
                    'title' => '移动端内容图片左侧距离（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => 35,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array('media_style', '=', 'style2'),
                    ),
                ),

                // 左侧样式二
                'list_settings' => array(
                    'type' => 'separator',
                    'title' => '左侧样式设置',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    )
                ),
                'list_height' => array(
                    'type' => 'slider',
                    'title' => '左侧列表高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => array('md' => '', 'sm' => '', 'xs' => '300'),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                    'responsive' =>true
                ),
                'list_width' => array(
                    'type' => 'slider',
                    'title' => '左侧列表宽度（%）',
                    'max' => 100,
                    'min' => 0,
                    'std' => array('md' => 42, 'sm' => 42, 'xs' => 100),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                    'responsive' =>true
                ),
                'list_margin_top' => array(
                    'type' => 'slider',
                    'title' => '左侧列表上边距',
                    'max' => 500,
                    'min' => 0,
                    'std' => array('md' => 44, 'sm' => 44, 'xs' => 10),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                    'responsive' =>true
                ),
                'list_padding_left' => array(
                    'type' => 'slider',
                    'title' => '左侧列表左内边距',
                    'max' => 100,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                ),
                'list_mark_settings' => array(
                    'type' => 'select',
                    'title' => '内容列表圆点设置',
                    'std' => 'color',
                    'values' => array(
                        'color' => '颜色',
                        'img' => '图片'
                    ),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                ),
                'list_mark_size' => array(
                    'type' => 'slider',
                    'title' => '内容列表圆点大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                        array(
                            'list_mark_settings','=','color'
                        ),
                    ),
                ),
                'list_mark_color' => array(
                    'type' => 'color',
                    'title' => '内容列表圆点颜色',
                    'std' => '#0065cc',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                        array(
                            'list_mark_settings','=','color'
                        ),
                    ),
                ),
                'list_mark_img' => array(
                    'type' => 'media',
                    'title' => '内容列表圆点图片',
                    'std' => '',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                        array(
                            'list_mark_settings','=','img'
                        ),
                    ),
                ),
                'list_mark_width' => array(
                    'type' => 'slider',
                    'title' => '内容列表圆点大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '5',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                        array(
                            'list_mark_settings','=','img'
                        ),
                    ),
                ),
                'list_item_font_size' => array(
                    'type' => 'slider',
                    'title' => '左侧列表字号',
                    'max' => 50,
                    'min' => 0,
                    'std' => array('md' => 20, 'sm'=>16, 'xs' => 12),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                        array(
                            'list_mark_settings','=','color'
                        ),
                    ),
                    'responsive' => true,
                ),
                'list_item_lineheight' => array(
                    'type' => 'slider',
                    'title' => '左侧列表行高',
                    'std' => array('md' => 2,'sm' => 2, 'xs' => 2),
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                    'responsive' => true,
                ),
                'list_item_color' => array(
                    'type' => 'color',
                    'title' => '左侧列表字体颜色',
                    'std' => '#333',
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                ),
                'button_margin_top' => array(
                    'type' => 'slider',
                    'title' => '左侧按钮上边距',
                    'std' => array('md'=>80,'sm' => 60, 'xs'=>20),
                    'max' => 500,
                    'depends' => array(
                        array('media_bj', '=', 'ym2'),
                        array(
                            'media_style','=','style2'
                        ),
                    ),
                    'responsive' => true
                ),
            ),
        ),
    )
);
