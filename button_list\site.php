<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('resticted access');

class JwpagefactoryAddonButton_list extends JwpagefactoryAddons
{
    public function render()
    {
        $addon_id    = '#jwpf-addon-' . $this->addon->id;
        $settings    = $this->addon->settings;
        $jw_tab_item_button = (isset($settings->jw_tab_item_button) && $settings->jw_tab_item_button) ? $settings->jw_tab_item_button : '';
        
        $output = '';
        $output .= '
            <style>
                '.$addon_id.' * {
                    padding: 0;
                    margin: 0;
                    box-sizing: border-box;
                }
            
                '.$addon_id.' .ft1-a5 {
                    height: 100px;
                    position: relative;
                    cursor: pointer;
                }
                '.$addon_id.' .p-bot-a1-list:first-child img:last-child{display:none;}
                '.$addon_id.' .ft1-a6 {
                    width: 240px;
                    height: 0;
                    overflow: hidden;
                    position: absolute;
                    bottom: 75px;
                    left: calc(50% - 240px/2);
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a7 {
                    width: 100%;
                    height: 126px;
                    position: relative;
                    top: 1px;
                }
            
                '.$addon_id.' .ft1-a8 {
                    width: 100%;
                    height: 116px;
                    background: #d9012a;
                    padding: 10px;
                }
            
                '.$addon_id.' .ft1-a8>div:nth-child(1) {
                    height: 96px;
                    float: left;
                }
            
                '.$addon_id.' .i200 {
                    overflow: hidden;
                }
            
                '.$addon_id.' .i200>img {
                    height: 100%;
                }
            
                '.$addon_id.' .ft1-a8>div:nth-child(2) {
                    width: 104px;
                    height: 96px;
                    float: right;
                    display: table;
                }
            
                '.$addon_id.' .ft1-a8>div:nth-child(2)>div {
                    display: table-cell;
                    vertical-align: middle;
                    font-size: 12px;
                    line-height: 20px;
                    color: #fff;
                    font-weight: bold;
                }
            
                '.$addon_id.' .ft1-a10 {
                    height: 100px;
                    position: relative;
                }
            
                '.$addon_id.' .i200 {
                    overflow: hidden;
                }
            
                '.$addon_id.' .ft1-a11 img:nth-child(1) {
                    position: relative;
                    opacity: 1;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a11 img:nth-child(2) {
                    position: absolute;
                    top: 0;
                    right: 0;
                    opacity: 0;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a12 {
                    font-size: 16px;
                    line-height: 100px;
                    color: #454545;
                    float: left;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a5:hover .ft1-a6 {
                    height: 126px;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a5:hover .ft1-a12 {
                    color: #d90029;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .ft1-a2 {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    margin: 0 auto;
                }
            
                '.$addon_id.' .ft1-a4 {
                    width: calc(100%);
                    height: 100%;
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                }
            
                '.$addon_id.' .clear:after {
                    content: "";
                    display: block;
                    clear: both;
                }
            
                '.$addon_id.' .ft1-a11 {
                    height: 20px;
                    position: relative;
                    top: calc(50% - 20px/2);
                    float: left;
                    margin-right: 20px;
                }
            
                '.$addon_id.' .ft1-a9 {
                    width: 0;
                    height: 0;
                    border-top: 9px solid #d9012a;
                    border-left: 9px solid transparent;
                    border-right: 9px solid transparent;
                    position: absolute;
                    top: 136px;
                    left: 22px;
                }
            
                '.$addon_id.' .p-bot-a1-list {
                    margin-right: 17.5px;
                    margin-left: 17.5px;
                    position: relative;
                    z-index: 5;
                }
            
                '.$addon_id.' .p-bot-a1-list img:first-child {
                    width: 23px;
                    height: 23px;
                }
            
                '.$addon_id.' .p-bot-a1-list img:last-child {
                    width: 120px;
                    height: 120px;
                    max-width: 120px;
                    position: absolute;
                    bottom: 110%;
                    left: calc(50% - 120px/2);
                    transform: scale(0);
                    opacity: 0;
                    transition: 0.5s;
                }
            
                '.$addon_id.' .p-bot-a1-list:hover img:last-child {
                    transform: scale(1);
                    opacity: 1;
                    transition: 0.5s;
                }
                '.$addon_id.' .p-bot-a1 {
                    display: flex;
                    justify-content: center;
                    padding-bottom: 1.15rem;
                }
            
                @media only screen and (max-width: 768px) {
                    '.$addon_id.' .ft1-a2 {
                        display: none !important;
                    }
                }
            
                @media only screen and (min-width: 768px) {
                    '.$addon_id.' .p-bot-a1 {
                        display: none !important;
                    }
                }
                '.$addon_id.' .p-bot-a1-list a {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    display: block;
                }
            </style>
        ';
        $output .= '
            <div class="ft1-a2 clear">
                <div class="ft1-a4">
        ';
        foreach($jw_tab_item_button as $k => $v)
        {
            if($v->detail_page_id){
                $idurl= $this->removeqsvar($_SERVER['REQUEST_URI'],['page']);
                $arrray=explode('&',$idurl);
                foreach ($arrray as $key=>$value){
                    if(substr($value,0,3)=='id='){
                        $arrray[$key]='id='.base64_encode($v->detail_page_id);
                    }
                }
    
                $return=implode('&',$arrray);
            }
            else
            {
                $return = $v->detail_page;
            }
            $output .='
                <div class="ft1-a5">
            ';
            if($v->select_show=='tp')
            {
                $output.='
                    <div class="ft1-a6">
                        <div class="ft1-a7">
                            <div class="ft1-a8 clear">
                                <div class="i200">
                                    <img src="'.$v->button_img_hr.'"
                                        oncontextmenu="return false;">
                                </div>
                                <div>
                                    <div>'.$v->button_desc.'</div>
                                </div>
                            </div>
                            <div class="ft1-a9"></div>
                        </div>
                    </div>
                ';
            }
            $output .='
                    <div class="ft1-a10">
                        <a href="'.$return.'"
                            target="'.$v->target.'">
                            <div class="ft1-a11 i200">
                                <img src="'.$v->button_img.'" oncontextmenu="return false;">
                                <img src="'.$v->button_img_hg.'" oncontextmenu="return false;">
                            </div>
                        </a>
                        <div class="ft1-a12">'.$v->title.'</div>
                    </div>
                </div>
            ';
        }
        $output .='</div>
            </div>';
        $output .= '
            <div class="p-bot-a1">
        '; 
        foreach($jw_tab_item_button as $k => $v)
        {
            if($v->detail_page_id){
                $idurl= $this->removeqsvar($_SERVER['REQUEST_URI'],['page']);
                $arrray=explode('&',$idurl);
                foreach ($arrray as $key=>$value){
                    if(substr($value,0,3)=='id='){
                        $arrray[$key]='id='.base64_encode($v->detail_page_id);
                    }
                }
    
                $return=implode('&',$arrray);
            }
            else
            {
                $return = $v->detail_page;
            }
            $output .='
                <div class="p-bot-a1-list wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s"
                style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
                    <img src="'.$v->button_img.'" alt="" oncontextmenu="return false;">
            ';
            if($v->select_show=='tz')
            {
                $output .='
                    <a href="'.$return.'" target="'.$v->target.'"></a>
                ';
            }
            $output .='
                    <img src="'.$v->button_img_hr.'" alt=""
                        oncontextmenu="return false;">
                </div>
            ';
        }
        $output .='</div>';
        return $output;
    }

    public function css()
    {
    }

    public function scripts()
    {
        // $scripts = array(JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper.min.js');
        // return $scripts;
    }

    /* 带省略号分页js */
    public function js()
    {

    }

    //去掉url指定参数
    public function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
