<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'minceshi',
        'title' => JText::_('图像遮罩2'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '图片',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => '标题',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => '标题内容',
                ),
                'title_size' => array(
                    'type' => 'slider',
                    'title' => '标题字体大小',
                    'max' => 2000,
                    'min' => 12,
//                    'std' => '14',
                    'std' => array('md' => '14', 'sm' => '14', 'xs' => '14'),
                    'responsive' => true,
                ),
                'title_align' => array(
                    'type' => 'select',
                    'title' => '标题文字对齐方式',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中',
                        'right' => '右对齐',
                    ),
                    'std' => 'left'
                ),
                'title_padding' => array(
                    'type' => 'padding',
                    'title' => '标题内边距',
                    'std' => '',
                ),
                'title_line' => array(
                    'type' => 'checkbox',
                    'title' => '开启标题下划线',
                    'std' => '0',
                ),
                'title_line_w' => array(
                    'type' => 'slider',
                    'title' => '下划线宽度',
                    'std' => array('md' => '55', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('title_line', '=', '1')
                    ),
                ),
                'title_line_h' => array(
                    'type' => 'slider',
                    'title' => '下划线高度',
                    'std' => array('md' => '2', 'sm' => '', 'xs' => ''),
                    'responsive' => true,
                    'depends' => array(
                        array('title_line', '=', '1')
                    ),
                ),
                'title_line_color' => array(
                    'type' => 'color',
                    'title' => '下划线颜色',
                    'std' => '#FFFFFF',
                    'depends' => array(
                        array('title_line', '=', '1'),
                    )
                ),
                'title_line_align' => array(
                    'type' => 'select',
                    'title' => '下划线对齐方式',
                    'values' => array(
                        'flex-start' => '左对齐',
                        'center' => '居中',
                        'flex-end' => '右对齐',
                    ),
                    'std' => 'flex-start'
                ),
                'line_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('下划线内边距'),
                    'std' => '',
                    'depends' => array(
                        array('title_line', '=', '1'),
                    )
                ),
                'model_height' => array(
                    'type' => 'slider',
                    'title' => '模块高度',
                    'max' => 2000,
                    'min' => 200,
                    'placeholder' => '350px',
                    'std' => array('md' => '300', 'sm' => '300', 'xs' => '300'),
                    'responsive' => true,

                ),
                'model_cover' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('自定义遮罩层高度'),
                    'std' => '0',
                ),
                'model_cover_height' => array(
                    'type' => 'slider',
                    'title' => '遮罩层高度',
                    'std' => array('md' => '300', 'sm' => '300', 'xs' => '300'),
                    'responsive' => true,
                    'depends' => array(
                        array('model_cover', '=', '1')
                    ),
                ),
                'bg_image' => array(
                    'type' => 'media',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/94d79392fb13f5871660e4eaf79dc1f0.jpeg'
                ),
                'bg_image_height' => array(
                    'type' => 'slider',
                    'title' => '背景图片高度',
                    'max' => 2000,
                    'min' => 200,
                    'placeholder' => '350px',
                    'std' => array('md' => '300', 'sm' => '300', 'xs' => '300'),
                    'responsive' => true,
                ),
                'text' => array(
                    'type' => 'editor',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CONTENT_CONTENT'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_CONTENT_CONTENT_DESC'),
                    'std' => '录音从夏季暴风的噼里啪啦声开始。后来，一个飘忽不定的声音响起，就像一个没有完全被调谐的广播电台的声音一样，一会儿淹没了这种喋喋不休的声音。这些是我们遇到的声音.4月26日，美国国家航空航天局(NASA)的卡西尼号(Cassini)宇宙飞船穿过土星与其最内环之间的缝隙，这是它22次遭遇中的第一次，之后它将于9月进入土星大气层.'
                ),
                'content_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内容内边距'),
                    'std' => '',
                    'responsive' => true,
                ),
                'overlay_mode' => array(
                    'type' => 'buttons',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_IMAGE_OVERLAY_OVERLAY_MODE'),
                    'std' => 'normal',
                    'values' => array(
                        array(
                            'label' => '正常',
                            'value' => 'normal'
                        ),
                        array(
                            'label' => '移入',
                            'value' => 'hover'
                        ),
                    ),
                    'tabs' => true,
                ),
                'small_image' => array(
                    'type' => 'media',
                    'title' => '小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                    'depends' => array(
                        array('overlay_mode', '=', 'normal'),
                    )
                ),
                'small_image_top' => array(
                    'type' => 'slider',
                    'title' => '图标距上边距距离',
                    'max' => 2000,
                    'min' => 0,
                    'placeholder' => '120px',
                    'std' => array('md' => '120', 'sm' => '120', 'xs' => '120'),
                    'responsive' => true,
                    'depends' => array(
                        array('overlay_mode', '=', 'normal'),
                    )
                ),
                'overlay_color_n' => array(
                    'type' => 'color',
                    'title' => '遮罩层颜色',
                    'std' => 'rgba(0, 0, 0, 0.5)',
                    'depends' => array(
                        array('overlay_mode', '=', 'normal'),
                    )
                ),
                'small_image_hover' => array(
                    'type' => 'media',
                    'title' => '移入后小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                    'depends' => array(
                        array('overlay_mode', '=', 'hover'),
                    )
                ),
                'small_image_top_hover' => array(
                    'type' => 'slider',
                    'title' => '移入后图标距上边距距离',
                    'max' => 2000,
                    'min' => 0,
                    'placeholder' => '120px',
                    'std' => array('md' => '80', 'sm' => '80', 'xs' => '80'),
                    'responsive' => true,
                    'depends' => array(
                        array('overlay_mode', '=', 'hover'),
                    )
                ),
                'overlay_color' => array(
                    'type' => 'color',
                    'title' => '移入后遮罩层颜色',
                    'std' => 'rgba(0, 91, 234, 0.5)',
                    'depends' => array(
                        array('overlay_mode', '=', 'hover'),
                    )
                ),
                'bg_s_hover' => array(
                    'type' => 'checkbox',
                    'title' => '移入背景图放大',
                    'std' => '0',
                    'depends' => array(
                        array('overlay_mode', '=', 'hover'),
                    )
                ),
            ),
        ),
    )
);
