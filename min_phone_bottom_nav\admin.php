<?php

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'min_phone_bottom_nav',
        'title' => JText::_('手机底部导航'),
        'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_LIST_DESC'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '底部导航总体设置'
                ),
                'showBtnCenter' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示中心按钮'),
                    'desc' => JText::_('开启显示中心按钮'),
                    'std' => 1,
                ),
                'btn_item_custom' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自定义按钮个数'),
                    'desc' => JText::_('开启后可以设置按钮显示隐藏'),
                    'std' => 0,
                ),
                'bg' => array(
					'type' => 'buttons',
					'title' => '背景',
					'std' => 'color',
					'values' => array(
						array(
							'label' => '背景颜色',
							'value' => 'color'
						),
						array(
							'label' => '背景图片',
							'value' => 'img'
						),
					),
					'tabs' => true,
				),
                'bg_color' => array(
					'type' => 'color',
					'title' => '背景颜色',
					'std' => '#000',
                    'depends' => array(
                        array('bg', '=', 'color'),
                    )
				),
                'bg_image' => array(
					'type' => 'media',
					'title' => '背景图片',
					'std' => '',
                    'depends' => array(
                        array('bg', '=', 'img'),
                    )
				),
                'font_color' => array(
					'type' => 'color',
					'title' => '字体颜色',
					'std' => '#fff'
				),
				'font_size' => array(
					'type' => 'slider',
					'title' => '字体大小',
					'std' => '16'
				),
				'font_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('内间距'),
                    'std' => '10px 10px 10px 10px',
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '底部导航菜单项'
                ),
                'btn_item' => array(
                    'type' => 'buttons',
                    'title' => '底部菜单项',
                    'desc' => '拨打电话按钮链接格式(tel:13888888888);发送短信按钮链接格式(sms:13888888888)',
                    'std' => 'btn01',
                    'values' => array(
                        array(
                            'label' => '菜单1',
                            'value' => 'btn01'
                        ),
                        array(
                            'label' => '菜单2',
                            'value' => 'btn02'
                        ),
                        array(
                            'label' => '中心按钮',
                            'value' => 'btn03'
                        ),
                        array(
                            'label' => '菜单3',
                            'value' => 'btn04'
                        ),
                        array(
                            'label' => '菜单4',
                            'value' => 'btn05'
                        ),

                    ),
                    'tabs' => true,
                ),
                'showBtn1' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示按钮1'),
                    'std' => 1,
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                    ),
                ),
                'btn1_image' => array(
                    'type' => 'media',
                    'title' => '按钮1小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/6376072799d4440baeeaea3cd0f091f3.png',
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                    )
                ),
                'btn1link_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否跳转站内链接'),
                    'std' => 0,
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                    ),
                ),
                'btn1link_' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                        array('btn1link_from', '=', 1),
                    )
                ),
                'btn1link' => array(
					'type' => 'text',
					'title' => '按钮1链接',
					'std' => 'tel:13888888888',
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                        array('btn1link_from', '!=', 1),
                    )
				),
				'btn1font' => array(
					'type' => 'text',
					'title' => '按钮1文字',
					'std' => '产品',
                    'depends' => array(
                        array('btn_item', '=', 'btn01'),
                    )
				),
                'showBtn2' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示按钮2'),
                    'std' => 1,
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                    ),
                ),
                'btn2_image' => array(
                    'type' => 'media',
                    'title' => '按钮2小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/980894e0f051c4364f1fbdf80674edf8.png',
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                    )
                ),
                'btn2link_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否跳转站内链接'),
                    'std' => 0,
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                    ),
                ),
                'btn2link_' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                        array('btn2link_from', '=', 1),
                    )
                ),
                'btn2link' => array(
					'type' => 'text',
					'title' => '按钮2链接',
					'std' => 'sms:13888888888',
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                        array('btn2link_from', '!=', 1),
                    )
				),
				'btn2font' => array(
					'type' => 'text',
					'title' => '按钮2文字',
					'std' => '案例',
                    'depends' => array(
                        array('btn_item', '=', 'btn02'),
                    )
				),
                'btn3_image' => array(
                    'type' => 'media',
                    'title' => '中心按钮小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/dbc5bd3cf3b456deb7194d36145f973c.png',
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'btn3link_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否跳转站内链接'),
                    'std' => 0,
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                    ),
                ),
                'btn3link_' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('btn3link_from', '=', 1),
                    )
                ),
                'btncenterlink' => array(
                    'type' => 'text',
                    'title' => '中心按钮链接',
                    'std' => '',
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                        array('btn3link_from', '!=', 1),
                    )
                ),
                'btncenterfont' => array(
                    'type' => 'text',
                    'title' => '中心按钮文字',
                    'std' => '首页',
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'font_color_center' => array(
                    'type' => 'color',
                    'title' => '中间导航字体颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'font_bgcolor_center' => array(
                    'type' => 'color',
                    'title' => '中间导航背景颜色',
                    'std' => '#d30000',
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'center_btn_w' => array(
                    'type' => 'slider',
                    'title' => '中间导航宽度',
                    'max' => 600,
                    'min' => 0,
                    'std' => 80,
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'center_btn_h' => array(
                    'type' => 'slider',
                    'title' => '中间导航高度',
                    'max' => 600,
                    'min' => 0,
                    'std' => 80,
                    'depends' => array(
                        array('btn_item', '=', 'btn03'),
                        array('showBtnCenter', '=', '1'),
                    )
                ),
                'showBtn4' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示按钮3'),
                    'std' => 1,
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                    ),
                ),
                'btn4_image' => array(
                    'type' => 'media',
                    'title' => '按钮四小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/46bf6cc65dacb44b1931688fbcf5781c.png',
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                    )
                ),
                'btn4link_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否跳转站内链接'),
                    'std' => 0,
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                    ),
                ),
                'btn4link_' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                        array('btn4link_from', '=', 1),
                    )
                ),
                'btn4link' => array(
					'type' => 'text',
					'title' => '按钮4链接',
					'std' => '',
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                        array('btn4link_from', '!=', 1),
                    )
				),
				'btn4font' => array(
					'type' => 'text',
					'title' => '按钮4文字',
					'std' => '地图',
                    'depends' => array(
                        array('btn_item', '=', 'btn04'),
                    )
				),
                'showBtn5' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('显示按钮4'),
                    'std' => 1,
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                    ),
                ),
                'btn5_image' => array(
                    'type' => 'media',
                    'title' => '按钮五小图标',
                    'std' => 'https://oss.lcweb01.cn/joomla/20210811/26aca3c2ae4317a04f3584781bcee9d4.png',
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                    )
                ),
                'btn5link_from' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否跳转站内链接'),
                    'std' => 0,
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                    ),
                ),
                'btn5link_' => array(
                    'type' => 'select',
                    'title' => '内部页面链接',
                    'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                        array('btn5link_from', '=', 1),
                    )
                ),
                'btn5link' => array(
					'type' => 'text',
					'title' => '按钮5链接',
					'std' => 'tel:13888888888',
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                        array('btn5link_from', '!=', 1),
                    )
				),
				'btn5font' => array(
					'type' => 'text',
					'title' => '按钮5文字',
					'std' => '电话',
                    'depends' => array(
                        array('btn_item', '=', 'btn05'),
                    )
				),
                'part03' => array(
                    'type' => 'separator',
                    'title' => '底部导航图标统一设置'
                ),
				'img_width' => array(
					'type' => 'slider',
					'title' => '图标宽度',
					'std' => '30'
				),
				'img_height' => array(
					'type' => 'slider',
					'title' => '图标高度',
					'std' => '30'
				),
            ),
        ),
    )
);
