<?php
defined('_JEXEC') or die('resticted aceess');
JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'jw_m_nav_one',
        'title' => JText::_('手机导航1'),
        'category' => '导航',
        'attr' => array(
            'general' => array(
                'dh_style' => array(
                    'type' => 'select',
                    'title' => JText::_('手机导航样式'),
                    'desc' => JText::_('手机导航样式'),
                    'values' => array(
                        'type1' => '样式1',
                        'type2' => '样式2',
                        'type3' => '样式3',
                        'type4' => '样式4',
                        'type5' => '样式5',
                    ),
                    'std' => 'type1',
                ),
                'logoImage_type2' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220805/1c123aeb08d348bba8857580c151e5bf.png',
                    'depends' => array(array('dh_style', '=', 'type2')),
                ),
                'bottonImage_type2' => array(
                    'type' => 'media',
                    'title' => JText::_('按钮图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/jzt/255/image/20240330/a490ebeb0d17de8772ec2422e41412d2.png',
                    'depends' => array(array('dh_style', '=', 'type2')),
                ),
                'nav_color_font_type2' => array(
                    'type' => 'color',
                    'title' => JText::_('导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(array('dh_style', '=', 'type2')),
                ),
                'logoImage' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'logoImage' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'logoWidth' => array(
                    'type' => 'slider',
                    'title' => JText::_('logo宽度'),
                    'std' => 50,
                    'max' => 600,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'logoHeight' => array(
                    'type' => 'slider',
                    'title' => JText::_('logo高度'),
                    'std' => 30,
                    'max' => 600,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_border' => array(
                    'type' => 'color',
                    'title' => JText::_('导航按钮颜色'),
                    'std' => '#333333',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'nav_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('导航背景颜色'),
                    'std' => '#fffff',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('导航栏高度'),
                    'std' => '42',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('导航栏内边距(格式：数字px)'),
                    'std' => '0px 0px 0px 0px',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'nav_color_bg_xz' => array(
                    'type' => 'color',
                    'title' => JText::_('选中导航背景颜色'),
                    'std' => '',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_font_xz' => array(
                    'type' => 'color',
                    'title' => JText::_('选中导航字体颜色'),
                    'std' => '',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_border_left' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航竖线颜色'),
                    'std' => '#cc0000',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_border_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航竖线高度'),
                    'std' => 16,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航竖线宽度'),
                    'std' => 2,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_border_marginRight' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航竖线右边距'),
                    'std' => 10,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_border_br' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航分割线颜色'),
                    'std' => '#3f3f3f',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航字体颜色'),
                    'std' => '#cc0000',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_color_font_color_hover' => array(
                    'type' => 'color',
                    'title' => JText::_('一级导航点击后字体颜色'),
                    'std' => '#e81748',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航字体大小'),
                    'std' => 14,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_arrow_img' => array(
                    'type' => 'media',
                    'title' => JText::_('一级导航箭头图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_arrow' => array(
                    'type' => 'slider',
                    'title' => JText::_('一级导航箭头大小'),
                    'std' => 6,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_font_w' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('一级导航是否加粗'),
                    'std' => 0,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'nav2_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航背景颜色'),
                    'std' => '#eeeeee',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav_s_padding' => array(
                    'type' => 'padding',
                    'title' => JText::_('二级导航内边距(格式：数字px)'),
                    'std' => '10px 10px 10px 10px',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'nav2_color_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('二级导航字体颜色'),
                    'std' => '#cc0000',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav2_font_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体大小'),
                    'std' => 14,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav2_lineHeight' => array(
                    'type' => 'slider',
                    'title' => JText::_('二级导航字体行高'),
                    'std' => '34',
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),
                'nav2_font_w' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('二级导航是否加粗'),
                    'std' => 0,
                    'depends' => array(array('dh_style', '=', 'type1')),
                ),

                'nav3_settings'=> array(
                    'title' => JText::_('样式3设置项'),
                    'type' => 'buttons',
                    'tabs' => true,
                    'std' => 'nav_row',
                    'values' => array(
                        array(
                            'label'=>'导航条',
                            'value'=>'nav_row'
                        ),
                        array(
                            'label'=>'导航详情',
                            'value'=>'nav_section'
                        )
                    ),
                    'depends' => array(array('dh_style', '=','type3'))
                ),
                'nav3_fixed' => array(
                    'title' => "导航条是否需要定位",
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_bg' => array(
                    'title' => "导航条背景色",
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_logo' => array(
                    'title' => "导航条logo",
                    'type' => 'media',
                    'std' => '/components/com_jwpagefactory/addons/m_nav_one/assets/img/logo_nav3.png',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_height' => array(
                    'title' => "导航条高度",
                    'type' => 'slider',
                    'std' => 60,
                    'max' => 500,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_padding' => array(
                    'title' => "导航条内边距",
                    'type' => 'padding',
                    'std' => '13px 4.5%',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_button_color' => array(
                    'title' => "导航条按钮颜色",
                    'type' => 'color',
                    'std' => '#333',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_row'),
                    )
                ),
                'nav3_nav_width' => array(
                    'title' => "导航详情宽度(%)",
                    'type' => 'slider',
                    'std' => 80,
                    'max' => 100,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_nav_bg' => array(
                    'title' => "导航详情背景色",
                    'type' => 'color',
                    'std' => '#222',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_nav_button_color' => array(
                    'title' => "导航详情关闭按钮颜色",
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_nav_item_height' => array(
                    'title' => "导航项高度",
                    'type' => 'slider',
                    'std' => 56,
                    'max' => 200,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_nav_item_width' => array(
                    'title' => "导航项宽度（%）",
                    'type' => 'slider',
                    'std' => array('md' => 58,'sm' => 78, 'xs' => 78),
                    'max' => 200,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    ),
                    'responsive'=> true
                ),
                'nav3_nav_item_font_size' => array(
                    'title' => "导航项字号",
                    'type' => 'slider',
                    'std' => 24,
                    'max' => 50,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_second_nav_item_font_size' => array(
                    'title' => "二级导航项字号",
                    'type' => 'slider',
                    'std' => 20,
                    'max' => 50,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_nav_item_color' => array(
                    'title' => "导航项字体颜色",
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_tel_show' => array(
                    'title' => "是否显示服务热线",
                    'type' => 'checkbox',
                    'std' => 1,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                    )
                ),
                'nav3_tel_title' => array(
                    'title' => "服务热线标题",
                    'type' => 'text',
                    'std' => '服务热线',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                        array('nav3_tel_show', '=', 1),
                    )
                ),
                'nav3_tel' => array(
                    'title' => "服务热线",
                    'type' => 'text',
                    'std' => '************',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                        array('nav3_tel_show', '=', 1),
                    )
                ),
                'nav3_tel_color' => array(
                    'title' => "导航热线颜色",
                    'type' => 'color',
                    'std' => '#fff',
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                        array('nav3_tel_show', '=', 1),
                    )
                ),
                'nav3_tel_title_font_size' => array(
                    'title' => "导航热线标题字号",
                    'type' => 'slider',
                    'std' => 14,
                    'max' => 50,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                        array('nav3_tel_show', '=', 1),
                    )
                ),
                'nav3_tel_font_size' => array(
                    'title' => "导航热线字号",
                    'type' => 'slider',
                    'std' => 18,
                    'max' => 50,
                    'depends' => array(
                        array('dh_style', '=', 'type3'),
                        array('nav3_settings', '=', 'nav_section'),
                        array('nav3_tel_show', '=', 1),
                    )
                ),
                //布局4
                'fixed_type4' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('关闭跟随滚动'),
                    'std' => '0',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'logoImage_type4' => array(
                    'type' => 'media',
                    'title' => JText::_('logo图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220803/f8a5437faf1133e6947a0274ea343aaf.png',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'bg_type4' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#bfb77b',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'radius_type4' => array(
                    'type' => 'slider',
                    'title' => JText::_('圆角'),
                    'std' => '10',
                    'max' => '50',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'bgcolor_tc' => array(
                    'type' => 'color',
                    'title' => JText::_('弹出背景色'),
                    'std' => '#f00',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'bglogo_tc' => array(
                    'type' => 'media',
                    'title' => JText::_('弹出logo图片'),
                    'show_input' => true,
                    'std' => 'https://oss.lcweb01.cn/joomla/20220803/f8a5437faf1133e6947a0274ea343aaf.png',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'dhcolor' => array(
                    'type' => 'color',
                    'title' => JText::_('导航字体颜色'),
                    'std' => '#fff',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'dhcolor_xhx' => array(
                    'type' => 'color',
                    'title' => JText::_('导航下划线颜色'),
                    'std' => '#fff',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'select_jt' => array(
                    'type' => 'media',
                    'title' => JText::_('下拉箭头'),
                    'std' => 'https://oss.lcweb01.cn/joomla/20220802/69d13bb71d7999b651e3dd819402aa83.png',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'tel_type4' => array(
                    'type' => 'text',
                    'title' => JText::_('电话'),
                    'std' => '************',
                    'depends' => array(array('dh_style', '=', 'type4')),
                ),
                'jw_image_type4' => array(
                    'title' => JText::_('底部小图标项'),
                    'std' => array(
                        array(
                            'title4' => '抖音',
                            'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/2cc2e5e752f4a6ce30d73f9c8aa1f50c.png',
                            'fanshi' => 'chu1',
                            'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title4' => '视频号',
                            'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/ac716e2bf4e719d8ba42873863886012.png',
                            'fanshi' => 'chu1',
                            'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                        array(
                            'title4' => '微信',
                            'logo4' => 'https://oss.lcweb01.cn/joomla/20220802/0dec44bb71131cb1c2fab5ea8836049e.png',
                            'fanshi' => 'chu1',
                            'image_xtb' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                            'target' => '',
                            'detail_page_id' => 0,
                        ),
                    ),
                    'attr' => array(
                        'title4' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '标题',
                        ),
                        'logo4' => array(
                            'type' => 'media',
                            'title' => '小图标',
                            'std' => '',
                        ),
                        'fanshi' => array(
                            'type' => 'select',
                            'title' => '触发方式',
                            'values' => array(
                                'chu1' => '划过显示二维码',
                                'chu2' => '点击跳转链接',
                            ),
                            'std' => 'chu1',
                        ),
                        'image_xtb' => array(
                            'type' => 'media',
                            'title' => '二维码',
                            'std' => '',
                            'depends' => array(array('fanshi', '=', 'chu1')),
                        ),

                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                            'depends' => array(array('fanshi', '=', 'chu2')),

                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                            'depends' => array(
                                array('fanshi', '=', 'chu2'),
                                array('tz_page_type', '=', 'Internal_pages'),
                                
                            ),
                            
                        ),
                        'detail_page' => array(
                            'type' => 'text',
                            'title' => '跳转链接',
                            'depends' => array(
                                array('fanshi', '=', 'chu2'),
                                array('tz_page_type', '=', 'external_links'),

                            ),
                            
                        ),
                        'target' => array(
                            'type' => 'select',
                            'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB'),
                            'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINK_NEWTAB_DESC'),
                            'values' => array(
                                '' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_SAME_WINDOW'),
                                '_blank' => JText::_('COM_JWPAGEFACTORY_ADDON_GLOBAL_TARGET_NEW_WINDOW'),
                            ),
                            'depends' => array(array('fanshi', '=', 'chu2')),

                        ),
                    ),
                    'depends' => array(array('dh_style', '=', 'type4')),

                ),

                // 'douyin_type4' => array(
                //     'type' => 'media',
                //     'title' => JText::_('抖音二维码'),
                //     'std' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                //     'depends' => array(array('dh_style', '=', 'type4')),
                // ),
                // 'sph_type4' => array(
                //     'type' => 'media',
                //     'title' => JText::_('视频号二维码'),
                //     'std' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                //     'depends' => array(array('dh_style', '=', 'type4')),
                // ),
                // 'wx_type4' => array(
                //     'type' => 'media',
                //     'title' => JText::_('微信二维码'),
                //     'std' => 'https://oss.lcweb01.cn/joomla/20210817/e8544b96304e105b412bd65fa959d000.jpg',
                //     'depends' => array(array('dh_style', '=', 'type4')),
                // ),

                
                //  样式5
                'nav5_settings' => array(
                    'title' => "导航样式5配置项",
                    'type' => 'buttons',
                    'std' => 'button',
                    'tabs' => true,
                    'values' => array(
                        array(
                            'label' => '按钮',
                            'value' => 'button'
                        ),
                        array(
                            'label' => '列表',
                            'value' => 'list'
                        ),
                        array(
                            'label' => '列表项',
                            'value' => 'item'
                        ),
                    ),
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                    )
                ),
                'nav5_button_size' => array(
                    'title' => "导航按钮大小",
                    'type' => 'slider',
                    'std' => 50,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_right' => array(
                    'title' => "导航按钮右边距（%）",
                    'type' => 'slider',
                    'std' => 0,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_top' => array(
                    'title' => "导航按钮上边距（%）",
                    'type' => 'slider',
                    'std' => 0,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_bg' => array(
                    'title' => "导航按钮背景色",
                    'type' => 'color',
                    'std' => 'transparent',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_line_width' => array(
                    'title' => "导航按钮线条宽度",
                    'type' => 'slider',
                    'std' => 25,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_line_height' => array(
                    'title' => "导航按钮线条高度",
                    'type' => 'slider',
                    'std' => 3,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_button_line_color' => array(
                    'title' => "导航按钮线条颜色",
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'button'),
                    )
                ),
                'nav5_list_padding' => array(
                    'title' => "导航列表内边距",
                    'type' => 'padding',
                    'std' => '50px 5% 50px 5%',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'list'),
                    )
                ),
                'nav5_list_bg_img' => array(
                    'title' => "导航列表背景图",
                    'type' => 'media',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220816/33ebe3002194ca278fd0e003b09fbb8e.png',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'list'),
                    )
                ),
                'nav5_list_bg_color' => array(
                    'title' => "导航列表背景色",
                    'type' => 'color',
                    'std' => '#fcf5ee',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'list'),
                    )
                ),
                'nav5_item_second_margin' => array(
                    'title' => "二级导航列表边距",
                    'type' => 'margin',
                    'std' => '10px 0 10px 0',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'list'),
                    )
                ),
                'nav5_item_color' => array(
                    'title' => "导航列表项字体颜色",
                    'type' => 'color',
                    'std' => '#000',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_item_size' => array(
                    'title' => "导航列表项字号",
                    'type' => 'slider',
                    'max' => 50,
                    'std' => 16,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_item_margin' => array(
                    'title' => "导航列表项外边距",
                    'type' => 'margin',
                    'std' => '1.6em 0 1.6em 0',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_item_padding' => array(
                    'title' => "导航列表项内边距",
                    'type' => 'padding',
                    'std' => '0 0 0 0',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_item_align' => array(
                    'title' => "导航列表项对齐方式",
                    'type' => 'select',
                    'std' => 'center',
                    'values' => array(
                        'left' => '左对齐',
                        'center' => '居中对齐',
                        'right' => '右对齐',
                    ),
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_second_item_size' => array(
                    'title' => "二级导航列表项字号",
                    'type' => 'slider',
                    'max' => 50,
                    'std' => 14,
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_second_item_margin' => array(
                    'title' => "导航列表项外边距",
                    'type' => 'margin',
                    'std' => '1.4em 0 1.4em 0',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
                'nav5_second_item_padding' => array(
                    'title' => "导航列表项内边距",
                    'type' => 'padding',
                    'std' => '0 0 0 0',
                    'depends' => array(
                        array('dh_style', '=', 'type5'),
                        array('nav5_settings', '=', 'item'),
                    )
                ),
            ),
        ),
    )
);
