<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'advi_button',
        'title' => JText::_('QQ咨询按钮'),
        'desc' => JText::_('QQ咨询按钮'),
        'category' => '按钮',
        'attr' => array(
            'general' => array(
                'consult_style' => array(
                    'type' => 'select',
                    'title' => '布局样式',
                    'values' => array(
                        'theme01' => '布局01',
                        'theme02' => '布局02',
                    )
                ),
                'text_id' => array(
                    'type' => 'select',
                    'title' => JText::_('选择对应的QQ号'),
                    'desc' => JText::_('QQ号在后台填写'),
                    'values' => JwPageFactoryBase::getQqList($site_id, $company_id)['list'],
                ),
                'search_button_text' => array(
                    'type' => 'text',
                    'title' => JText::_('按钮文本'),
                    'desc' => JText::_(''),
                    'std' => 'QQ咨询',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'search_wz' => array(
                    'type' => 'select',
                    'title' => '按钮位置',
                    'values' => array(
                        'left' => '左',
                        'right' => '右',
                    ),
                    'std' => 'left',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),

                'button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮颜色'),
                    'std' => '#eeeeee',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'button_font_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮字体颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'button_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮宽度'),
                    'max' => 300,
                    'min' => 0,
                    'std' => '200',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'button_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('按钮边框颜色'),
                    'std' => '#000000',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'input_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮高度'),
                    'max' => 300,
                    'min' => 0,
                    'std' => '60',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'button_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('按钮圆角'),
                    'max' => 300,
                    'min' => 0,
                    'std' => '10',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                'button_top' => array(
                    'type' => 'slider',
                    'title' => '按钮顶端距离(单位%)',
                    'max' => 100,
                    'min' => 0,
                    'std' => '65',
                    'depends' => array(
                        array('consult_style', '!=', 'theme02'),
                    ),
                ),
                // 布局二
                'theme02_part01' => array(
                    'type' => 'separator',
                    'title' => '背景配置部分',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_bg_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '背景图片',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211221/da664c5bf545b0c4a6c388ec1827cfb2.jpg',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_bg_img_w' => array(
                    'type' => 'slider',
                    'title' => '背景图的宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 624,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_bg_img_h' => array(
                    'type' => 'slider',
                    'title' => '背景图的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 292,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_bg_img_p' => array(
                    'type' => 'padding',
                    'title' => '背景图内边距',
                    'std' => '80px 60px 70px 120px',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_part02' => array(
                    'type' => 'separator',
                    'title' => '关闭按钮配置部分',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_close_img' => array(
                    'type' => 'media',
                    'format' => 'image',
                    'title' => '关闭按钮',
                    'std' => 'https://oss.lcweb01.cn/joomla/20211221/7757b90e9ba9416adc8059287142a226.png',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_close_img_w' => array(
                    'type' => 'slider',
                    'title' => '关闭按钮的宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 26,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_close_img_h' => array(
                    'type' => 'slider',
                    'title' => '关闭按钮的高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 26,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_part03' => array(
                    'type' => 'separator',
                    'title' => '中间文字配置部分',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_title' => array(
                    'type' => 'text',
                    'title' => '中间文字内容',
                    'std' => '山西资海科技有限公司欢迎您 请问有什么可以帮助您？',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => '内容文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 24,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_title_color' => array(
                    'type' => 'color',
                    'title' => '内容文字颜色',
                    'std' => '#1b5778',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_title_weight' => array(
                    'type' => 'checkbox',
                    'title' => '内容文字加粗',
                    'std' => 0,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_part04' => array(
                    'type' => 'separator',
                    'title' => '按钮配置部分',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_top' => array(
                    'type' => 'slider',
                    'title' => '按钮组距内容距离',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 30,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_w' => array(
                    'type' => 'slider',
                    'title' => '按钮宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 140,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_h' => array(
                    'type' => 'slider',
                    'title' => '按钮高度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 42,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_fontsize' => array(
                    'type' => 'slider',
                    'title' => '按钮文字大小',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 16,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_radius' => array(
                    'type' => 'slider',
                    'title' => '按钮圆角',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 10,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn_m' => array(
                    'type' => 'slider',
                    'title' => '按钮间距',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 20,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn' => array(
                    'type' => 'buttons',
                    'title' => '按钮样式配置',
                    'std' => 'btn01',
                    'values' => array(
                        array(
                            'label' => '左侧按钮（只关闭窗口）',
                            'value' => 'btn01'
                        ),
                        array(
                            'label' => '右侧按钮（QQ咨询）',
                            'value' => 'btn02'
                        ),
                    ),
                    'tabs' => true,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                    ),
                ),
                'theme02_btn01_text' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'std' => '稍后再说',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn01'),
                    ),
                ),
                'theme02_btn01_color' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#1b5778',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn01'),
                    ),
                ),
                'theme02_btn01_bgColor' => array(
                    'type' => 'color',
                    'title' => '背景颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn01'),
                    ),
                ),
                'theme02_btn01_bColor' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'std' => '#1b5778',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn01'),
                    ),
                ),
                'theme02_btn01_bW' => array(
                    'type' => 'slider',
                    'title' => '边框宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 1,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn01'),
                    ),
                ),
                'theme02_btn02_text' => array(
                    'type' => 'text',
                    'title' => '按钮文字',
                    'std' => '立即咨询',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn02'),
                    ),
                ),
                'theme02_btn02_color' => array(
                    'type' => 'color',
                    'title' => '文字颜色',
                    'std' => '#fff',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn02'),
                    ),
                ),
                'theme02_btn02_bgColor' => array(
                    'type' => 'color',
                    'title' => '背景颜色',
                    'std' => '#1b5778',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn02'),
                    ),
                ),
                'theme02_btn02_bColor' => array(
                    'type' => 'color',
                    'title' => '边框颜色',
                    'std' => '#1b5778',
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn02'),
                    ),
                ),
                'theme02_btn02_bW' => array(
                    'type' => 'slider',
                    'title' => '边框宽度',
                    'max' => 1000,
                    'min' => 0,
                    'std' => 1,
                    'depends' => array(
                        array('consult_style', '=', 'theme02'),
                        array('theme02_btn', '=', 'btn02'),
                    ),
                ),
            ),
        ),
    )
);
