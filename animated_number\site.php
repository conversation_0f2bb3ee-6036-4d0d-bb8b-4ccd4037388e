<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonAnimated_number extends JwpagefactoryAddons
{

	public function render()
	{
		$settings = $this->addon->settings;

		$number = (isset($settings->number) && $settings->number) ? $settings->number : 0;
		$duration = (isset($settings->duration) && $settings->duration) ? $settings->duration : 0;
		$counter_title = (isset($settings->counter_title) && $settings->counter_title) ? $settings->counter_title : '';
		$alignment = (isset($settings->alignment) && $settings->alignment) ? $settings->alignment : '';
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$number_position = (isset($settings->number_position) && $settings->number_position) ? 'animated-number-position-' . $settings->number_position : '';
		$number_type = (isset($settings->number_type) && $settings->number_type) ? $settings->number_type : 'type1';
		$number_before_after_text_right = (isset($settings->number_before_after_text_right) && $settings->number_before_after_text_right) ? $settings->number_before_after_text_right : '';

		if($number_type=='type1')
		{
		    $output = '<div class="jwpf-addon jwpf-addon-animated-number ' . $alignment . ' ' . $class . ' ' . $number_position . '">';
    		$output .= '<div class="jwpf-addon-content">';
    		$output .= '<div class="jwpf-animated-number" data-digit="' . $number . '" data-duration="' . $duration . '">0</div>';
    		if ($counter_title) {
    			$output .= '<div class="jwpf-animated-number-title">' . $counter_title . '</div>';
    		}
    		$output .= '</div>';
    		$output .= '</div>';
		}
	    elseif($number_type=='type2')
	    {
	        $number_before_after_text = (isset($settings->number_before_after_text) && $settings->number_before_after_text) ? $settings->number_before_after_text : '';
		    $number_before_after_text_position = (isset($settings->number_before_after_text_position) && $settings->number_before_after_text_position) ? $settings->number_before_after_text_position : '';
	        $output = '<div class="jwpf-addon jwpf-addon-animated-number ' . $alignment . ' ' . $class . ' ' . $number_position . '">';
    		$output .= '<div class="jwpf-addon-content">';
    		$output .= '<div class="jwpf-animated-number" data-digit="' . $number . '" data-duration="' . $duration . '">0</div>';
    		$output .= '</div>';
    		$output .='<br><span>'. $counter_title .'</span>';
    		$output .= '</div>';
	    }
		else
	    {
	        $number_before_after_text = (isset($settings->number_before_after_text) && $settings->number_before_after_text) ? $settings->number_before_after_text : '';
		    $number_before_after_text_position = (isset($settings->number_before_after_text_position) && $settings->number_before_after_text_position) ? $settings->number_before_after_text_position : '';

			$type3_div_width = (isset($settings->type3_div_width) && $settings->type3_div_width) ? $settings->type3_div_width : 100;
			$type3_image = (isset($settings->type3_image) && $settings->type3_image) ? $settings->type3_image : '';
			$type3_title_left = (isset($settings->type3_title_left) && $settings->type3_title_left) ? $settings->type3_title_left : 0;
			$type3_title_height = (isset($settings->type3_title_height) && $settings->type3_title_height) ? $settings->type3_title_height : 30;
			$type3_title_bgcolor = (isset($settings->type3_title_bgcolor) && $settings->type3_title_bgcolor) ? $settings->type3_title_bgcolor : '#b71b28';
			$type3_title_bgcolor_jb = (isset($settings->type3_title_bgcolor_jb) && $settings->type3_title_bgcolor_jb) ? $settings->type3_title_bgcolor_jb : '#ce3d4a';
			$type3_img_width = (isset($settings->type3_img_width) && $settings->type3_img_width) ? $settings->type3_img_width : 30;
			$type3_number_height = (isset($settings->type3_number_height) && $settings->type3_number_height) ? $settings->type3_number_height : 50;
			$type3_number_bgcolor = (isset($settings->type3_number_bgcolor) && $settings->type3_number_bgcolor) ? $settings->type3_number_bgcolor : '#ffa500';
			$type3_number_left = (isset($settings->type3_number_left) && $settings->type3_number_left) ? $settings->type3_number_left : 0;
			$type3_title_font_color = (isset($settings->type3_title_font_color) && $settings->type3_title_font_color) ? $settings->type3_title_font_color : '#fff';
			$type3_number_font_color = (isset($settings->type3_number_font_color) && $settings->type3_number_font_color) ? $settings->type3_number_font_color : '#fff';
			$type3_image_top = (isset($settings->type3_image_top) && $settings->type3_image_top) ? $settings->type3_image_top : 40;
			$type3_image_right = (isset($settings->type3_image_right) && $settings->type3_image_right) ? $settings->type3_image_right : 20;
			$type3_title_font = (isset($settings->type3_title_font) && $settings->type3_title_font) ? $settings->type3_title_font : '标题';

	        $output = '<div class="jwpf-addon jwpf-addon-animated-number ' . $alignment . ' ' . $class . ' ' . $number_position . '">';
    		$output .= '<div class="jwpf-addon-content" style="width:'.$type3_div_width.'%;">';
			$output .= '<div style="width:100%;text-align: left;    background: linear-gradient(to right, '.$type3_title_bgcolor.', '.$type3_title_bgcolor_jb.');color: '.$type3_title_font_color.';float:left;height: '.$type3_title_height.'px;line-height: '.$type3_title_height.'px;padding-left:'.$type3_title_left.'px;">'.$type3_title_font.'</div>';
			if($type3_image)
			{
				$output .= '<div style="position: absolute;right: '.$type3_image_right.'%;top: '.$type3_image_top.'%;z-index: 2;"><img style="width:'.$type3_img_width.'px;" src="'.$type3_image.'" /></div>';
			}
    		$output .= '<div style="color:'.$type3_number_font_color.';padding-left:'.$type3_number_left.'px;line-height: '.$type3_number_height.'px;background: '.$type3_number_bgcolor.';text-align: left;height: '.$type3_number_height.'px;" class="jwpf-animated-number" data-digit="' . $number . '" data-duration="' . $duration . '">0<span>+</span></div>';
    		$output .= '</div>';
    		$output .='<br><span>'. $counter_title .'</span>';
    		$output .= '</div>';
	    }

		return $output;
	}

	public function css()
	{
		$addon_id = '#jwpf-addon-' . $this->addon->id;
		$settings = $this->addon->settings;
		$number_type = (isset($settings->number_type) && $settings->number_type) ? $settings->number_type : 'type1';
		$font_size = (isset($settings->font_size) && $settings->font_size) ? $settings->font_size : '';
		//Number Style
		$number_style = '';
		$number_style .= (isset($settings->color) && $settings->color) ? 'color:' . $settings->color . ';' : '';
		$number_style .= (isset($settings->font_size) && $settings->font_size) ? 'font-size:' . (int)$settings->font_size . 'px;' : '';
		if($number_type=='type1')
		{
			$number_style .= (isset($settings->line_height) && $settings->line_height) ? 'line-height:' . (int)$settings->line_height . 'px;' : '';
		}
		else
		{
			$number_style .=  'line-height:none;';
		}
		$number_style .= (isset($settings->number_font_wight) && $settings->number_font_wight) ? 'font-weight:' . (int)$settings->number_font_wight . ';' : '';
		//Number Tablet Style
		$number_style_sm = '';
		$number_style_sm .= (isset($settings->font_size_sm) && $settings->font_size_sm) ? 'font-size:' . (int)$settings->font_size_sm . 'px;' : '';
		if($number_type=='type1')
		{
			$number_style_sm .= (isset($settings->line_height_sm) && $settings->line_height_sm) ? 'line-height:' . (int)$settings->line_height_sm . 'px;' : '';
		}
		else
		{
			$number_style_sm .=  'line-height:none;';
		}

		//Number Mobile Style
		$number_style_xs = '';
		$number_style_xs .= (isset($settings->font_size_xs) && $settings->font_size_xs) ? 'font-size:' . (int)$settings->font_size_xs . 'px;' : '';
		if($number_type=='type1')
		{
			$number_style_xs .= (isset($settings->line_height) && $settings->line_height) ? 'line-height:' . (int)$settings->line_height . 'px;' : '';
		}
		else
		{
			$number_style_xs .=  'line-height:none;';
		}

		//Text Style
		$text_style = '';
		$text_style_sm = '';
		$text_style_xs = '';

		$text_style .= (isset($settings->title_font_size) && $settings->title_font_size) ? 'font-size:' . (int)$settings->title_font_size . 'px;' : '';
		$text_style .= (isset($settings->title_line_height) && $settings->title_line_height) ? 'line-height:' . (int)$settings->title_line_height . 'px;' : '';
		$text_style .= (isset($settings->title_color) && $settings->title_color) ? 'color:' . $settings->title_color . ';' : '';
		$text_style .= (isset($settings->title_margin) && trim($settings->title_margin)) ? 'margin:' . $settings->title_margin . ';' : '';
		//Title Font Style
		$title_fontstyle = (isset($settings->title_fontstyle) && $settings->title_fontstyle) ? $settings->title_fontstyle : '';
		if (isset($title_fontstyle->underline) && $title_fontstyle->underline) {
			$text_style .= 'text-decoration:underline;';
		}
		if (isset($title_fontstyle->italic) && $title_fontstyle->italic) {
			$text_style .= 'font-style:italic;';
		}
		if (isset($title_fontstyle->uppercase) && $title_fontstyle->uppercase) {
			$text_style .= 'text-transform:uppercase;';
		}
		if (isset($title_fontstyle->weight) && $title_fontstyle->weight) {
			$text_style .= 'font-weight:' . $title_fontstyle->weight . ';';
		}

		//Title tablet style
		$text_style_sm .= (isset($settings->title_font_size_sm) && $settings->title_font_size_sm) ? 'font-size:' . (int)$settings->title_font_size_sm . 'px;' : '';
		$text_style_sm .= (isset($settings->title_line_height_sm) && $settings->title_line_height_sm) ? 'line-height:' . (int)$settings->title_line_height_sm . 'px;' : '';
		$text_style_sm .= (isset($settings->title_margin_sm) && $settings->title_margin_sm) ? 'margin:' . $settings->title_margin_sm . ';' : '';
		//Title mobile style
		$text_style_xs .= (isset($settings->title_font_size_xs) && $settings->title_font_size_xs) ? 'font-size:' . (int)$settings->title_font_size_xs . 'px;' : '';
		$text_style_xs .= (isset($settings->title_line_height_xs) && $settings->title_line_height_xs) ? 'line-height:' . (int)$settings->title_line_height_xs . 'px;' : '';
		$text_style_xs .= (isset($settings->title_margin_xs) && $settings->title_margin_xs) ? 'margin:' . $settings->title_margin_xs . ';' : '';

		$number_before_after_text = (isset($settings->number_before_after_text) && $settings->number_before_after_text) ? $settings->number_before_after_text : '';
		$number_before_after_text_right = (isset($settings->number_before_after_text_right) && $settings->number_before_after_text_right) ? $settings->number_before_after_text_right : '';
		$number_before_after_text_position = (isset($settings->number_before_after_text_position) && $settings->number_before_after_text_position) ? $settings->number_before_after_text_position : '';

		if (isset($settings->before_font_size) && $settings->before_font_size) {
			if (is_object($settings->before_font_size)) {
					$before_font_size = $settings->before_font_size->md;
					$before_font_size_sm = $settings->before_font_size->sm;
					$before_font_size_xs = $settings->before_font_size->xs;
			} else {
					$before_font_size = $settings->before_font_size;
					$before_font_size_sm = $settings->before_font_size_sm;
					$before_font_size_xs = $settings->before_font_size_xs;
			}
		} else {
				$before_font_size = 1;
				$before_font_size_sm = 1;
				$before_font_size_xs = 1;
		}

		if (isset($settings->after_font_size) && $settings->after_font_size) {
			if (is_object($settings->after_font_size)) {
					$after_font_size = $settings->after_font_size->md;
					$after_font_size_sm = $settings->after_font_size->sm;
					$after_font_size_xs = $settings->after_font_size->xs;
			} else {
					$after_font_size = $settings->after_font_size;
					$after_font_size_sm = $settings->after_font_size_sm;
					$after_font_size_xs = $settings->after_font_size_xs;
			}
		} else {
				$after_font_size = 1;
				$after_font_size_sm = 1;
				$after_font_size_xs = 1;
		}

		$number_position = (isset($settings->number_position) && $settings->number_position) ? $settings->number_position : 'top';
		//Css output start
		$css = '';

		if($number_type=='type1')
		{
		    if ($number_before_after_text_position == 'right') {
    			if ($number_before_after_text)
    			{
    				$css .= $addon_id . ' .jwpf-animated-number::after {';
    				$css .= 'content:"' . $number_before_after_text . '";';
    				$css .= 'display: inline-block;';
    				$css .= '}';
    			}
        	}
        	else
        	{
        		if ($number_before_after_text)
        		{
        			$css .= $addon_id . ' .jwpf-animated-number::before {';
        			$css .= 'content:"' . $number_before_after_text . '";';
        			$css .= 'display: inline-block;';
        			$css .= '}';
        		}
    		}
		}
		elseif($number_type=='type2')
		{
		    if ($number_before_after_text)
			{
				$css .= $addon_id . ' .jwpf-animated-number::after{
					content:"' . $number_before_after_text . '";
					font-size:'.$after_font_size.'px;
					right:0;

				}';
				$css .= $addon_id . ' .jwpf-animated-number::before{
					content:"' . $number_before_after_text_right . '";
					position:absolute;
					font-size:'.$before_font_size.'px;
					top:0;
					right:0;
					margin-right:auto;
				}';
				$css .= '
					@media (min-width: 768px) and (max-width: 991px) {
						#jwpf-addon-'.$addon_id.' .jwpf-animated-number::after{
							font-size:'.$after_font_size_sm.'px;
						}
						#jwpf-addon-'.$addon_id.' .jwpf-animated-number::before{
							font-size:'.$before_font_size_sm.'px;
						}
					}
					@media (max-width: 767px) {
						#jwpf-addon-'.$addon_id.' .jwpf-animated-number::after{
							font-size:'.$after_font_size_xs.'px;
						}
						#jwpf-addon-'.$addon_id.' .jwpf-animated-number::before{
							font-size:'.$before_font_size_xs.'px;
						}
					}
				';
			}
		}
		elseif($number_type=='type3')
		{
		    if ($number_before_after_text)
			{
				$css .= $addon_id . ' .jwpf-animated-number::after{
					content:"' . $number_before_after_text . '";
					font-size:'.$after_font_size.'px;
					right:0;

				}';
			}
		}
		else
		{
			if ($number_before_after_text)
			{
				$css .= $addon_id . ' .jwpf-animated-number::after{
					content:"' . $number_before_after_text . '";
					font-size:'.$font_size.'px;
					right:0;
					margin-left: 5px;
				}';
			}
		}
		if ($number_style) {
			$css .= $addon_id . ' .jwpf-animated-number {';
			$css .= $number_style;
			$css .= 'float:left;position:relative;';
			$css .= 'min-width: -webkit-fill-available;';
			$css .= 'min-width: -mos-fill-available;';
			$css .= 'min-width: fill-available;';
			if($number_position == 'top') {
				$css .= 'float:none;';
			}
			$css .= '}';
		}
		if ($text_style) {
			$css .= $addon_id . ' .jwpf-animated-number-title {';
			$css .= $text_style;
			$css .= '}';
		}

		$css .= '@media (min-width: 768px) and (max-width: 991px) {';
		if ($number_style_sm) {
			$css .= $addon_id . ' .jwpf-animated-number {';
			$css .= $number_style_sm;
			$css .= '}';
		}

		if ($text_style_sm) {
			$css .= $addon_id . ' .jwpf-animated-number-title {';
			$css .= $text_style_sm;
			$css .= '}';
		}
		$css .= '}';

		$css .= '@media (max-width: 767px) {';
		if ($number_style_xs) {
			$css .= $addon_id . ' .jwpf-animated-number {';
			$css .= $number_style_xs;
			$css .= '}';
		}

		if ($text_style_xs) {
			$css .= $addon_id . ' .jwpf-animated-number-title {';
			$css .= $text_style_xs;
			$css .= '}';
		}
		if($number_type=='type1')
		{
		    if ($number_before_after_text_position == 'right') {
    			if ($number_before_after_text)
    			{
    				$css .= $addon_id . ' .jwpf-animated-number::after {';
    				$css .= 'content:"' . $number_before_after_text . '";';
    				$css .= 'display: inline-block;';
    				$css .= '}';
    			}
        	}
        	else
        	{
        		if ($number_before_after_text)
        		{
        			$css .= $addon_id . ' .jwpf-animated-number::before {';
        			$css .= 'content:"' . $number_before_after_text . '";';
        			$css .= 'display: inline-block;';
        			$css .= '}';
        		}
    		}
		}
		else
		{
    			$css .= $addon_id . ' .jwpf-animated-number::after{
    				content:"' . $number_before_after_text . '";
    				font-size:'.$after_font_size.'px;
    				right:0;

    			}';
    			$css .= $addon_id . ' .jwpf-animated-number::before{
    				content:"' . $number_before_after_text_right . '";
    				position:absolute;
    				font-size:'.$before_font_size.'px;
    				top:0;
    				right:0;
    				margin-right:auto;
    			}';
		}

		$css .= '}';

		return $css;
	}

	public static function getTemplate()
	{
		$output = '
		<#
			var addonId = "jwpf-addon-"+data.id;
			var number_position = (!_.isEmpty(data.number_position) && data.number_position) ? "animated-number-position-"+data.number_position : "";
		#>
		<style type="text/css">
			<# if(data.number_type == "type1") { #>
				<# if(data.number_before_after_text_position == "right") { #>
    				#{{ addonId }} .jwpf-animated-number::after{
    					content:"{{data.number_before_after_text}}";
    				}
    			<# } else { #>
    				#{{ addonId }} .jwpf-animated-number::before{
    					content:"{{data.number_before_after_text}}";
    				}
    			<# } #>
			<# } #>
			<# if(data.number_type == "type2") { #>
				<#
				 	let before_font_size=(_.isObject(data.before_font_size)&&data.before_font_size)?data.before_font_size.md:data.before_font_size
					let before_font_size_sm=(_.isObject(data.before_font_size)&&data.before_font_size)?data.before_font_size.sm:data.before_font_size;
					let before_font_size_xs=(_.isObject(data.before_font_size)&&data.before_font_size)?data.before_font_size.xs:data.before_font_size;

					let after_font_size=(_.isObject(data.after_font_size)&&data.after_font_size)?data.after_font_size.md:data.after_font_size;
					let after_font_size_sm=(_.isObject(data.after_font_size)&&data.after_font_size)?data.after_font_size.sm:data.after_font_size;
					let after_font_size_xs=(_.isObject(data.after_font_size)&&data.after_font_size)?data.after_font_size.xs:data.after_font_size;
				#>
    				#{{ addonId }} .jwpf-animated-number::after{
    					content:"{{data.number_before_after_text}}";
    					font-size:{{after_font_size}}px;
    					right:0;

    				}
    				#{{ addonId }} .jwpf-animated-number::before{
    				    content:"{{data.number_before_after_text_right}}";
    				    position:absolute;
    					font-size:{{before_font_size}}px;
    					top:0;
    					right:0;
    					margin-right:auto;
    				}
    				@media (min-width: 768px) and (max-width: 991px) {
						#{{ addonId }} .jwpf-animated-number::after{
							font-size:{{after_font_size_sm}}px;
						}
						#{{ addonId }} .jwpf-animated-number::before{
							font-size:{{before_font_size_sm}}px;
						}
					}
					@media (max-width: 767px) {
						#{{ addonId }} .jwpf-animated-number::after{
							font-size:{{after_font_size_xs}}px;
						}
						#{{ addonId }} .jwpf-animated-number::before{
							font-size:{{before_font_size_xs}}px;
						}
					}
			<# } #>
			<# if(data.number_type == "type1") { #>
				#{{ addonId }} .jwpf-animated-number{
					animation-iteration-count:3;
    				color: {{ data.color }};
    				position:relative;
    				font-weight: {{ data.number_font_wight }};
    				font-family: {{ data.number_font_family }};
    				<# if(_.isObject(data.font_size)){ #>
    					font-size: {{ data.font_size.md }}px;
    				<# } else { #>
    					font-size: {{ data.font_size }}px;
    				<# }
    				if(_.isObject(data.line_height)){ #>
    					line-height: {{ data.line_height.md }}px;
    				<# } else { #>
    					line-height: {{ data.line_height }}px;
    				<# } #>
    			}
			<# } #>
			<# if(data.number_type == "type2") { #>
				#{{ addonId }} .jwpf-animated-number{
    				color: {{ data.color }};
    				position:relative;
    				font-weight: {{ data.number_font_wight }};
    				font-family: {{ data.number_font_family }};
    				<# if(_.isObject(data.font_size)){ #>
    					font-size: {{ data.font_size.md }}px;
    				<# } else { #>
    					font-size: {{ data.font_size }}px;
    				<# }
    				if(_.isObject(data.line_height)){ #>
    					line-height: none;
    				<# } else { #>
    					line-height: none;
    				<# } #>
    			}
			<# } #>

			<# if(data.number_type == "type1") { #>
    				#{{ addonId }} .jwpf-animated-number-title{
    				color: {{ data.title_color }};
    				<# if(_.isObject(data.title_font_size)){ #>
    					font-size: {{ data.title_font_size.md }}px;
    				<# } else { #>
    					font-size: {{ data.title_font_size }}px;
    				<# }
    				if(_.isObject(data.title_line_height)){ #>
    					line-height: {{ data.title_line_height.md }}px;
    				<# } else { #>
    					line-height: {{ data.title_line_height }}px;
    				<# }
    				if(_.isObject(data.title_margin)){ #>
    					margin: {{ data.title_margin.md }};
    				<# }
    				if(_.isObject(data.title_fontstyle)){ #>
    					<# if(data.title_fontstyle.underline){ #>
    						text-decoration:underline;
    					<# }
    					if(data.title_fontstyle.italic){
    					#>
    						font-style:italic;
    					<# }
    					if(data.title_fontstyle.uppercase){
    					#>
    						text-transform:uppercase;
    					<# }
    					if(data.title_fontstyle.weight){
    					#>
    						font-weight:{{data.title_fontstyle.weight}};
    					<# }
    				} #>
    			}
			<# } #>
			<# if(data.number_type == "type2") { #>
    				#{{ addonId }} .jwpf-animated-number-title{
    				color: {{ data.title_color }};
    				<# if(_.isObject(data.title_font_size)){ #>
    					font-size: {{ data.title_font_size.md }}px;
    				<# } else { #>
    					font-size: {{ data.title_font_size }}px;
    				<# }
    				if(_.isObject(data.title_line_height)){ #>
    					ine-height: none;
    				<# } else { #>
    					line-height: none;
    				<# }
    				if(_.isObject(data.title_margin)){ #>
    					margin: {{ data.title_margin.md }};
    				<# }
    				if(_.isObject(data.title_fontstyle)){ #>
    					<# if(data.title_fontstyle.underline){ #>
    						text-decoration:underline;
    					<# }
    					if(data.title_fontstyle.italic){
    					#>
    						font-style:italic;
    					<# }
    					if(data.title_fontstyle.uppercase){
    					#>
    						text-transform:uppercase;
    					<# }
    					if(data.title_fontstyle.weight){
    					#>
    						font-weight:{{data.title_fontstyle.weight}};
    					<# }
    				} #>
    			}
			<# } #>



			<# if(data.number_type == "type1") { #>
					@media (min-width: 768px) and (max-width: 991px) {
        				#{{ addonId }} .jwpf-animated-number{
        					<# if(_.isObject(data.font_size)){ #>
        						font-size: {{ data.font_size.sm }}px;
        					<# }
        					if(_.isObject(data.line_height)){ #>
        						line-height: {{ data.line_height.sm }}px;
        					<# } #>
							animation-iteration-count:3;
        				}
        				#{{ addonId }} .jwpf-animated-number-title{
        					<# if(_.isObject(data.title_font_size)){ #>
        						font-size: {{ data.title_font_size.sm }}px;
        					<# }
        					if(_.isObject(data.title_line_height)){ #>
        						line-height: {{ data.title_line_height.sm }}px;
        					<# }
        					if(_.isObject(data.title_margin)){ #>
        						margin: {{ data.title_margin.sm }};
        					<# } #>
        				}
        			}
			<# } #>
			<# if(data.number_type == "type2") { #>
				@media (min-width: 768px) and (max-width: 991px) {
        				#{{ addonId }} .jwpf-animated-number{
        					<# if(_.isObject(data.font_size)){ #>
        						font-size: {{ data.font_size.sm }}px;
        					<# }
        					if(_.isObject(data.line_height)){ #>
        						line-height: none;
        					<# } #>
        				}
        				#{{ addonId }} .jwpf-animated-number-title{
        					<# if(_.isObject(data.title_font_size)){ #>
        						font-size: {{ data.title_font_size.sm }}px;
        					<# }
        					if(_.isObject(data.title_line_height)){ #>
        						line-height: none;
        					<# }
        					if(_.isObject(data.title_margin)){ #>
        						margin: {{ data.title_margin.sm }};
        					<# } #>
        				}
        			}
			<# } #>

			<# if(data.number_type == "type1") { #>
					@media (max-width: 767px) {
        				#{{ addonId }} .jwpf-animated-number{
							animation-iteration-count:3;
        					<# if(_.isObject(data.font_size)){ #>
        						font-size: {{ data.font_size.xs }}px;
        					<# }
        					if(_.isObject(data.line_height)){ #>
        						line-height: {{ data.line_height.xs }}px;
        					<# } #>
        				}
        				#{{ addonId }} .jwpf-animated-number-title{
        					<# if(_.isObject(data.title_font_size)){ #>
        						font-size: {{ data.title_font_size.xs }}px;
        					<# }
        					if(_.isObject(data.title_line_height)){ #>
        						line-height: {{ data.title_line_height.xs }}px;
        					<# }
        					if(_.isObject(data.title_margin)){ #>
        						margin: {{ data.title_margin.xs }};
        					<# } #>
        				}
        			}
			<# } #>

			<# if(data.number_type == "type2") { #>
				@media (max-width: 767px) {
        				#{{ addonId }} .jwpf-animated-number{
        					<# if(_.isObject(data.font_size)){ #>
        						font-size: {{ data.font_size.xs }}px;
        					<# }
        					if(_.isObject(data.line_height)){ #>
        				 		line-height: none;
        					<# } #>
        				}
        				#{{ addonId }} .jwpf-animated-number-title{
        					<# if(_.isObject(data.title_font_size)){ #>
        						font-size: {{ data.title_font_size.xs }}px;
        					<# }
        					if(_.isObject(data.title_line_height)){ #>
        						line-height: none;
        					<# }
        					if(_.isObject(data.title_margin)){ #>
        						margin: {{ data.title_margin.xs }};
        					<# } #>
        				}
        			}
			<# } #>



		</style>
		<# if(data.number_type == "type1") { #>
    			<div class="jwpf-addon jwpf-addon-animated-number {{ data.alignment }} {{ data.class }} {{number_position}}">
        			<div class="jwpf-addon-content">
        				<div class="jwpf-animated-number jw-inline-editable-element" data-id={{data.id}} data-fieldName="number" contenteditable="true" data-digit="{{ data.number }}" data-duration="{{ data.duration }}">0</div>
        				<# if(data.counter_title){ #>
        					<div class="jwpf-animated-number-title jw-inline-editable-element" data-id={{data.id}} data-fieldName="counter_title" contenteditable="true">{{ data.counter_title }}</div>
        				<# } #>
        			</div>
        		</div>
    	<# } else { #>
                <div class="jwpf-addon jwpf-addon-animated-number {{ data.alignment }} {{ data.class }} {{number_position}}">
        			<div class="jwpf-addon-content">
        				<div class="jwpf-animated-number jw-inline-editable-element" data-id={{data.id}} data-fieldName="number" contenteditable="true" data-digit="{{ data.number }}" data-duration="{{ data.duration }}">0</div>
        			</div>
        			<br>
        			<span>{{ data.counter_title }}</span>
        		</div>
    	<# } #>
		';

		return $output;
	}
}
