<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-07 15:23:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonTel_window extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $settings = $this->addon->settings;
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $tel_text = (isset($settings->tel_text)) ? $settings->tel_text : '***********';//电话号码
        $tel_img = (isset($settings->tel_img)) ? $settings->tel_img : 'type1';//悬浮电话图片
        $button_width = (isset($settings->button_width)) ? $settings->button_width : 'type1';//按钮宽度
        $button_height = (isset($settings->button_height)) ? $settings->button_height : 'type1';//按钮高度
        $button_top = (isset($settings->button_top)) ? $settings->button_top : 32;//按钮上下位置
        $button_top =$button_top-6 ;//按钮上下位置
        $button_left = (isset($settings->button_left)) ? $settings->button_left : 100;//按钮左右位置
        $button_left = $button_left-5;//按钮左右位置
        $addon_style = (isset($settings->addon_style)) ? $settings->addon_style : 'scroller';//浮窗功能
        $tz_style = (isset($settings->tz_style)) ? $settings->tz_style : 'tzbsx';//返回方式


        $output = '';
        if($addon_style=='scroller')
        {
            $output .= '
            <div class="solid" style="position: fixed;top:'.$button_top.'%; left:'.$button_left.'%;">
                    <a href="tel:'.$tel_text.'"><img style="width: '.$button_width.'px;height: '.$button_height.'px" src="'.$tel_img.'" alt=""></a>
                </div>
            ';
        }
        elseif($addon_style=='ticker')
        {
            
            if($tz_style=='tzbsx')
            {
                $output .= '
                <div class="solid" style="position: fixed;top:'.$button_top.'%; left:'.$button_left.'%;z-index:100;">
                        <a href="javascript:;" onclick="javascript:history.back(-1)"><img style="width: '.$button_width.'px;height: '.$button_height.'px" src="'.$tel_img.'" alt=""></a>
                    </div>
                
                ';
            }
            elseif($tz_style=='tzsx')
            {
                $output .= '
                <div class="solid" style="position: fixed;top:'.$button_top.'%; left:'.$button_left.'%;z-index:100;">
                        <a href="javascript:;" onclick="javascript:window.location.replace(document.referrer);"><img style="width: '.$button_width.'px;height: '.$button_height.'px" src="'.$tel_img.'" alt=""></a>
                    </div>
                
                ';
            }
        }
        

        return $output;
    }

    public function css()
    {

    }

    //用于设计器中显示
    public static function getTemplate()
    {

        $output = '';
        $output .= '
<#
                        var addonId = "jwpf-addon-"+data.id;
                        var top = (data.button_top)-6;
                        var left = (data.button_left)-3;
                        var tel_img = data.tel_img;
                        var tel_text = data.tel_text;
                        var button_width = data.button_width;
                        var button_height = data.button_height;
#>';
        $output .= '<div   style="height:{{data.input_height}}px ">';
        $output .= '<span>本段文字用于编辑模式下占位，预览模式下不显示</span>';
        $output .= '
		    <div class="solid" style="position: fixed;top:{{top}}%; left:{{left}}%;z-index:100;">
                <a href="tel:{{tel_text}}"><img style="width:{{button_width}}px;height:{{button_height}}px" src=\'{{data.tel_img}}\' alt=""></a>
            </div>
		';

        $output .= "</div>";
        return $output;
    }
}