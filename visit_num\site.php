<?php
/*
 * @Author: your name
 * @Date: 2021-03-31 11:14:36
 * @LastEditTime: 2021-04-10 14:00:11
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\business_bridge\site.php
 */
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON><PERSON><PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonVisit_num extends JwpagefactoryAddons
{
    public function render()
    {
        $output = '';
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $layout_id = $_GET['layout_id'] ?? 0;
        $settings = $this->addon->settings;
        $kaiqi_xianshi = (isset($settings->kaiqi_xianshi)) ? $settings->kaiqi_xianshi : 'k';
        $num_title = (isset($settings->num_title)) ? $settings->num_title : '访问量：';
        $num_title_color = (isset($settings->num_title_color)) ? $settings->num_title_color : '#000000';
        $num_color = (isset($settings->num_color)) ? $settings->num_color : '#ff0000';
        $num_title_bgcolor = (isset($settings->num_title_bgcolor)) ? $settings->num_title_bgcolor : '#ffffff';
        $num_title_font_size = (isset($settings->num_title_font_size)) ? $settings->num_title_font_size : 18;
        $num_font_size = (isset($settings->num_font_size)) ? $settings->num_font_size : 18;
        $num_title_bj = (isset($settings->num_title_bj)) ? $settings->num_title_bj : 0;
        $num_title_zbj = (isset($settings->num_title_zbj)) ? $settings->num_title_zbj : 0;
        $num_title_bg_width = (isset($settings->num_title_bg_width)) ? $settings->num_title_bg_width : 100;
        $num_title_bg_border = (isset($settings->num_title_bg_border)) ? $settings->num_title_bg_border : 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        if($kaiqi_xianshi=='k')
        {
            $output .="<div class='div-nav' style='border-radius:".$num_title_bg_border."px;width: ".$num_title_bg_width."px;background:".$num_title_bgcolor."'><span style='margin-left:".$num_title_zbj."px;color:".$num_title_color.";font-size:".$num_title_font_size."px'>".$num_title."</span><span id='num'  style='color:".$num_color.";font-size:".$num_font_size."px;margin-left:".$num_title_bj."px;'></span></div>";
        }
        $output .= "<script>
                        $(document).ready(function () {
                            $.ajax({
                                url: 'https://zhjzt.china9.cn/api/Site/addFwnum',
                                type: 'post',
                                async: false,
                                dataType: 'json',
                                data: {site_id:".$site_id."},
                                success: function (data) {
                                    console.log(data)
                                    ";
                                if($kaiqi_xianshi=='k')
                                {
                                    $output .="$('".$addon_id." #num').text(data.data);";
                                }
                                    
        $output .= "
                                }
                            })
                        })
                </script>";

        return $output;
    }

    public function css()
    {
    }

    //用于设计器中显示
    public static function getTemplate()
    {
        $output ="<# if(data.kaiqi_xianshi=='k'){ #>
                        <div class='div-nav' style='border-radius:{{data.num_title_bg_border}}px;width: {{data.num_title_bg_width}}px;background:{{data.num_title_bgcolor}}'><span style='margin-left:{{data.num_title_zbj}}px;color:{{data.num_title_color}};font-size:{{data.num_title_font_size}}px'>{{data.num_title}}</span><span id='num'  style='color:{{data.num_color}};font-size:{{data.num_font_size}}px;margin-left:{{data.num_title_bj}}px;'>0</span></div>
                    <# } else { #>
                        <div>此为【访问量】插件占位，只有在已发布站点显示</div>
                    <# } #>";
        return $output;
    }
}