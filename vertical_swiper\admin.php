<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted aceess');

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'vertical_swiper',
        'title' => JText::_('垂直轮播'),
        'desc' => JText::_('垂直轮播'),
        'category' => '轮播',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'carousel_options' => array(
                    'type' => 'buttons',
                    'title' => JText::_('轮播'),
                    'std' => 'elements',
                    'values' => array(
                        array(
                            'label' => '轮播元素',
                            'value' => 'elements'
                        ),
                        array(
                            'label' => '轮播元素样式',
                            'value' => 'item_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'jw_image_carousel_item' => array(
                    'title' => JText::_('轮播项'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => array(
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20210630/c7b11373bac85f6aeda5f0f70c577054.png',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20210630/c7b11373bac85f6aeda5f0f70c577054.png',
                        ),
                        array(
                            'image_carousel_img' => 'https://oss.lcweb01.cn/joomla/20210630/c7b11373bac85f6aeda5f0f70c577054.png',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '轮播项标签名',
                            'desc' => '轮播项标签名',
                            'std' => 'Carousel Item Tittle',
                        ),
                        'image_carousel_img' => array(
                            'type' => 'media',
                            'title' => '轮播项图片',
                            'desc' => '轮播项图片',
                            'std' => 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20220514/9cd357958d96ef30f000f008ab272bee.jpeg',
                        ),
                        'is_link' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('是否为该项添加链接'),
                            'desc' => JText::_('是否为该项添加链接'),
                            'std' => '0',
                        ),
                        'image_carousel_img_link' => array(
                            'type' => 'media',
                            'title' => '图片链接',
                            'desc' => '图片链接',
                            'placeholder' => 'http://',
                            'hide_preview' => true,
                            'std' => '',
                            'depends' => array(
                                array('is_link', '=', 1)
                            )
                        ),
                        'link_open_new_window' => array(
                            'type' => 'checkbox',
                            'title' => JText::_('在新标签页中打开'),
                            'std' => 0,
                            'depends' => array(array('image_carousel_img_link', '!=', '')),
                        ),

//                        字还是图片
                        'item_content_type' => array(
                            'type' => 'buttons',
                            'title' => JText::_('内容类别'),
                            'desc' => JText::_('内容类别'),
                            'std' => 'font',
                            'values' => array(
                                array(
                                    'label' => '文字',
                                    'value' => 'font'
                                ),
                                array(
                                    'label' => '图片',
                                    'value' => 'img'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'item_title' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项标题'),
                            'desc' => JText::_('轮播项标题'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'item_subtitle' => array(
                            'type' => 'text',
                            'title' => JText::_('轮播项副标题'),
                            'desc' => JText::_('轮播项副标题'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'item_description' => array(
                            'type' => 'textarea',
                            'title' => JText::_('轮播项描述'),
                            'desc' => JText::_('轮播项描述'),
                            'depends' => array(
                                array('item_content_type', '=', 'font')
                            )
                        ),
                        'carousel_item_img' => array(
                            'type' => 'media',
                            'title' => '轮播图中间图片链接',
                            'desc' => '轮播图中间图片链接',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            )
                        ),
                        'carousel_item_img_width' => array(
                            'type' => 'slider',
                            'title' => '轮播图中间图片宽度所占比',
                            'desc' => '轮播图中间图片宽度所占比',
                            'std' => '',
                            'depends' => array(
                                array('item_content_type', '=', 'img')
                            ),
                            'max' => 100
                        ),
                    ),
                ),
                'carousel_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('轮播区域高度'),
                    'desc' => JText::_('轮播区域高度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'min' => 0,
                    'max' => 2000,
                    'std' => array(
                        'md' => 924,
                        'sm' => 600,
                        'xs' => 300
                    ),
                    'responsive' => true,
                ),
                'carousel_autoplay' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否自动轮播'),
                    'desc' => JText::_('是否自动轮播'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 1
                ),
                'carousel_speed' => array(
                    'type' => 'number',
                    'title' => JText::_('切换速度'),
                    'desc' => JText::_('切换速度'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 2500
                ),
                'carousel_interval' => array(
                    'type' => 'number',
                    'title' => JText::_('自动切换的时间间隔'),
                    'desc' => JText::_('自动切换的时间间隔'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                    'std' => 4500
                ),

                'item_content_verti_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容垂直对齐'),
                    'desc' => JText::_('内容垂直对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'values' => array(
                        'top' => JText::_('顶部对齐'),
                        'middle' => JText::_('中间对齐'),
                        'bottom' => JText::_('底部对齐'),
                    ),
                    'std' => 'middle',
                ),
                'item_content_hori_align' => array(
                    'type' => 'select',
                    'title' => JText::_('内容水平对齐'),
                    'desc' => JText::_('内容水平对齐'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'values' => array(
                        'left' => JText::_('左对齐'),
                        'center' => JText::_('居中'),
                        'right' => JText::_('右对齐'),
                    ),
                    'std' => 'center',
                ),
                'content_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('内容布局'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std' => 'title_style',
                    'values' => array(
                        array(
                            'label' => '标题',
                            'value' => 'title_style'
                        ),
                        array(
                            'label' => '副标题',
                            'value' => 'subtitle_style'
                        ),
                        array(
                            'label' => '描述',
                            'value' => 'desc_style'
                        ),
                        array(
                            'label' => '图片',
                            'value' => 'img_style'
                        ),
                    ),
                    'tabs' => true,
                ),
                'content' => array(
                    'type' => 'separator',
                    'title' => JText::_('轮播项内容'),
                    'depends' => array(
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                //Title style
                'content_title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题字体大小'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive' => true,
                    'max' => 100,
                ),
                'content_title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题行高'),
                    'std' => '60',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 100,
                ),
                'content_title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('字体'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-heading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'red',
                ),
                'content_title_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10',
                    'depends' => array(
                        array('content_style', '=', 'title_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                    'responsive' => true
                ),

                //Subtitle style
                'content_subtitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'content_subtitle_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                ),
                'content_subtitle_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-subheading { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),
                'content_subtitle_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'content_subtitle_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'content_subtitle_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'subtitle_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>'red'
                ),

                //Description style
                'description_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_SIZE'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'responsive' => true,
                    'max' => 400,
                ),
                'description_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                ),
                'description_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-carousel-extended-description { font-family: "{{ VALUE }}"; }'
                    ),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'description_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_FONT_STYLE'),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'description_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '-10px' => '-10px',
                        '-9px' => '-9px',
                        '-8px' => '-8px',
                        '-7px' => '-7px',
                        '-6px' => '-6px',
                        '-5px' => '-5px',
                        '-4px' => '-4px',
                        '-3px' => '-3px',
                        '-2px' => '-2px',
                        '-1px' => '-1px',
                        '0px' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0px',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                ),

                'description_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_COLOR_DESC'),
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'std'=>''
                ),
                'description_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'item_style'),
                    ),
                    'max' => 400,
                    'responsive' => true
                ),
                'content_img_margin' => array(
                    'type' => 'margin',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_GLOBAL_MARGIN_DESC'),
                    'placeholder' => '10',
                    'depends' => array(
                        array('content_style', '=', 'desc_style'),
                        array('carousel_options', '=', 'img_style'),
                    ),
                    'max' => 400,
                    'responsive' => true
                ),

                'controller_settings' => array(
                    'type' => 'separator',
                    'title' => JText::_('控制器设置'),
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'carousel_bullet' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('是否显示分页器'),
                    'desc' => JText::_('是否显示分页器'),
                    'std' => 1,
                    'depends' => array(
                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'bullet_style' => array(
                    'type' => 'buttons',
                    'title' => JText::_('分页器样式'),
                    'std' => 'normal_bullet',
                    'values' => array(
                        array(
                            'label' => '正常状态',
                            'value' => 'normal_bullet'
                        ),
                        array(
                            'label' => '选中状态',
                            'value' => 'active_bullet'
                        )
                    ),
                    'depends' => array(

                        array('carousel_bullet', '=', 1),

                        array('carousel_options', '=', 'elements'),
                    ),
                ),
                'bullet_margin'=> array(
                    'type' => 'slider',
                    'title' => JText::_('分页器每项之间的距离'),
                    'max' => 100,
                    'min' => 15,
                    'depends' => array(

                        array('carousel_bullet', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 15,
                ),
                'pagination_right'=> array(
                    'type' => 'slider',
                    'title' => JText::_('分页器右边距'),
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(

                        array('carousel_bullet', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 39,
                ),
                'bullet_opacity'=> array(
                    'type' => 'slider',
                    'title' => JText::_('分页器不透明度'),
                    'max' => 100,
                    'min' => 0,
                    'depends' => array(

                        array('carousel_bullet', '=', 1),

                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 8,
                ),
                'bullet_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器高度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 3,
                ),
                'bullet_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器宽度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    ),
                    'std' => 18,
                ),
                'bullet_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    )
                ),
                'bullet_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'std' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    )
                ),
                'bullet_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'std' => '',
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    )
                ),
                'bullet_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('圆角'),
                    'desc' => JText::_('圆角'),
                    'max' => 25,
                    'std' => '',
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'normal_bullet'),
                    )
                ),
                //Bullet hover
                'bullet_active_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器高度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    ),
                    'std' => 3,
                ),
                'bullet_active_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('分页器宽度'),
                    'max' => 50,
                    'min' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    ),
                    'std' => 28,
                ),
                'bullet_active_background' => array(
                    'type' => 'color',
                    'title' => JText::_('背景色'),
                    'std' => '#fff',
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),
                'bullet_active_border_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('边框宽度'),
                    'max' => 20,
                    'std' => 0,
                    'depends' => array(


                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),
                'bullet_active_border_color' => array(
                    'type' => 'color',
                    'title' => JText::_('边框颜色'),
                    'std' => '',
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),
                'bullet_active_border_radius' => array(
                    'type' => 'slider',
                    'title' => JText::_('圆角'),
                    'desc' => JText::_('圆角'),
                    'max' => 20,
                    'std' => 0,
                    'depends' => array(
                        array('carousel_bullet', '=', 1),
                        array('carousel_options', '=', 'elements'),
                        array('bullet_style', '=', 'active_bullet'),
                    )
                ),

            ),
        )
    )
);
