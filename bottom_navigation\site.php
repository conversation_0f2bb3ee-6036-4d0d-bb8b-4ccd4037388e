<?php

/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct access
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonBottom_navigation extends JwpagefactoryAddons
{

  public function getList()
  {
    $app = JFactory::getApplication();
    $input = $app->input;

    // 获取地址栏参数layout
    $layout = $_GET['layout'];

    if ($layout == 'edit') {
      return $this->getNavData();
    }

    $nav_list = JwPageFactoryBase::getNavigationList($input->get('layout_id', 0));
    if (count((array)$nav_list)) {
      foreach ($nav_list as $key => $link) {

        $link->all_link = $this->getNavUrl($link);
        $link->a_target = $this->toTarget($link->target);

        $gos = explode(',', $link->goods_type);
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $compid = $_GET['company_id'] ?? 0;
        $sitid = $_GET['site_id'] ?? 0;
        $layid = $_GET['layout_id'] ?? 0;
        $type1 = []; // 这个是获取当前分类的信息
        if (count($gos)) {
          foreach ($gos as $keyd => $valued) {
            $data = JwpagefactoryHelperCategories::GetCategorieidsssss('com_goods', $sitid, '', $layid, $compid, '', $valued);
            // 给data追加参数
            $data->all_link = $this->getNavUrl($link, $valued);
            $data->a_target = $this->toTarget($v->target);
            // 给data追加参数
            $type1[] = $data;
          }
        } else {
          foreach ($link->data as $k => $v) {
            $v->all_link = $this->getNavUrl($v);
            $v->a_target = $this->toTarget($v->target);
            $type1[] = $v;
          }
        }
        $link->children = $type1;
      }
    } else {
      $nav_list = [];
    }

    return $nav_list;
  }

  // 返回跳转方式
  public function toTarget($target = '')
  {
    return !empty($target) ? 'target="' . $target . '"' . ($target === '_blank' ? ' rel="noopener noreferrer"' : '') : '';
  }

  // 返回链接
  public function getNavUrl($link, $cate_id = 0)
  {
    $url = (isset($link->url) ? $link->url : '');
    $nav_info = '';
    if ($link->goods_type) {
      $nav_info = '&from=goods' . '&nav_pid=' . $link->id;
      if ($cate_id) {
        $nav_info .= '&zcpcatid=' . $cate_id . '&cpcatid=' . $cate_id . '&goods_catid=' . $cate_id;
      }
    }
    if ($url) {
      if ($link->type == 1) {
        $url .= ((strpos($url, '?') !== false) ? '&' : '?') . 'company_id=' . ($_GET['company_id'] ?? 0) . '&site_id=' . ($_GET['site_id'] ?? 0) . '&layout_id=' . ($_GET['layout_id'] ?? 0);
        if ($link->qkzjid) {
          $url .= '#' . $link->qkzjid;
        }
      }
    }
    return $url . $nav_info;
  }

  // 静态导航数据
  public function getNavData()
  {
    $navData = [
      (object)[
        'title' => '网站首页',
        'all_link' => '',
        'target' => '',
        'children' => []
      ],
      (object)[
        'title' => '关于我们',
        'all_link' => '',
        'target' => '',
        'children' => [
          (object)['title' => '品牌介绍', 'all_link' => '', 'target' => '',],
          (object)['title' => '发展历程', 'all_link' => '', 'target' => '',],
          (object)['title' => '资质荣誉', 'all_link' => '', 'target' => '',],
          (object)['title' => '科研实力', 'all_link' => '', 'target' => '',],
          (object)['title' => '行业地位', 'all_link' => '', 'target' => '',]
        ]
      ],
      (object)[
        'title' => '产品中心',
        'all_link' => '',
        'target' => '',
        'children' => [
          (object)['title' => '产品展示一', 'all_link' => '', 'target' => '',],
          (object)['title' => '产品展示二', 'all_link' => '', 'target' => '',],
          (object)['title' => '产品展示三', 'all_link' => '', 'target' => '',],
          (object)['title' => '产品展示四', 'all_link' => '', 'target' => '',],
          (object)['title' => '产品展示五', 'all_link' => '', 'target' => '',]
        ]
      ],
      (object)[
        'title' => '案例展示',
        'all_link' => '',
        'target' => '',
        'children' => [
          (object)['title' => '案例展示一', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示二', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示三', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示四', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示五', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示六', 'all_link' => '', 'target' => '',],
          (object)['title' => '案例展示七', 'all_link' => '', 'target' => '',]
        ]
      ],
      (object)[
        'title' => '新闻中心',
        'all_link' => '',
        'target' => '',
        'children' => [
          (object)['title' => '行业新闻', 'all_link' => '', 'target' => '',],
          (object)['title' => '公司新闻', 'all_link' => '', 'target' => '',],
          (object)['title' => '行业动态', 'all_link' => '', 'target' => '',],
          (object)['title' => '公司动态', 'all_link' => '', 'target' => '',],
        ]
      ],
      (object)[
        'title' => '工程案例',
        'all_link' => '',
        'target' => '',
        'children' => [
          (object)['title' => '工程案例一', 'all_link' => '', 'target' => '',],
          (object)['title' => '工程案例二', 'all_link' => '', 'target' => '',],
          (object)['title' => '工程案例三', 'all_link' => '', 'target' => '',],
          (object)['title' => '工程案例四', 'all_link' => '', 'target' => '',],
          (object)['title' => '工程案例五', 'all_link' => '', 'target' => '',],
          (object)['title' => '工程案例六', 'all_link' => '', 'target' => '',]
        ]
      ]
    ];
    return $navData;
  }

  public function render()
  {
    $settings = $this->addon->settings;
    $addon_id = '#jwpf-addon-' . $this->addon->id;
    $class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';

    $nav_links = $this->getList();

    $nav_layout = $this->safeGetProp('nav_layout', 'theme1');

    $output = '';

    if ($nav_layout == 'theme1') {
      // 获取logo配置
      $logo_image = $this->safeGetProp('theme1_logo_image', '');

      // 获取底部内容配置
      $theme1_footer_content = $this->safeGetProp('theme1_footer_content', '');

      $output .= '
            <footer class="footer-wrapper">
              <!-- 红色导航栏 -->
              <nav class="top-nav">
                <ul class="nav-links">
                ';
      foreach ($nav_links as $link) {
        $output .= '<li><a href="' . $link->all_link . '" ' . $link->a_target . '>' . $link->title . '</a></li>
                    ';
      }
      $output .= '
                </ul>
              </nav>';

      // 只有当有logo或有底部信息时才显示深色底部区域
      if ($logo_image || $theme1_footer_content) {
        $output .= '
              <!-- 深色底部区域 -->
              <div class="footer">';

        // 只有设置了logo图片才显示logo区域
        if ($logo_image) {
          $output .= '
                <div class="footer-logo">
                  <img src="' . htmlspecialchars($logo_image, ENT_QUOTES, 'UTF-8') . '" alt="">
                </div>';
        }

        // 只有有底部内容时才显示footer-info区域
        if ($theme1_footer_content) {
          $output .= '
                <div class="footer-info">
                  ' . $theme1_footer_content . '
                </div>';
        }

        $output .= '
              </div>';
      }

      $output .= '
            </footer>
          ';
    } else if ($nav_layout == 'theme2') {
      // 获取theme2配置
      $theme2_logo = $this->safeGetProp('theme2_logo_image', '');
      $theme2_company_name = $this->safeGetProp('theme2_company_name', '');
      $theme2_address = $this->safeGetProp('theme2_address', '');
      $theme2_phone = $this->safeGetProp('theme2_phone', '');
      $theme2_email = $this->safeGetProp('theme2_email', '');
      $theme2_wechat_qr = $this->safeGetProp('theme2_wechat_qr', '');
      $theme2_mobile_qr = $this->safeGetProp('theme2_mobile_qr', '');
      $theme2_copyright = $this->safeGetProp('theme2_copyright', '');

      $output .= '
          <!-- 蓝色底部区域 -->
          <footer class="footer">
            <!-- 导航栏 -->
            <nav class="footer-nav">
              <ul class="nav-links">
                ';
      foreach ($nav_links as $link) {
        $output .= '<li><a href="' . $link->all_link . '" ' . $link->a_target . '>' . $link->title . '</a></li>
                    ';
      }
      $output .= '
              </ul>
            </nav>

            <div class="footer-container">
              <div class="footer-left">';

      // 只有设置了logo才显示
      if ($theme2_logo) {
        $output .= '
                <div class="footer-logo">
                  <img src="' . htmlspecialchars($theme2_logo, ENT_QUOTES, 'UTF-8') . '" alt="">
                </div>';
      }

      // 如果有logo和其他内容，显示分隔线
      if ($theme2_logo && ($theme2_company_name || $theme2_address || $theme2_phone || $theme2_email)) {
        $output .= '
                <div class="divider-line"></div>';
      }

      // 公司信息区域
      if ($theme2_company_name || $theme2_address || $theme2_phone || $theme2_email) {
        $output .= '
                <div class="company-info">';

        if ($theme2_company_name) {
          $output .= '
                  <div class="company-name">' . htmlspecialchars($theme2_company_name, ENT_QUOTES, 'UTF-8') . '</div>';
        }

        if ($theme2_address) {
          $output .= '
                  <div class="contact-item">
                    <img src="https://oss.lcweb01.cn/jzt/255/image/20250828/dc6dae33ba86cd61acf889c7a7d40e17.png" alt="地址">
                    地址：' . htmlspecialchars($theme2_address, ENT_QUOTES, 'UTF-8') . '
                  </div>';
        }

        if ($theme2_phone) {
          $output .= '
                  <div class="contact-item">
                    <img src="https://oss.lcweb01.cn/jzt/255/image/20250828/7a9077635cd95b568d9f06dea80c0240.png" alt="电话">
                    电话：' . htmlspecialchars($theme2_phone, ENT_QUOTES, 'UTF-8') . '
                  </div>';
        }

        if ($theme2_email) {
          $output .= '
                  <div class="contact-item">
                    <img src="https://oss.lcweb01.cn/jzt/255/image/20250828/29954fe8f4103525ebbf58557b90d143.png" alt="邮箱">
                    邮箱：' . htmlspecialchars($theme2_email, ENT_QUOTES, 'UTF-8') . '
                  </div>';
        }

        $output .= '
                </div>';
      }

      $output .= '
              </div>';

      // 二维码区域
      if ($theme2_wechat_qr || $theme2_mobile_qr) {
        $output .= '
              <div class="footer-right">';

        if ($theme2_wechat_qr) {
          $output .= '
                <div class="qr-code">
                  <img src="' . htmlspecialchars($theme2_wechat_qr, ENT_QUOTES, 'UTF-8') . '" alt="微信二维码">
                  <div class="qr-code-label">微信二维码</div>
                </div>';
        }

        if ($theme2_mobile_qr) {
          $output .= '
                <div class="qr-code">
                  <img src="' . htmlspecialchars($theme2_mobile_qr, ENT_QUOTES, 'UTF-8') . '" alt="手机二维码">
                  <div class="qr-code-label">手机二维码</div>
                </div>';
        }

        $output .= '
              </div>';
      }

      $output .= '
            </div>';

      // 版权信息
      if ($theme2_copyright) {
        $output .= '
            <div class="footer-bottom">
              ' . $theme2_copyright . '
            </div>';
      }

      $output .= '
          </footer>
          ';
    } else if ($nav_layout == 'theme3') {
      // 获取theme3配置
      $theme3_chinese_slogan = $this->safeGetProp('theme3_chinese_slogan', '每个企业  都值得拥有科技产业');
      $theme3_english_slogan = $this->safeGetProp('theme3_english_slogan', ' / Every family deserves change');
      $theme3_logo_image = $this->safeGetProp('theme3_logo_image', 'https://oss.lcweb01.cn/jzt/255/image/20250828/9f02ce6979aeac873ba81ee75af6cd5f.png');
      $theme3_contact_label = $this->safeGetProp('theme3_contact_label', '服务热线');
      $theme3_phone = $this->safeGetProp('theme3_phone', '************');
      $theme3_address = $this->safeGetProp('theme3_address', '联系地址：山西省太原市清控创新基地 控创新基地');
      $theme3_social_label = $this->safeGetProp('theme3_social_label', '关注我们：');
      $theme3_social_icon1 = $this->safeGetProp('theme3_social_icon1', 'https://oss.lcweb01.cn/jzt/255/image/20250828/f16d94b7c39a061a5524bcd78e43337c.png');
      $theme3_social_icon2 = $this->safeGetProp('theme3_social_icon2', 'https://oss.lcweb01.cn/jzt/255/image/20250828/3429b3d982d489d71d63dbc3932f5187.png');
      $theme3_social_icon3 = $this->safeGetProp('theme3_social_icon3', 'https://oss.lcweb01.cn/jzt/255/image/20250828/acebedb985a2e3f5391bb53ffedaab38.png');
      $theme3_copyright = $this->safeGetProp('theme3_copyright', 'CopyRight ◎ 2024&nbsp;&nbsp;&nbsp;&nbsp;山西龙采科技有限公司&nbsp;&nbsp;&nbsp;&nbsp;晋ICP备12001731号-2&nbsp;&nbsp;&nbsp;&nbsp;晋公网安备 42018502003958号');

      $output .= '
      <footer class="footer">
        <div class="footer-container">

          <div class="footer-main">
            <div class="footer-left">
              <div class="footer-slogan">
                <div class="slogan-text">
                  <span class="chinese-text">' . htmlspecialchars($theme3_chinese_slogan, ENT_QUOTES, 'UTF-8') . '</span>
                  <span class="english-text">' . htmlspecialchars($theme3_english_slogan, ENT_QUOTES, 'UTF-8') . '</span>
                </div>
              </div>
              <div class="footer-column-wrap">';

      // 动态生成导航栏目
      foreach ($nav_links as $link) {
        $output .= '
                <div class="footer-column">';

        // 一级导航标题，如果有链接则可点击
        if (!empty($link->all_link)) {
          $output .= '
                  <h3 class="column-title"><a href="' . $link->all_link . '" ' . $link->a_target . '>' . htmlspecialchars($link->title, ENT_QUOTES, 'UTF-8') . '</a></h3>';
        } else {
          $output .= '
                  <h3 class="column-title">' . htmlspecialchars($link->title, ENT_QUOTES, 'UTF-8') . '</h3>';
        }

        $output .= '
                  <ul class="column-links">';

        if (!empty($link->children)) {
          foreach ($link->children as $child) {
            $output .= '
                    <li><a href="' . $child->all_link . '" ' . $child->a_target . '>' . htmlspecialchars($child->title, ENT_QUOTES, 'UTF-8') . '</a></li>';
          }
        }

        $output .= '
                  </ul>
                </div>';
      }

      $output .= '
              </div>
            </div>

            <div class="footer-right">';

      // 只有设置了logo才显示
      if ($theme3_logo_image) {
        $output .= '
              <div class="footer-logo">
                <img src="' . htmlspecialchars($theme3_logo_image, ENT_QUOTES, 'UTF-8') . '" alt="Logo">
              </div>';
      }

      // 联系信息
      if ($theme3_contact_label || $theme3_phone || $theme3_address) {
        $output .= '
              <div class="contact-info">';

        if ($theme3_contact_label) {
          $output .= '
                <div class="contact-label">' . htmlspecialchars($theme3_contact_label, ENT_QUOTES, 'UTF-8') . '</div>';
        }

        if ($theme3_phone) {
          $output .= '
                <div class="contact-phone">' . htmlspecialchars($theme3_phone, ENT_QUOTES, 'UTF-8') . '</div>';
        }

        if ($theme3_address) {
          $output .= '
                <div class="contact-address">' . htmlspecialchars($theme3_address, ENT_QUOTES, 'UTF-8') . '</div>';
        }

        $output .= '
              </div>';
      }

      // 社交媒体区域
      if ($theme3_social_label || $theme3_social_icon1 || $theme3_social_icon2 || $theme3_social_icon3) {
        $output .= '
              <div class="social-section">';

        if ($theme3_social_label) {
          $output .= '
                <div class="social-label">' . htmlspecialchars($theme3_social_label, ENT_QUOTES, 'UTF-8') . '</div>';
        }

        $output .= '
                <div class="social-icons">';

        if ($theme3_social_icon1) {
          $output .= '
                  <img src="' . htmlspecialchars($theme3_social_icon1, ENT_QUOTES, 'UTF-8') . '" alt="社交图标1" class="social-icon">';
        }

        if ($theme3_social_icon2) {
          $output .= '
                  <img src="' . htmlspecialchars($theme3_social_icon2, ENT_QUOTES, 'UTF-8') . '" alt="社交图标2" class="social-icon">';
        }

        if ($theme3_social_icon3) {
          $output .= '
                  <img src="' . htmlspecialchars($theme3_social_icon3, ENT_QUOTES, 'UTF-8') . '" alt="社交图标3" class="social-icon">';
        }

        $output .= '
                </div>
              </div>';
      }

      $output .= '
            </div>
          </div>';

      // 版权信息
      if ($theme3_copyright) {
        $output .= '
          <div class="footer-bottom">
            ' . $theme3_copyright . '
          </div>';
      }

      $output .= '
        </div>
      </footer>
      ';
    } else if ($nav_layout == 'theme4') {
      $output .= '
      <footer class="footer">
        <div class="footer-container">
          <div class="footer-main">
            <div class="footer-left">
              <div class="footer-column">
                <h3 class="column-title">关于大行</h3>
                <ul class="column-links">
                  <li><a href="#">公司介绍</a></li>
                  <li><a href="#">发展历程</a></li>
                  <li><a href="#">企业文化</a></li>
                  <li><a href="#">荣誉资质</a></li>
                  <li><a href="#">创新学习</a></li>
                </ul>
              </div>

              <div class="footer-column">
                <h3 class="column-title">产品中心</h3>
                <ul class="column-links">
                  <li><a href="#">建筑材料</a></li>
                  <li><a href="#">装饰材料</a></li>
                  <li><a href="#">OEM代工</a></li>
                  <li><a href="#">创新产品</a></li>
                  <li><a href="#">智能制造</a></li>
                  <li><a href="#">专业制造</a></li>
                </ul>
              </div>

              <div class="footer-column">
                <h3 class="column-title">服务中心</h3>
                <ul class="column-links">
                  <li><a href="#">服务理念</a></li>
                  <li><a href="#">服务保障</a></li>
                  <li><a href="#">售后服务</a></li>
                  <li><a href="#">技术支持</a></li>
                  <li><a href="#">专业制造</a></li>
                  <li><a href="#">智能制造</a></li>
                </ul>
              </div>

              <div class="footer-column">
                <h3 class="column-title">新闻资讯</h3>
                <ul class="column-links">
                  <li><a href="#">集团动态</a></li>
                  <li><a href="#">行业资讯</a></li>
                  <li><a href="#">媒体报道</a></li>
                  <li><a href="#">文件下载</a></li>
                </ul>
              </div>

              <div class="footer-column">
                <h3 class="column-title">联系我们</h3>
                <ul class="column-links">
                  <li><a href="#">联系方式</a></li>
                  <li><a href="#">在线留言</a></li>
                  <li><a href="#">人才招聘</a></li>
                </ul>
              </div>
            </div>
            <div class="column-line"></div>
            <div class="contact-section">
              <div class="contact-label">全国咨询热线</div>
              <div class="contact-phone">************</div>
              <div class="contact-address-label">地址</div>
              <div class="contact-address">
                山西省太原市小店区南中环清控创新基地B座3层
              </div>
            </div>
            <div class="column-line"></div>
            <div class="footer-right">
              <div class="qr-section">
                <div class="qr-code">
                  <img src="https://oss.lcweb01.cn/jzt/255/image/20250828/c476df963dfd0651101794263fe154db.png"
                    alt="关注我们官方微信">
                  <div class="qr-code-label">关注我们官方微信</div>
                </div>
              </div>
            </div>
          </div>

          <div class="footer-bottom">
            <div class="footer-bottom-left">
              © 2020 Copyright 山西龙采科技有限公司&nbsp;&nbsp;&nbsp;&nbsp;免责声明&nbsp;&nbsp;&nbsp;&nbsp;晋ICP备2022007876号 -1
            </div>
            <div class="footer-bottom-right">
              技术支持：龙采科技
            </div>
          </div>
        </div>
      </footer>
      ';
    }

    return $output;
  }

  // 处理适配的值
  public function getResponsiveValues($baseName, $defaults = null)
  {
    $settings = $this->addon->settings;

    // 设置默认值（支持完整覆盖或部分覆盖）
    $finalDefaults = array_merge([
      'md' => '',
      'sm' => '',
      'xs' => ''
    ], (array)$defaults);

    // 动态构建属性名
    $value = $baseName;
    $valueSm = $baseName . '_sm';
    $valueXs = $baseName . '_xs';

    // 检查主属性是否存在
    if (isset($settings->$value)) {
      $mainValue = $settings->$value;

      if ($mainValue && is_object($mainValue)) {
        // 对象处理：从对象属性获取值
        return [
          'md' => $mainValue->md ?? $finalDefaults['md'],
          'sm' => $mainValue->sm ?? $finalDefaults['sm'],
          'xs' => $mainValue->xs ?? $finalDefaults['xs']
        ];
      } elseif ($mainValue) {
        // 标量值处理：从后缀属性获取响应值
        return [
          'md' => $mainValue,
          'sm' => $settings->$valueSm ?? $finalDefaults['sm'],
          'xs' => $settings->$valueXs ?? $finalDefaults['xs']
        ];
      }
    }

    // 当主属性存在但为假值时（如0、空字符串等），返回默认值
    return $finalDefaults;
  }

  /**
   * 处理普通变量 安全获取对象属性值
   * @param object $obj 对象实例
   * @param string $prop 属性名称
   * @param mixed $default 默认值 (默认为'percent')
   * @param bool $strict 严格模式 (true: 0/false视为有效值; false: 0/false视为空值)
   * @return mixed 属性值或默认值
   */
  public function safeGetProp($prop, $default = '', $strict = false)
  {
    $settings = $this->addon->settings;
    // 检查属性是否存在
    if (!isset($settings->$prop)) {
      return $default;
    }
    $value = $settings->$prop;

    // 严格模式：0/false视为有效值
    if ($strict) {
      return $value;
    }
    // 非严格模式：空值检查
    return trim($value) ? $value : $default;
  }

  public function css()
  {
    $settings = $this->addon->settings;
    $addon_id = '#jwpf-addon-' . $this->addon->id;
    $nav_layout = $this->safeGetProp('nav_layout', 'theme1');

    $css = '';

    $css .= $addon_id . ' * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }';

    if ($nav_layout == 'theme1') {
      // 获取theme1的配置值
      $nav_bg_color = $this->safeGetProp('theme1_nav_bg_color', '#94070A');
      $nav_text_color = $this->safeGetProp('theme1_nav_text_color', '#ffffff');
      $nav_hover_color = $this->safeGetProp('theme1_nav_hover_color', '#cccccc');
      $separator_color = $this->safeGetProp('theme1_separator_color', 'rgba(255, 255, 255, 0.2)');
      $separator_height = $this->getResponsiveValues('theme1_separator_height', ['md' => 96, 'sm' => 80, 'xs' => 60]);
      $footer_bg_color = $this->safeGetProp('theme1_footer_bg_color', '#302A29');
      $footer_text_color = $this->safeGetProp('theme1_footer_text_color', 'rgba(255, 255, 255, 0.5)');
      $info_separator_margin = $this->safeGetProp('theme1_info_separator_margin', 15);
      $nav_font_weight = $this->safeGetProp('theme1_nav_font_weight', 'normal');

      // 获取响应式配置值
      $nav_font_size = $this->getResponsiveValues('theme1_nav_font_size', ['md' => 16, 'sm' => 14, 'xs' => 12]);
      $nav_height = $this->getResponsiveValues('theme1_nav_height', ['md' => 124, 'sm' => 100, 'xs' => 80]);
      $nav_padding = $this->getResponsiveValues('theme1_nav_padding', ['md' => 40, 'sm' => 30, 'xs' => 20]);
      $footer_font_size = $this->getResponsiveValues('theme1_footer_font_size', ['md' => 14, 'sm' => 12, 'xs' => 11]);
      $footer_padding = $this->getResponsiveValues('theme1_footer_padding', ['md' => '30px 0', 'sm' => '20px 0', 'xs' => '15px 0']);
      $logo_height = $this->getResponsiveValues('theme1_logo_height', ['md' => 65, 'sm' => 50, 'xs' => 40]);

      $css .= '
            ' . $addon_id . ' .footer-wrapper {
              display: block;
            }

            /* 红色导航栏 */
            ' . $addon_id . ' .top-nav {
              background-color: ' . $nav_bg_color . ';
              height: ' . $nav_height['md'] . 'px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            ' . $addon_id . ' .nav-links {
              display: flex;
              list-style: none;
              align-items: center;
            }

            ' . $addon_id . ' .nav-links li {
              position: relative;
            }

            ' . $addon_id . ' .nav-links li:not(:last-child)::after {
              content: "";
              position: absolute;
              right: -1px;
              top: 50%;
              transform: translateY(-50%);
              width: 1px;
              height: ' . $separator_height['md'] . 'px;
              background-color: ' . $separator_color . ';
            }

            ' . $addon_id . ' .nav-links a {
              color: ' . $nav_text_color . ';
              text-decoration: none;
              font-size: ' . $nav_font_size['md'] . 'px;
              font-weight: ' . $nav_font_weight . ';
              padding: 0 ' . $nav_padding['md'] . 'px;
              display: block;
              transition: color 0.3s;
            }

            ' . $addon_id . ' .nav-links a:hover {
              color: ' . $nav_hover_color . ';
            }

            /* 深色底部区域 */
            ' . $addon_id . ' .footer {
              background-color: ' . $footer_bg_color . ';
              padding: ' . $footer_padding['md'] . ';
              text-align: center;
            }

            ' . $addon_id . ' .footer-logo {
              margin-bottom: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            
            ' . $addon_id . ' .footer:not(:has(.footer-logo)) .footer-info {
              margin-top: 0;
            }

            ' . $addon_id . ' .footer-logo img {
              height: ' . $logo_height['md'] . 'px;
              width: auto;
            }

            ' . $addon_id . ' .footer-info {
              color: ' . $footer_text_color . ';
              font-size: ' . $footer_font_size['md'] . 'px;
              line-height: 1.6;
              text-align: center;
            }

            ' . $addon_id . ' .info-item {
              display: inline;
            }

            ' . $addon_id . ' .info-separator {
              margin: 0 ' . $info_separator_margin . 'px;
            }
          ';
    } else if ($nav_layout == 'theme2') {
      // 获取theme2的配置值
      $theme2_bg_color = $this->safeGetProp('theme2_bg_color', '#195DB3');
      $theme2_text_color = $this->safeGetProp('theme2_text_color', '#ffffff');
      $theme2_nav_hover_color = $this->safeGetProp('theme2_nav_hover_color', '#cccccc');

      // 底部信息样式配置
      $theme2_company_name_font_size = $this->safeGetProp('theme2_company_name_font_size', 18);
      $theme2_company_name_color = $this->safeGetProp('theme2_company_name_color', '#ffffff');
      $theme2_contact_font_size = $this->safeGetProp('theme2_contact_font_size', 16);
      $theme2_contact_color = $this->safeGetProp('theme2_contact_color', '#ffffff');
      $theme2_footer_bottom_font_size = $this->safeGetProp('theme2_footer_bottom_font_size', 14);
      $theme2_footer_bottom_color = $this->safeGetProp('theme2_footer_bottom_color', 'rgba(255, 255, 255, 0.8)');
      $theme2_footer_border_color = $this->safeGetProp('theme2_footer_border_color', 'rgba(255, 255, 255, 0.2)');

      // 获取响应式配置值
      $theme2_nav_font_size = $this->getResponsiveValues('theme2_nav_font_size', ['md' => 18, 'sm' => 16, 'xs' => 14]);
      $theme2_logo_height = $this->getResponsiveValues('theme2_logo_height', ['md' => 68, 'sm' => 55, 'xs' => 45]);
      $theme2_qr_size = $this->getResponsiveValues('theme2_qr_size', ['md' => 130, 'sm' => 110, 'xs' => 90]);
      $theme2_nav_padding = $this->getResponsiveValues('theme2_nav_padding', ['md' => '0 30px 0 30px', 'sm' => '', 'xs' => '']);
      $theme2_separator_height = $this->getResponsiveValues('theme2_separator_height', ['md' => 20, 'sm' => 18, 'xs' => 16]);
      $theme2_max_width = $this->safeGetProp('theme2_max_width', 1300);
      $theme2_content_margin = $this->getResponsiveValues('theme2_content_margin', ['md' => '', 'sm' => '0 20px 0 20px', 'xs' => '0 15px 0 15px']);

      $css .= '
          /* footer内的导航栏 */
          ' . $addon_id . ' .footer-nav {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30px 0;
            margin-bottom: 30px;
          }

          ' . $addon_id . ' .nav-links {
            display: flex;
            list-style: none;
            align-items: center;
          }

          ' . $addon_id . ' .nav-links li {
            position: relative;
            display: flex;
            align-items: center;
            padding: ' . $theme2_nav_padding['md'] . ';
          }

          ' . $addon_id . ' .nav-links li:first-child::before {
            content: "";
            position: absolute;
            left: 0;
            width: 1px;
            height: ' . $theme2_separator_height['md'] . 'px;
            background-color: ' . $theme2_text_color . ';
          }

          ' . $addon_id . ' .nav-links li::after {
            content: "";
            position: absolute;
            right: 0;
            width: 1px;
            height: ' . $theme2_separator_height['md'] . 'px;
            background-color: ' . $theme2_text_color . ';
          }

          ' . $addon_id . ' .nav-links a {
            color: ' . $theme2_text_color . ';
            text-decoration: none;
            font-size: ' . $theme2_nav_font_size['md'] . 'px;
            font-weight: normal;
            transition: color 0.3s;
          }

          ' . $addon_id . ' .nav-links a:hover {
            color: ' . $theme2_nav_hover_color . ';
          }

          /* 蓝色底部区域 */
          ' . $addon_id . ' .footer {
            background-color: ' . $theme2_bg_color . ';
            background-image: url("https://oss.lcweb01.cn/jzt/255/image/20250828/ecd84bc3230fd9ac9924dadbc182f00b.png");
            background-repeat: repeat;
            color: ' . $theme2_text_color . ';
            padding-top: 20px;
          }

          ' . $addon_id . ' .footer-container {
            max-width: ' . $theme2_max_width . 'px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px 20px;
          }

          ' . $addon_id . ' .footer-left {
            display: flex;
            align-items: center;
          }

          ' . $addon_id . ' .divider-line {
            width: 1px;
            height: 162px;
            background-color: rgba(255, 255, 255, 0.32);
            margin-left: 100px;
            margin-right: 60px;
          }

          ' . $addon_id . ' .footer-logo img {
            height: ' . $theme2_logo_height['md'] . 'px;
            width: auto;
          }

          ' . $addon_id . ' .company-info {
            color: ' . $theme2_text_color . ';
            display: flex;
            flex-direction: column;
            gap: 26px;
          }

          ' . $addon_id . ' .company-name {
            font-size: ' . $theme2_company_name_font_size . 'px;
            font-weight: bold;
            color: ' . $theme2_company_name_color . ';
          }

          ' . $addon_id . ' .contact-item {
            display: flex;
            align-items: center;
            font-size: ' . $theme2_contact_font_size . 'px;
            color: ' . $theme2_contact_color . ';
          }

          ' . $addon_id . ' .contact-item img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            object-fit: scale-down;
          }

          ' . $addon_id . ' .footer-right {
            display: flex;
            gap: 30px;
            align-items: center;
          }

          ' . $addon_id . ' .qr-code {
            text-align: center;
          }

          ' . $addon_id . ' .qr-code img {
            width: ' . $theme2_qr_size['md'] . 'px;
            height: ' . $theme2_qr_size['md'] . 'px;
            margin-bottom: 5px;
          }

          ' . $addon_id . ' .qr-code-label {
            font-size: 14px;
            color: ' . $theme2_text_color . ';
          }

          ' . $addon_id . ' .footer-bottom {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid ' . $theme2_footer_border_color . ';
            font-size: ' . $theme2_footer_bottom_font_size . 'px;
            color: ' . $theme2_footer_bottom_color . ';
            line-height: 1.6;
          }

          /* 版权信息富文本样式 */
          ' . $addon_id . ' .footer-bottom p {
            margin: 0 0 8px 0;
            color: inherit;
            font-size: inherit;
            line-height: inherit;
          }

          ' . $addon_id . ' .footer-bottom p:last-child {
            margin-bottom: 0;
          }

          ' . $addon_id . ' .footer-bottom a {
            color: inherit;
            text-decoration: none;
            transition: opacity 0.3s;
          }

          ' . $addon_id . ' .footer-bottom a:hover {
            opacity: 0.8;
            text-decoration: underline;
          }

          ' . $addon_id . ' .footer-bottom strong,
          ' . $addon_id . ' .footer-bottom b {
            font-weight: bold;
            color: inherit;
          }

          ' . $addon_id . ' .footer-bottom em,
          ' . $addon_id . ' .footer-bottom i {
            font-style: italic;
            color: inherit;
          }
          ';
    } else if ($nav_layout == 'theme3') {
      // 获取theme3的配置值
      $theme3_bg_image = $this->safeGetProp('theme3_bg_image', 'https://oss.lcweb01.cn/jzt/255/image/20250828/8745084ba45b3ecf442f7e8e3c40eccd.jpeg');
      $theme3_max_width = $this->safeGetProp('theme3_max_width', 1400);
      $theme3_padding = $this->getResponsiveValues('theme3_padding', ['md' => '60px 0 30px', 'sm' => '40px 0 20px', 'xs' => '30px 0 15px']);

      // 标语样式配置
      $theme3_slogan_font_size = $this->getResponsiveValues('theme3_slogan_font_size', ['md' => 29, 'sm' => 24, 'xs' => 20]);
      $theme3_slogan_color = $this->safeGetProp('theme3_slogan_color', '#ffffff');
      $theme3_english_font_size = $this->getResponsiveValues('theme3_english_font_size', ['md' => 19, 'sm' => 16, 'xs' => 14]);
      $theme3_separator_color = $this->safeGetProp('theme3_separator_color', 'rgba(255, 255, 255, 0.2)');

      // 栏目样式配置
      $theme3_column_title_font_size = $this->safeGetProp('theme3_column_title_font_size', 16);
      $theme3_column_title_color = $this->safeGetProp('theme3_column_title_color', '#ffffff');
      $theme3_link_font_size = $this->safeGetProp('theme3_link_font_size', 16);
      $theme3_link_color = $this->safeGetProp('theme3_link_color', '#858585');
      $theme3_link_hover_color = $this->safeGetProp('theme3_link_hover_color', '#ffffff');

      // 右侧区域样式配置
      $theme3_logo_height = $this->getResponsiveValues('theme3_logo_height', ['md' => 60, 'sm' => 50, 'xs' => 40]);
      $theme3_contact_label_font_size = $this->safeGetProp('theme3_contact_label_font_size', 16);
      $theme3_contact_label_color = $this->safeGetProp('theme3_contact_label_color', 'rgba(255, 255, 255, 1)');
      $theme3_phone_font_size = $this->getResponsiveValues('theme3_phone_font_size', ['md' => 33, 'sm' => 28, 'xs' => 24]);
      $theme3_phone_color = $this->safeGetProp('theme3_phone_color', '#005E3C');
      $theme3_address_font_size = $this->safeGetProp('theme3_address_font_size', 16);
      $theme3_address_color = $this->safeGetProp('theme3_address_color', '#A1A1A1');
      $theme3_social_label_font_size = $this->safeGetProp('theme3_social_label_font_size', 16);
      $theme3_social_label_color = $this->safeGetProp('theme3_social_label_color', '#ffffff');
      $theme3_social_icon_size = $this->getResponsiveValues('theme3_social_icon_size', ['md' => 52, 'sm' => 45, 'xs' => 40]);

      // 底部版权样式配置
      $theme3_copyright_font_size = $this->safeGetProp('theme3_copyright_font_size', 16);
      $theme3_copyright_color = $this->safeGetProp('theme3_copyright_color', '#989898');
      $theme3_copyright_border_color = $this->safeGetProp('theme3_copyright_border_color', 'rgba(255, 255, 255, 0.2)');

      $css .= '
        /* 深色底部区域 */
        ' . $addon_id . ' .footer {
          background-image: url("' . $theme3_bg_image . '");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          color: #fff;
          padding: ' . $theme3_padding['md'] . ';
          position: relative;
        }

        ' . $addon_id . ' .footer-container {
          max-width: ' . $theme3_max_width . 'px;
          margin: 0 auto;
          position: relative;
          z-index: 2;
        }

        ' . $addon_id . ' .footer-main {
          display: flex;
          justify-content: space-between;
          margin-bottom: 40px;
          gap: 50px;
        }

        ' . $addon_id . ' .footer-left {
          flex: 1;
        }

        ' . $addon_id . ' .footer-slogan {
          flex: 1;
        }

        ' . $addon_id . ' .slogan-text {
          font-size: ' . $theme3_slogan_font_size['md'] . 'px;
          color: ' . $theme3_slogan_color . ';
          margin-bottom: 10px;
          line-height: 1.2;
          position: relative;
          padding-bottom: 50px;
          margin-bottom: 50px;
        }

        ' . $addon_id . ' .slogan-text::after {
          content: "";
          position: absolute;
          bottom: 0px;
          left: 0;
          width: 100%;
          height: 1px;
          background-color: ' . $theme3_separator_color . ';
        }

        ' . $addon_id . ' .chinese-text {
          display: inline;
        }

        ' . $addon_id . ' .english-text {
          font-size: ' . $theme3_english_font_size['md'] . 'px;
        }

        ' . $addon_id . ' .footer-column-wrap {
          display: flex;
          gap: 80px;
        }

        ' . $addon_id . ' .footer-column {
          min-width: 100px;
        }

        ' . $addon_id . ' .column-title {
          font-size: ' . $theme3_column_title_font_size . 'px;
          color: ' . $theme3_column_title_color . ';
          margin-bottom: 20px;
          font-weight: bold;
        }

        ' . $addon_id . ' .column-title a {
          color: inherit;
          text-decoration: none;
          transition: color 0.3s;
        }

        ' . $addon_id . ' .column-title a:hover {
          color: ' . $theme3_link_hover_color . ';
        }

        ' . $addon_id . ' .column-links {
          list-style: none;
        }

        ' . $addon_id . ' .column-links li {
          margin-bottom: 12px;
        }

        ' . $addon_id . ' .column-links a {
          color: ' . $theme3_link_color . ';
          text-decoration: none;
          font-size: ' . $theme3_link_font_size . 'px;
          transition: color 0.3s;
        }

        ' . $addon_id . ' .column-links a:hover {
          color: ' . $theme3_link_hover_color . ';
        }

        ' . $addon_id . ' .footer-right {
          min-width: 200px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }

        ' . $addon_id . ' .footer-logo {
          flex-shrink: 0;
          margin-bottom: 60px;
        }

        ' . $addon_id . ' .footer-logo img {
          height: ' . $theme3_logo_height['md'] . 'px;
          width: auto;
        }

        ' . $addon_id . ' .footer-right .footer-logo {
          flex-shrink: 0;
        }

        ' . $addon_id . ' .footer-right .footer-logo img {
          height: ' . $theme3_logo_height['md'] . 'px;
          width: auto;
        }

        ' . $addon_id . ' .contact-info {
          margin-bottom: 70px;
        }

        ' . $addon_id . ' .contact-label {
          font-size: ' . $theme3_contact_label_font_size . 'px;
          color: ' . $theme3_contact_label_color . ';
          margin-bottom: 40px;
        }

        ' . $addon_id . ' .contact-phone {
          font-size: ' . $theme3_phone_font_size['md'] . 'px;
          color: ' . $theme3_phone_color . ';
          font-weight: bold;
          margin-bottom: 15px;
        }

        ' . $addon_id . ' .contact-address {
          font-size: ' . $theme3_address_font_size . 'px;
          color: ' . $theme3_address_color . ';
          line-height: 1.5;
        }

        ' . $addon_id . ' .social-section {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 15px;
          margin-top: 20px;
        }

        ' . $addon_id . ' .social-label {
          font-size: ' . $theme3_social_label_font_size . 'px;
          color: ' . $theme3_social_label_color . ';
          white-space: nowrap;
        }

        ' . $addon_id . ' .social-icons {
          display: flex;
          gap: 15px;
        }

        ' . $addon_id . ' .social-icon {
          width: ' . $theme3_social_icon_size['md'] . 'px;
          height: ' . $theme3_social_icon_size['md'] . 'px;
          transition: opacity 0.3s;
        }

        ' . $addon_id . ' .social-icon:hover {
          opacity: 1;
        }

        ' . $addon_id . ' .footer-bottom {
          border-top: 1px solid ' . $theme3_copyright_border_color . ';
          padding-top: 20px;
          text-align: left;
          font-size: ' . $theme3_copyright_font_size . 'px;
          color: ' . $theme3_copyright_color . ';
          line-height: 1.6;
        }

        /* 版权信息富文本样式 */
        ' . $addon_id . ' .footer-bottom p {
          margin: 0 0 8px 0;
          color: inherit;
          font-size: inherit;
          line-height: inherit;
        }

        ' . $addon_id . ' .footer-bottom p:last-child {
          margin-bottom: 0;
        }

        ' . $addon_id . ' .footer-bottom a {
          color: inherit;
          text-decoration: none;
          transition: opacity 0.3s;
        }

        ' . $addon_id . ' .footer-bottom a:hover {
          opacity: 0.8;
          text-decoration: underline;
        }

        ' . $addon_id . ' .footer-bottom strong,
        ' . $addon_id . ' .footer-bottom b {
          font-weight: bold;
          color: inherit;
        }

        ' . $addon_id . ' .footer-bottom em,
        ' . $addon_id . ' .footer-bottom i {
          font-style: italic;
          color: inherit;
        }
      ';
    } else if ($nav_layout == 'theme4') {
      $css .= '
      /* 深蓝色底部区域 */
      ' . $addon_id . ' .footer {
        background-color: #213F6B;
        color: #fff;
        padding: 50px 0 0px;
      }

      ' . $addon_id . ' .footer-container {
        max-width: 1500px;
        margin: 0 auto;
        padding: 0;
      }

      ' . $addon_id . ' .footer-main {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 60px;
      }

      ' . $addon_id . ' .footer-left {
        flex: 1;
        display: flex;
        gap: 40px;
        padding: 30px 0;
      }

      ' . $addon_id . ' .footer-column {
        min-width: 120px;
      }

      ' . $addon_id . ' .column-title {
        font-size: 16px;
        color: #fff;
        margin-bottom: 25px;
        font-weight: bold;
      }

      ' . $addon_id . ' .column-links {
        list-style: none;
      }

      ' . $addon_id . ' .column-links li {
        margin-bottom: 15px;
      }

      ' . $addon_id . ' .column-links a {
        color: #B8C5D1;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s;
      }

      ' . $addon_id . ' .column-links a:hover {
        color: #fff;
      }

      ' . $addon_id . ' .footer-right {
        display: flex;
        align-items: flex-start;
        gap: 40px;
      }

      ' . $addon_id . ' .column-line {
        width: 1px;
        align-self: stretch;
        background: #38537A;
      }

      ' . $addon_id . ' .contact-section {
        flex: 1;
        padding: 30px 0;
      }

      ' . $addon_id . ' .contact-label {
        font-size: 16px;
        color: #fff;
        margin-bottom: 20px;
        text-align: left;
        position: relative;
        padding-bottom: 15px;
      }

      ' . $addon_id . ' .contact-label::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 1px;
        background-color: #fff;
      }

      ' . $addon_id . ' .contact-phone {
        font-size: 40px;
        color: #fff;
        font-weight: bold;
        margin-bottom: 30px;
        text-align: left;
        line-height: 1.2;
      }

      ' . $addon_id . ' .contact-address-label {
        font-size: 16px;
        color: #fff;
        margin-bottom: 20px;
        text-align: left;
        position: relative;
        padding-bottom: 15px;
      }

      ' . $addon_id . ' .contact-address-label::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 1px;
        background-color: #fff;
      }

      ' . $addon_id . ' .contact-address {
        font-size: 16px;
        color: #fff;
        line-height: 1.5;
        text-align: left;
      }

      ' . $addon_id . ' .qr-section {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 30px 0;
      }

      ' . $addon_id . ' .qr-code {
        text-align: center;
      }

      ' . $addon_id . ' .qr-code img {
        width: 160px;
        height: 160px;
        margin-bottom: 8px;
      }

      ' . $addon_id . ' .qr-code-label {
        font-size: 16px;
        color: #CCCCCC;
      }

      ' . $addon_id . ' .footer-bottom {
        border-top: 1px solid #38537A;
        padding: 20px 0;
        text-align: center;
        font-size: 14px;
        color: #B8C5D1;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      ' . $addon_id . ' .footer-bottom-left {
        font-size: 14px;
      }

      ' . $addon_id . ' .footer-bottom-right {
        font-size: 14px;
      }
      ';
    }

    $css .= '
        @media (min-width: 768px) and (max-width: 991px) {';

    if ($nav_layout == 'theme1') {
      $css .= '
            ' . $addon_id . ' .top-nav {
              height: ' . $nav_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links li:not(:last-child)::after {
              height: ' . $separator_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links a {
              font-size: ' . $nav_font_size['sm'] . 'px;
              padding: 0 ' . $nav_padding['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer {
              padding: ' . $footer_padding['sm'] . ';
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $logo_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer-info {
              font-size: ' . $footer_font_size['sm'] . 'px;
              max-width: 90%;
            }

          ';
    } else if ($nav_layout == 'theme2') {
      $css .= '
            ' . $addon_id . ' .nav-links li {
              padding: ' . $theme2_nav_padding['sm'] . ';
            }
            
            ' . $addon_id . ' .nav-links li:first-child::before {
              height: ' . $theme2_separator_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links li::after {
              height: ' . $theme2_separator_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links a {
              font-size: ' . $theme2_nav_font_size['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $theme2_logo_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .qr-code img {
              width: ' . $theme2_qr_size['sm'] . 'px;
              height: ' . $theme2_qr_size['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer-container {
              margin: ' . $theme2_content_margin['sm'] . ';
              flex-direction: column;
              gap: 30px;
            }
          ';
    } else if ($nav_layout == 'theme3') {
      $css .= '
            ' . $addon_id . ' .footer {
              padding: ' . $theme3_padding['sm'] . ';
            }
            
            ' . $addon_id . ' .footer-container {
              max-width: 90%;
            }
            
            ' . $addon_id . ' .footer-main {
              gap: 30px;
              flex-direction: column;
            }
            
            ' . $addon_id . ' .slogan-text {
              font-size: ' . $theme3_slogan_font_size['sm'] . 'px;
              padding-bottom: 30px;
              margin-bottom: 30px;
            }
            
            ' . $addon_id . ' .english-text {
              font-size: ' . $theme3_english_font_size['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer-column-wrap {
              gap: 40px;
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $theme3_logo_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .footer-right .footer-logo img {
              height: ' . $theme3_logo_height['sm'] . 'px;
            }
            
            ' . $addon_id . ' .contact-info {
              margin-bottom: 40px;
            }
            
            ' . $addon_id . ' .contact-phone {
              font-size: ' . $theme3_phone_font_size['sm'] . 'px;
            }
            
            ' . $addon_id . ' .social-icon {
              width: ' . $theme3_social_icon_size['sm'] . 'px;
              height: ' . $theme3_social_icon_size['sm'] . 'px;
            }
          ';
    }

    $css .= '
        }
        @media (max-width: 767px) {';

    if ($nav_layout == 'theme1') {
      $css .= '
            ' . $addon_id . ' .top-nav {
              height: ' . $nav_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links {
              flex-wrap: wrap;
              justify-content: center;
            }
            
            ' . $addon_id . ' .nav-links li:not(:last-child)::after {
              display: none;
            }
            
            ' . $addon_id . ' .nav-links a {
              font-size: ' . $nav_font_size['xs'] . 'px;
              padding: 5px ' . $nav_padding['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer {
              padding: ' . $footer_padding['xs'] . ';
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $logo_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-info {
              font-size: ' . $footer_font_size['xs'] . 'px;
              max-width: 95%;
              line-height: 1.5;
            }
            
            ' . $addon_id . ' .info-separator {
              display: none;
            }
          ';
    } else if ($nav_layout == 'theme2') {
      $css .= '
            ' . $addon_id . ' .nav-links {
              flex-wrap: wrap;
              justify-content: center;
            }
            
            ' . $addon_id . ' .nav-links li {
              padding: ' . $theme2_nav_padding['xs'] . ';
            }
            
            ' . $addon_id . ' .nav-links li:first-child::before {
              height: ' . $theme2_separator_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links li::after {
              height: ' . $theme2_separator_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .nav-links a {
              font-size: ' . $theme2_nav_font_size['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $theme2_logo_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .qr-code img {
              width: ' . $theme2_qr_size['xs'] . 'px;
              height: ' . $theme2_qr_size['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-container {
              margin: ' . $theme2_content_margin['xs'] . ';
              flex-direction: column;
              gap: 20px;
            }
            
            ' . $addon_id . ' .footer-left {
              flex-direction: column;
              gap: 20px;
            }
            
            ' . $addon_id . ' .divider-line {
              display: none;
            }
            
            ' . $addon_id . ' .footer-right {
              gap: 20px;
            }
          ';
    } else if ($nav_layout == 'theme3') {
      $css .= '
            ' . $addon_id . ' .footer {
              padding: ' . $theme3_padding['xs'] . ';
            }
            
            ' . $addon_id . ' .footer-container {
              max-width: 95%;
            }
            
            ' . $addon_id . ' .footer-main {
              flex-direction: column;
              gap: 30px;
              margin-bottom: 30px;
            }
            
            ' . $addon_id . ' .footer-left {
              order: 1;
            }
            
            ' . $addon_id . ' .footer-right {
              order: 2;
              align-items: center;
              min-width: auto;
            }
            
            ' . $addon_id . ' .slogan-text {
              font-size: ' . $theme3_slogan_font_size['xs'] . 'px;
              padding-bottom: 20px;
              margin-bottom: 20px;
              text-align: center;
            }
            
            ' . $addon_id . ' .english-text {
              font-size: ' . $theme3_english_font_size['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-column-wrap {
              flex-wrap: wrap;
              gap: 20px;
              justify-content: center;
            }
            
            ' . $addon_id . ' .footer-column {
              min-width: 120px;
              text-align: center;
            }
            
            ' . $addon_id . ' .column-title {
              font-size: 14px;
              margin-bottom: 15px;
            }
            
            ' . $addon_id . ' .column-links a {
              font-size: 14px;
            }
            
            ' . $addon_id . ' .footer-logo {
              margin-bottom: 30px;
            }
            
            ' . $addon_id . ' .footer-logo img {
              height: ' . $theme3_logo_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-right .footer-logo img {
              height: ' . $theme3_logo_height['xs'] . 'px;
            }
            
            ' . $addon_id . ' .contact-info {
              margin-bottom: 30px;
              text-align: center;
            }
            
            ' . $addon_id . ' .contact-label {
              font-size: 14px;
              margin-bottom: 20px;
            }
            
            ' . $addon_id . ' .contact-phone {
              font-size: ' . $theme3_phone_font_size['xs'] . 'px;
            }
            
            ' . $addon_id . ' .contact-address {
              font-size: 14px;
            }
            
            ' . $addon_id . ' .social-section {
              justify-content: center;
              margin-top: 15px;
            }
            
            ' . $addon_id . ' .social-label {
              font-size: 14px;
            }
            
            ' . $addon_id . ' .social-icon {
              width: ' . $theme3_social_icon_size['xs'] . 'px;
              height: ' . $theme3_social_icon_size['xs'] . 'px;
            }
            
            ' . $addon_id . ' .footer-bottom {
              text-align: center;
              font-size: 14px;
            }
          ';
    }

    $css .= '
        }';

    return $css;
  }
}