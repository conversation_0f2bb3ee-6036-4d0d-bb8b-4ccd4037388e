<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>orker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonTiyuyunguan_swiper extends JwpagefactoryAddons
{

    public function render()
    {
        $settings = $this->addon->settings;
        $output   = '<style>';
        $output .= '     #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' a {
                color:#333;
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' a:link{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' a:visited{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' a:hover{text-decoration:none;cursor:pointer;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' a:active{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .hidden{display:none;visibility:hidden;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap{border: 0;padding:0;margin:0 auto;*zoom:1;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap:after{clear: both;content:"";display: table;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .clear{clear:both;font-size:0;line-height:0;height:0;visibility:hidden;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .clearfix:before, .clearfix:after {
                content:"";
                display:table;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .clearfix:after {
                clear:both;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .clearfix {
                zoom:1;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap {
                padding:0 100px;
                margin:0 auto;
                position:relative;
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap {padding:0 4.5%;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .mc-hidden {display:none}
            }
            @media (min-width:990px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .lg-hidden1 {display:none}
            }
            @media (max-width:767px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .sm-hidden {display:none;}
            }
            @media (min-width:768px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .lg-hidden {display:none;}
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap:after,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap:before,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container:after,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container:before{
                content:"";
                display:table;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .wrap:after,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container:after {clear:both;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container {width:80%;margin:0 auto;}
            @media (min-width:1200px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container {width:1200px;}
            }
            @media (min-width:1450px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container {width:1330px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container {width:1530px;}
            }
            @media (min-width:1450px) {
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-page .container {width:88%;}
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .container {width:91%;}
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .clear {clear:both;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .fl{float: left;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .fr{float: right;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .absolute { position: absolute;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .pors{position: relative;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .tl{text-align: left;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .tc{text-align: center;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .tr{text-align: right;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .hide {opacity:0;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .en {font-family:"gotham-medium";}
            /*header*/

            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-page-block {
                /*z-index:99;*/
                /*position:relative;*/
                background:#fff;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service {
                position:relative;
                overflow:hidden;
                height: 735px;
            }
            @media (max-width:1449px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .container {width:91%;}
            }

            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .swiper-slide-text-scroll {position:relative;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-container {
                position:static;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {
                position:relative;
                padding:10% 7.5%;
            }
            @media (min-width:1200px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap { height:575px;}
            }
            @media (min-width:1400px){
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . '  .index-service-scroll-wrap { height:638px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap { height:750px;}
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap {
                float:left;
                width:350px;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each {top:15px;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-image {
                position:absolute;
                opacity: 0;
                transition: all 1s ease 0s;
                -webkit-transition: all 1s ease 0s;
                -moz-transition: all 1s ease 0s;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-image {
                width:100%;
                height:100%;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each.active,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-image.active {position:relative;z-index:2;opacity:1;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {
                margin-bottom:20px;
                font-weight:bold;
                font-size:21px;
                color:#333;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:40px;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition dl, ol, ul{margin-left: -25px;}
             #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition li{list-style-type: none;}
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {
                display:block;
                padding:8px 0;
                font-size:16px;
                color:#333;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap {
                position:absolute;
                left:0;
                top:0;
                display:block;
                width:100%;
                height:100%;
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap img {
                max-width:100%;
                height: 100%;
                background-color: #999999;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {
                z-index:3;
                position:absolute;
                right:40px;
               /* bottom:45px;*/
               bottom: 110px;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab {
                margin-right:25px;
                margin-bottom:30px;
                border-right:1px solid rgba(186,186,186,.3);
            }

            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .tab {
                position:relative;
                width:110px;
                margin-right:-10px;
                line-height:40px;
                cursor:pointer;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .tab-key {
                font-weight:normal;
                font-size:12px;
                color:#666;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .each-tab.active .tab-key {color:#666;font-weight:bold;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .dot {
                position:absolute;
                right:1px;
                top:15px;
                display:block;
                width:13px;
                height:13px;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .dot:before {
                content:"";
                position:absolute;
                top: 42%;
                left: 45%;
                display:block;
                width:3px;
                height:3px;
                margin:-2.5px 0 0 -2.5px;
                background:#bababa;
                border-radius:100%;
                transition:all .5s ease-in-out;
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .each-tab.active .dot:before {background:#666;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .dot:after {
                content:"";
                position:absolute;
                display:block;
                width:9px;
                height:9px;
                border:2px solid #666;
                border-radius:100%;
                transform:scale(0);
                transition:all .5s ease-in-out;
            }';
        $output .= '
             #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .each-tab.active .dot:after {transform:scale(1);}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .each-tab.active .tab-key {font-weight:bold;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .prograss-bar {float:right;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont,.ourwork-mobile-images-scroll .work-buttons .iconfont {
                position:relative;
                display:block;
                width:50px;
                height:50px;
                margin-top:15px;
                background:#fff;
                border-radius:100%;
                -moz-border-radius:100%;
                -webkit-border-radius:100%;
                transition:all .5s ease-in-out;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-images-scroll .work-buttons .iconfont {background:transparent;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-images-scroll .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont:after,.ourwork-mobile-images-scroll .work-buttons .iconfont:after{
                position:absolute;
                left:0;
                top:0;
                content:"";
                display:block;
                width:100%;
                height:100%;
                border-radius:100%;
                -moz-border-radius:100%;
                -webkit-border-radius:100%;
                transition:all .5s ease-in-out;
                box-sizing:border-box;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .radiu-button {
                z-index:2;
                position:relative;
                display:block;
                width:215px;
                height:60px;
                font:normal 16px/60px "gotham-medium";
                text-align:center;
                background:#fff;
                border:1px solid #fff;
                border-radius:30px;
                transition: all .4s ease-in-out;
                overflow:hidden;
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .radiu-button:hover {
                color: #fff;
                background:#000;
                border-color:#000;
            }';

        $output .= '   #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .radiu-button:hover:after {
                transform-origin: center bottom;
                transform: scaleY(1);
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-images-scroll .work-buttons .iconfont:before { z-index:1;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .prev:before {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/left-icon1.png) no-repeat center center;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .next:before {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/right-icon1.png) no-repeat center center;}


            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont:after,
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-images-scroll .work-buttons .iconfont:after {
                transform:scale(0);
            }
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont:after {background:#339ec1;}
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-images-scroll .work-buttons .iconfont:after {background:#fff;}

            @media(max-width:1200px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-title {padding-bottom:25px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service {padding:70px 0 80px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {min-height:25rem;padding:5% 7.5%;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {margin-bottom:10px;font-size:23px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:3px 0;font-size:14px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {bottom:15px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .tab {line-height:30px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab .dot {top:8px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .radiu-button {width:130px;height:50px;    font: normal 14px/50px "gotham-medium";line-height:50px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont, .ourwork-mobile-images-scroll .work-buttons .iconfont {width:45px;height:45px;  }
            }
            @media (max-width:1330px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {padding:5% 7.5%;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {top:110px;bottom:auto;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab {margin-bottom:50px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab { margin-bottom:10px;}
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service {padding:50px 0;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {min-height:20rem;padding:0;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap { position:relative;height:20rem; margin-left: 0;  }
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap img {width:100%;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-image {right:0;width:100%;height:100%;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .swiper-slide-text-scroll {position:static;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each.active {position:absolute;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each {top:25px;left:25px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:2rem;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {font-size:28px;margin-bottom:10px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:5px 0;font-size:14px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .radiu-button {display:none;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {  top: 110px;right:25px;  bottom: auto;  }
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab {margin-bottom:25px;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap img {
                    width: 100%;
                }

            }
            @media (max-width:900px){
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {min-height:18rem;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap {height:18rem;}
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {  top:auto;right:25px;  bottom:45px;  }
                #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab {display:none;}
            }';

        $output .= '
            #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .swiper-container {position:relative;width:100%;height:100%;}

            @media (max-width:768px){
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service {padding:35px 0;height: 470px;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap {float:none;width:70%;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons {right:1.5rem;bottom:2rem;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count {margin-bottom:45px;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count i {height:90px;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . '  .index-service .work-buttons .prev:before {  background: url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/left-icon1.png) no-repeat center center;  background-size:10px 20px;  }
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . '  .index-service .work-buttons .next:before {  background: url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/right-icon1.png) no-repeat center center;  background-size:10px 20px;  }
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {font-size:20px;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:3px 0;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap img {
                    width: 100%;
                }

            }';
        $output .= '
            @media (max-width:768px){
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-container {position:relative;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap {height:auto;padding:0;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap {  height:auto;  }
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-image {padding:0;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-image-wrap img {width:100%;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each {width:70%;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each.active {position:absolute;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-scroll {position:absolute;bottom:0;width:100%;height:100%;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .swiper-slide-text-each {top:auto;bottom:5%;left:10%;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:0;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .count-tab {display:none;}
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .ourwork-mobile-article .back-wrap .icon,
               #jwpf-tiyuyunguan-swiper-' . $this->addon->id . ' .index-service .work-buttons .iconfont, .ourwork-mobile-images-scroll .work-buttons .iconfont {width:40px;height:40px;}

            }';
        $output .= '</style>';

        $output .= '<div class="js-container" id="jwpf-tiyuyunguan-swiper-' . $this->addon->id . '">';

        $output .= '<div class="index-page-block">';
        $output .= '<div class="index-service index-wrap active">';
        $output .= '<div class="container">';
        $output .= '<div class="index-service-scroll-wrap">';
        $output .= '<div class="swiper-container">';
        $output .= '<div class="swiper-slide-inner clearfix">';
        $output .= '<div class="swiper-slide-image-wrap">';
        foreach ($settings->jw_tiyuyunguan_item as $key => $value) {
            $output .= '<div class="swiper-slide-image ' . (($key == 0) ? 'active' : '') . '">';
            $output .= '<div class="sm-hidden">';
            $output .= '<img src="' . $value->bg . '">';
            $output .= '</div>';
            $output .= '<div class="lg-hidden">';
            $output .= ' <img src="' . $value->wapbg . '" alt="' . $value->title . '">';
            $output .= '</div>';
            $output .= '</div>';
        }
        $output .= '</div>';
        $output .= '<div class="swiper-slide-text-scroll">';

        foreach ($settings->jw_tiyuyunguan_item as $key2 => $value2) {
            $output .= ' <div class="swiper-slide-text-each ' . (($key2 == 0) ? 'active' : '') . '">';
            $output .= '<div class="swiper-slide-text-wrap">';
            $output .= '<div class="swiper-slide-text">';
            $output .= '<h2 class="slide-title">' . $value2->title . '</h2>';
            $output .= '<div class="slide-descrition">' . $value2->content . '</div>';
            $output .= '<a href="' . $value2->button_url . '" class="radiu-button">查看案例</a>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="work-buttons">';
        $output .= '<div class="work-button">';
        $output .= '<div class="count-tab">';

        foreach ($settings->jw_tiyuyunguan_item as $key3 => $value3) {
            $output .= ' <div class="each-tab ' . (($key3 == 0) ? 'active' : '') . '">';
            $output .= ' <div class="tab">';
            $output .= ' <strong class="tab-key">' . $value3->title . '</strong>';
            $output .= ' <span class="dot"></span>';
            $output .= ' </div>';
            $output .= ' </div>';
        }

        $output .= '</div>';
        $output .= '<div class="prograss-bar">';
        $output .= ' <a class="prev iconfont"></a><a class="next iconfont"></a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= "
            <script>

            (function(jQuery){
                var mainWit = jQuery(window).width(),
                    mainHit = jQuery(window).height();
                //index-service
                jQuery.fn.serviceScroll = function() {
                    return this.each(function () {
                        var jQuerythis = jQuery(this),
                            wslide = jQuerythis.find('.swiper-slide-image-wrap .swiper-slide-image'),
                            wslide1 = jQuerythis.find('.swiper-slide-image-wrap .swiper-slide-image:first-child'),
                            nslide = jQuerythis.find('.swiper-slide-text-scroll .swiper-slide-text-each'),
                            dot = jQuerythis.find('.work-buttons .work-button .each-tab'),
                            p = jQuerythis.find('.work-buttons .work-button a.prev'),
                            l = jQuerythis.find('.work-buttons .work-button a.next'),
                            wlengh = wslide.length- 1,
                            wi = 0,
                            sets = null;
                        wslide1.addClass('active').siblings().removeClass('active');

                        function sides() {
                            wslide.eq(wi).addClass('active').siblings().removeClass('active');
                            nslide.eq(wi).addClass('active').siblings().removeClass('active');
                            dot.eq(wi).addClass('active').siblings().removeClass('active');
                        }
                        p.click(function () {
                            clearInterval(sets);
                            wi--;
                            wi = wi < 0 ? wlengh : wi;
                            sides();
                        });
                        l.click(function () {

                            wi++;
                            wi = wi > wlengh ? 0 : wi;
                            sides();
                        });
                        dot.click(function () {
                            jQuerythis.find(' .swiper-slide-image-wrap').stop();
                            jQuerythis.find(' .swiper-slide-text-scroll').stop();
                            this_ = jQuery(this).index();
                            wi = this_;
                            wslide.eq(wi).addClass('active').siblings().removeClass('active');
                            nslide.eq(wi).addClass('active').siblings().removeClass('active');
                            dot.eq(wi).addClass('active').siblings().removeClass('active')
                        });
                        sets = setInterval(function(){l.click()}, 6000);
                    })
                }
                jQuery('.index-service').serviceScroll();

            })(jQuery);
            </script>
            ";
        return $output;
    }
    public static function getTemplate()
    {

        $output = '<style>';
        $output .= '#jwpf-tiyuyunguan-swiper-{{data.id}} a {
                color:#333;
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} a:link{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} a:visited{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} a:hover{text-decoration:none;cursor:pointer;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} a:active{text-decoration:none;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .hidden{display:none;visibility:hidden;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap{border: 0;padding:0;margin:0 auto;*zoom:1;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap:after{clear: both;content:"";display: table;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .clear{clear:both;font-size:0;line-height:0;height:0;visibility:hidden;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .clearfix:before, .clearfix:after {
                content:"";
                display:table;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .clearfix:after {
                clear:both;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .clearfix {
                zoom:1;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap {
                padding:0 100px;
                margin:0 auto;
                position:relative;
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap {padding:0 4.5%;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .mc-hidden {display:none}
            }
            @media (min-width:990px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .lg-hidden1 {display:none}
            }
            @media (max-width:767px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .sm-hidden {display:none;}
            }
            @media (min-width:768px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .lg-hidden {display:none;}
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap:after,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap:before,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .container:after,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .container:before{
                content:"";
                display:table;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .wrap:after,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .container:after {clear:both;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .container {width:80%;margin:0 auto;}
            @media (min-width:1200px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .container {width:1200px;}
            }
            @media (min-width:1450px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .container {width:1330px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .container {width:1530px;}
            }
            @media (min-width:1450px) {
                #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-page .container {width:88%;}
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .container {width:91%;}
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-{{data.id}} .clear {clear:both;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .fl{float: left;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .fr{float: right;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .absolute { position: absolute;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .pors{position: relative;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .tl{text-align: left;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .tc{text-align: center;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .tr{text-align: right;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .hide {opacity:0;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .en {font-family:"gotham-medium";}
            /*header*/

            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-page-block {
                /*z-index:99;*/
                /*position:relative;*/
                background:#fff;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service {
                position:relative;
                overflow:hidden;
                height: 735px;
            }
            @media (max-width:1449px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .container {width:91%;}
            }

            #jwpf-tiyuyunguan-swiper-{{data.id}} .swiper-slide-text-scroll {position:relative;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-container {
                position:static;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {
                position:relative;
                padding:10% 7.5%;
            }
            @media (min-width:1200px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap { height:575px;}
            }
            @media (min-width:1400px){
               #jwpf-tiyuyunguan-swiper-{{data.id}}  .index-service-scroll-wrap { height:638px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap { height:750px;}
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap {
                float:left;
                width:350px;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each {top:15px;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-image {
                position:absolute;
                opacity: 0;
                transition: all 1s ease 0s;
                -webkit-transition: all 1s ease 0s;
                -moz-transition: all 1s ease 0s;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-image {
                width:100%;
                height:100%;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each.active,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-image.active {position:relative;z-index:2;opacity:1;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {
                margin-bottom:20px;
                font-weight:bold;
                font-size:21px;
                color:#333;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:40px;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition dl, ol, ul{margin-left: -25px;}
             #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition li{list-style-type: none;}
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {
                display:block;
                padding:8px 0;
                font-size:16px;
                color:#333;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap {
                position:absolute;
                left:0;
                top:0;
                display:block;
                width:100%;
                height:100%;
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap img {
                max-width:100%;
                height: 100%;
                background-color: #999999;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {
                z-index:3;
                position:absolute;
                right:40px;
               /* bottom:45px;*/
               bottom: 110px;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab {
                margin-right:25px;
                margin-bottom:30px;
                border-right:1px solid rgba(186,186,186,.3);
            }

            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .tab {
                position:relative;
                width:110px;
                margin-right:-10px;
                line-height:40px;
                cursor:pointer;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .tab-key {
                font-weight:normal;
                font-size:12px;
                color:#666;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .each-tab.active .tab-key {color:#666;font-weight:bold;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .dot {
                position:absolute;
                right:1px;
                top:15px;
                display:block;
                width:13px;
                height:13px;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .dot:before {
                content:"";
                position:absolute;
                top: 42%;
                left: 45%;
                display:block;
                width:3px;
                height:3px;
                margin:-2.5px 0 0 -2.5px;
                background:#bababa;
                border-radius:100%;
                transition:all .5s ease-in-out;
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .each-tab.active .dot:before {background:#666;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .dot:after {
                content:"";
                position:absolute;
                display:block;
                width:9px;
                height:9px;
                border:2px solid #666;
                border-radius:100%;
                transform:scale(0);
                transition:all .5s ease-in-out;
            }';
        $output .= '
             #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .each-tab.active .dot:after {transform:scale(1);}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .each-tab.active .tab-key {font-weight:bold;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .prograss-bar {float:right;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont,.ourwork-mobile-images-scroll .work-buttons .iconfont {
                position:relative;
                display:block;
                width:50px;
                height:50px;
                margin-top:15px;
                background:#fff;
                border-radius:100%;
                -moz-border-radius:100%;
                -webkit-border-radius:100%;
                transition:all .5s ease-in-out;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-images-scroll .work-buttons .iconfont {background:transparent;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-images-scroll .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont:after,.ourwork-mobile-images-scroll .work-buttons .iconfont:after{
                position:absolute;
                left:0;
                top:0;
                content:"";
                display:block;
                width:100%;
                height:100%;
                border-radius:100%;
                -moz-border-radius:100%;
                -webkit-border-radius:100%;
                transition:all .5s ease-in-out;
                box-sizing:border-box;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .radiu-button {
                z-index:2;
                position:relative;
                display:block;
                width:215px;
                height:60px;
                font:normal 16px/60px "gotham-medium";
                text-align:center;
                background:#fff;
                border:1px solid #fff;
                border-radius:30px;
                transition: all .4s ease-in-out;
                overflow:hidden;
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .radiu-button:hover {
                color: #fff;
                background:#000;
                border-color:#000;
            }';

        $output .= '   #jwpf-tiyuyunguan-swiper-{{data.id}} .radiu-button:hover:after {
                transform-origin: center bottom;
                transform: scaleY(1);
            }';
        $output .= '
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont:before,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-images-scroll .work-buttons .iconfont:before { z-index:1;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .prev:before {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/left-icon1.png) no-repeat center center;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .next:before {background:url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/right-icon1.png) no-repeat center center;}


            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont:after,
            #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-images-scroll .work-buttons .iconfont:after {
                transform:scale(0);
            }
            #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont:after {background:#339ec1;}
            #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-images-scroll .work-buttons .iconfont:after {background:#fff;}

            @media(max-width:1200px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-title {padding-bottom:25px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service {padding:70px 0 80px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {min-height:25rem;padding:5% 7.5%;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {margin-bottom:10px;font-size:23px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:3px 0;font-size:14px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {bottom:15px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .tab {line-height:30px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab .dot {top:8px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .radiu-button {width:130px;height:50px;    font: normal 14px/50px "gotham-medium";line-height:50px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont, .ourwork-mobile-images-scroll .work-buttons .iconfont {width:45px;height:45px;  }
            }
            @media (max-width:1330px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {padding:5% 7.5%;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {top:110px;bottom:auto;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab {margin-bottom:50px;}
            }
            @media (min-width:1530px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab { margin-bottom:10px;}
            }
            @media (max-width:991px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service {padding:50px 0;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {min-height:20rem;padding:0;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap { position:relative;height:20rem; margin-left: 0;  }
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap img {width:100%;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-image {right:0;width:100%;height:100%;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .swiper-slide-text-scroll {position:static;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each.active {position:absolute;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each {top:25px;left:25px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:2rem;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {font-size:28px;margin-bottom:10px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:5px 0;font-size:14px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .radiu-button {display:none;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {  top: 110px;right:25px;  bottom: auto;  }
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab {margin-bottom:25px;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap img {
                    width: 100%;
                }

            }
            @media (max-width:900px){
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {min-height:18rem;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap {height:18rem;}
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {  top:auto;right:25px;  bottom:45px;  }
                #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab {display:none;}
            }';

        $output .= '
            #jwpf-tiyuyunguan-swiper-{{data.id}} .swiper-container {position:relative;width:100%;height:100%;}

            @media (max-width:768px){
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service {padding:35px 0;height: 470px;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap {float:none;width:70%;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons {right:1.5rem;bottom:2rem;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count {margin-bottom:45px;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count i {height:90px;}
               #jwpf-tiyuyunguan-swiper-{{data.id}}  .index-service .work-buttons .prev:before {  background: url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/left-icon1.png) no-repeat center center;  background-size:10px 20px;  }
               #jwpf-tiyuyunguan-swiper-{{data.id}}  .index-service .work-buttons .next:before {  background: url(/components/com_jwpagefactory/addons/tiyuyunguan_swiper/assets/images/right-icon1.png) no-repeat center center;  background-size:10px 20px;  }
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-title {font-size:20px;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition span {padding:3px 0;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap img {
                    width: 100%;
                }

            }';
        $output .= '
            @media (max-width:768px){
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-container {position:relative;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap {height:auto;padding:0;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap {  height:auto;  }
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-image {padding:0;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-image-wrap img {width:100%;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each {width:70%;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each.active {position:absolute;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-scroll {position:absolute;bottom:0;width:100%;height:100%;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .swiper-slide-text-each {top:auto;bottom:5%;left:10%;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service-scroll-wrap .swiper-slide-text-wrap .slide-descrition {margin-bottom:0;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .count-tab {display:none;}
               #jwpf-tiyuyunguan-swiper-{{data.id}} .ourwork-mobile-article .back-wrap .icon,
               #jwpf-tiyuyunguan-swiper-{{data.id}} .index-service .work-buttons .iconfont, .ourwork-mobile-images-scroll .work-buttons .iconfont {width:40px;height:40px;}

            }';
        $output .= '</style>';

        $output .= '<div class="js-container" id="jwpf-tiyuyunguan-swiper-{{data.id}}">';

        $output .= '<div class="index-page-block">';
        $output .= '<div class="index-service index-wrap active">';
        $output .= '<div class="container">';
        $output .= '<div class="index-service-scroll-wrap">';
        $output .= '<div class="swiper-container">';
        $output .= '<div class="swiper-slide-inner clearfix">';
        $output .= '<div class="swiper-slide-image-wrap">';
        $output .= '         <# _.each(data.jw_tiyuyunguan_item, function (tiyuyunguan_item, key){ #>
              <#
                        var classNames = (key == 0) ? "active" : "";
                    #>
                        <div class="swiper-slide-image {{ classNames }} ">
                             <div class="sm-hidden">
                                 <# if(tiyuyunguan_item.bg && tiyuyunguan_item.bg.indexOf("http://") == -1 && tiyuyunguan_item.bg.indexOf("https://") == -1){ #>
                                    <img src=\'{{ pagefactory_base + tiyuyunguan_item.bg }}\' alt="{{ tiyuyunguan_item.title }}">
                                <# } else if(tiyuyunguan_item.bg){ #>
                                    <img src="{{pagefactory_base +  tiyuyunguan_item.bg }}" alt="{{ tiyuyunguan_item.title }}">
                                <# } #>
                                </div>
                                <div class="lg-hidden">
                                  <# if(tiyuyunguan_item.wapbg && tiyuyunguan_item.wapbg.indexOf("http://") == -1 && tiyuyunguan_item.wapbg.indexOf("https://") == -1){ #>
                                    <img src=\'{{ pagefactory_base + tiyuyunguan_item.wapbg }}\' alt="{{ tiyuyunguan_item.title }}">
                                <# } else if(tiyuyunguan_item.wapbg){ #>
                                    <img src="{{pagefactory_base + tiyuyunguan_item.wapbg }}" alt="{{ tiyuyunguan_item.title }}">
                                <# } #>
                               </div>
                              </div>
                             <# }); #>
                              ';

        $output .= '</div>';
        $output .= '<div class="swiper-slide-text-scroll">';

        $output .= ' <# _.each(data.jw_tiyuyunguan_item, function (tiyuyunguan_item2, key2){ #>
                         <#
                            var classNames2 = (key2 == 0) ? "active" : "";
                        #>
                       <div class="swiper-slide-text-each {{ classNames2 }}">
                                    <div class="swiper-slide-text-wrap">
                                      <div class="swiper-slide-text">
                                        <h2 class="slide-title">{{ tiyuyunguan_item2.title }}</h2>
                                        <div class="slide-descrition">{{ tiyuyunguan_item2.content }}</div>
                                        <a href="{{ tiyuyunguan_item2.button_url }}" class="radiu-button">查看案例</a>
                                      </div>
                                    </div>
                                   </div>
                             <# }); #>
                               ';

        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="work-buttons">';
        $output .= '<div class="work-button">';
        $output .= '<div class="count-tab">';
        $output .= ' <# _.each(data.jw_tiyuyunguan_item, function (tiyuyunguan_item3, key3){ #>
                         <#
                            var classNames3 = (key3 == 0) ? "active" : "";
                        #>
                               <div class="each-tab  {{ classNames3 }}">
                                <div class="tab">
                                <strong class="tab-key">{{ tiyuyunguan_item3.title }}</strong>
                                   <span class="dot"></span>
                              </div>
                              </div>
                             <# }); #>
                               ';

        $output .= '</div>';
        $output .= '<div class="prograss-bar">';
        $output .= ' <a class="prev iconfont"></a><a class="next iconfont"></a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= "
            <script>

            (function(jQuery){
                //index-service
                jQuery.fn.serviceScroll = function() {
                    return this.each(function () {
                        var jQuerythis = jQuery(this),
                            wslide = jQuerythis.find('.swiper-slide-image-wrap .swiper-slide-image'),
                            wslide1 = jQuerythis.find(.swiper-slide-image-wrap .swiper-slide-image:first-child'),
                            nslide = jQuerythis.find('swiper-slide-text-scroll .swiper-slide-text-each'),
                            dot = jQuerythis.find('.work-buttons .work-button .each-tab'),
                            p = jQuerythis.find('.work-buttons .work-button a.prev'),
                            l = jQuerythis.find('.work-buttons .work-button a.next'),
                            wlengh = wslide.length- 1,
                            wi = 0,
                            sets = null;
                        wslide1.addClass('active').siblings().removeClass('active');

                        function sides() {
                            wslide.eq(wi).addClass('active').siblings().removeClass('active');
                            nslide.eq(wi).addClass('active').siblings().removeClass('active');
                            dot.eq(wi).addClass('active').siblings().removeClass('active');
                        }
                        p.click(function () {
                            clearInterval(sets);
                            wi--;
                            wi = wi < 0 ? wlengh : wi;
                            sides();
                        });
                        l.click(function () {

                            wi++;
                            wi = wi > wlengh ? 0 : wi;
                            sides();
                        });
                        dot.click(function () {
                            jQuerythis.find('.swiper-slide-image-wrap').stop();
                            jQuerythis.find('.swiper-slide-text-scroll').stop();
                            this_ = jQuery(this).index();
                            wi = this_;
                            wslide.eq(wi).addClass('active').siblings().removeClass('active');
                            nslide.eq(wi).addClass('active').siblings().removeClass('active');
                            dot.eq(wi).addClass('active').siblings().removeClass('active')
                        });
                        sets = setInterval(function(){l.click()}, 6000);
                    })
                }
                jQuery('.index-service').serviceScroll();

            })(jQuery);
            </script>
            ";
        return $output;
    }
}
