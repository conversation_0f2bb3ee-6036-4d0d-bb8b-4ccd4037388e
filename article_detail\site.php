<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonArticle_detail extends JwpagefactoryAddons
{

    public function render()
    {
        $app = JFactory::getApplication();
        $input = $app->input;

        $article_id = $input->get('detail');

        $article = JwPageFactoryBase::getArticleById($article_id);
        $output = '';
        $output .= '<div class="item-page">';
        $output .= '<div class="page-header" style="text-align: center">';
        $output .= '<h1>'.$article->title.'</h1>';
        $output .= '</div>';
        $output .= '<div itemprop="articleBody">';
        $output .=  $article->fulltext;
        $output .= '</div>';
        $output .= '</div>';
        return $output;
    }

    public function css()
    {

        return '';
    }


    public static function getTemplate()
    {
        return '<p style="display: block;width: 500px;height: 300px;">编辑模式下不显示文章详情，本段文字用于占位</p>';
    }
}
