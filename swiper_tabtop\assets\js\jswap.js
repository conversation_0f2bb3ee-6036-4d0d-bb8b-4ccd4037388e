// JavaScript Document$(function() {
    var wow = new WOW({ boxClass: 'wow', animateClass: 'animated', offset: 0, mobile: true, live: true });
    wow.init();
    dur('.wow', '2');
    /*hd1*/
    if (!$('.t-lb-a').length) {
        if ($(window).scrollTop() > $(window).height() / 3 * 2) {
            $('.left1-a1,.right1-a1,.right1-c1').fadeIn(500);
        } else {
            $('.left1-a1,.right1-a1,.right1-c1').fadeOut(500);
        }
    }
    $('.right1-a1,.right1-c1').hide();
    $(window).scroll(function() {
        $('.right1-a1,.right1-c1').css("opacity", "1");
        if (!$('.t-lb-a').length) {
            if ($(window).scrollTop() > $(window).height() / 3 * 2) {
                $('.left1-a1,.right1-a1,.right1-c1').fadeIn(500);
            } else {
                $('.left1-a1,.right1-a1,.right1-c1').fadeOut(500);
            }
        }
        if ($(window).scrollTop() > 0) {
            $('.hd1-a1').addClass('on1');
        } else {
            $('.hd1-a1').removeClass('on1');
        }
    });
    $('.hd1-a3').click(function() {
        $('.search1-a1').addClass('on1');
    });
    $('.search1-a10').click(function() {
        $('.search1-a1').removeClass('on1');
    });
    // $('.hd1-a5').mouseenter(function(){
    // 	if($(this).index()!=0){
    // 		$('.menu1-a1').addClass('on1');
    // 	}
    // });
    $('.hd1-a11').click(function() {
        $('.menu1-a1').addClass('on1');
        $('.menu1-a1').css('z-index', '107')
    });
    $('.menu1-a11').click(function() {
        $('.menu1-a1').removeClass('on1');
    });
    $('.yyp_nav .a2 span:first-child').click(function() {
        $('.search1-a1').addClass('on1');
    });
    $('.yyp_eject_nav .a1 em').click(function() {
        $('.yyp_eject_nav').slideUp(500);
    });
    $('.yyp_nav .a2 span:last-child').click(function() {
        $('.yyp_eject_nav').slideDown(500);
    });
    $('.yyp_eject_nav .a2 .b1 dl dd a').click(function() {
        $('.yyp_eject_nav').slideUp(500);
    });
    // $('.hd1-a66').mouseenter(function(){
    // 	$(this).find('.hd1-a68').slideDown(500);
    // });
    // $('.hd1-a66').mouseleave(function(){
    // 	$(this).find('.hd1-a68').slideUp(500);
    // });
    $('.hd1-a66').hover(function() {
        $('.hd1-a1').addClass('on2');
        $(this).find('.header-dropdown-market').stop(false, false).slideDown(500);
        $('.pc_nav_zz').addClass('pc_nav_on1');
        $('.pc_nav_zz').removeClass('pc_nav_on1s');
        $(this).find('.header-dropdown-market').css('z-index', '107');
        $('.pc_nav_zz').css('z-index', '107');
    }, function() {
        $('.hd1-a1').removeClass('on2');
        $(this).find('.header-dropdown-market').stop(false, false).slideUp(10);
        $('.pc_nav_zz').removeClass('pc_nav_on2');
        ;
        $(this).find('.header-dropdown-market').css('z-index', '99');
        $('.pc_nav_zz').css('z-index', '99')
    });
    /*ft1*/
    del(3, 0.1, 1, '.ft1-b3');
    /*right1*/
    // setTimeout(() => {
    //     $('.right1-a1,.right1-c1').css('z-index',6);
    //     $('.right1-a1,.right1-c1').css('opacity',1);
    // },2000)
    $('.top1').click(function() {
        $('html,body').animate({ 'scrollTop': 0 }, 800);
    });
    $('.right1-c2').click(function() {
        if ($(window).width() < 1024) {
            if ($('.right1-c1').hasClass('on1')) {
                $('.right1-c1').removeClass('on1');
            } else {
                $('.right1-c1').addClass('on1');
            }
        }
    });
    $('.right1-c1').mouseleave(function() {
        if ($(window).width() < 1024) {
            $('.right1-c1').removeClass('on1');
        }
    });
    /*ind1*/
    /*console.log(navigator.userAgent.indexOf('Chrome') > -1)*/
    if ($(window).width() > 1023) {
        if ($('.ind111').length) {
            // swiper1('ind111','ind112','ind113',5000,800);
            var ind111idx1 = 0;
            var ind111idx2 = 0;
            $('.ind111 .swiper-slide').eq(0).show();
            $('#video111').attr('muted', true);
            $('#video111')[0].preload = 'auto';
            $('#video111')[0].currentTime = 0;
            setTimeout(() => {
                $('.ind111').click();
                $('.ind111').keyup();
            }, 1000)
            videotime111 = setInterval(() => {
                if ($('#video111')[0].played.length) {
                    clearInterval(videotime111);
                } else {
                    $('#video111')[0].play();
                }
            }, 100)
            ind111time111 = setInterval(function() {
                if (ind111idx1 == 0) {
                    ind111idx2++;
                    if (ind111idx2 >= 350) {
                        $('#video111')[0].pause();
                        ind111idx2 = 0;
                        ind111idx1++;
                        $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                    }
                } else {
                    ind111idx2++;
                    if (ind111idx2 >= 50) {
                        ind111idx2 = 0;
                        ind111idx1++;
                        if (ind111idx1 >= $('.ind111 .swiper-slide').length) {
                            ind111idx1 = 0;
                            $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                            $('#video111')[0].currentTime = 0;
                            $('#video111')[0].play();
                        } else {
                            $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                        }
                    }
                }
            }, 100);
            $('.ind112').click(function() {
                clearInterval(ind111time111);
                if (ind111idx1 == 0) {
                    $('#video111')[0].pause();
                    ind111idx1 = $('.ind111 .swiper-slide').length - 1;
                    ind111idx2 = 0;
                    $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                } else {
                    ind111idx1--;
                    ind111idx2 =
                        $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                    if (ind111idx1 == 0) {
                        $('#video111')[0].currentTime = 0;
                        $('#video111')[0].play();
                    }
                }
                ind111time111 = setInterval(function() {
                    if (ind111idx1 == 0) {
                        ind111idx2++;
                        if (ind111idx2 >= 350) {
                            $('#video111')[0].pause();
                            ind111idx2 = 0;
                            ind111idx1++;
                            $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                        }
                    } else {
                        ind111idx2++;
                        if (ind111idx2 >= 50) {
                            ind111idx2 = 0;
                            ind111idx1++;
                            if (ind111idx1 >= $('.ind111 .swiper-slide').length) {
                                ind111idx1 = 0;
                                $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                                $('#video111')[0].currentTime = 0;
                                $('#video111')[0].play();
                            } else {
                                $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                            }
                        }
                    }
                }, 100);
            });
            $('.ind113').click(function() {
                clearInterval(ind111time111);
                if (ind111idx1 == $('.ind111 .swiper-slide').length - 1) {
                    ind111idx1 = 0;
                    ind111idx2 = 0;
                    $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                    $('#video111')[0].currentTime = 0;
                    $('#video111')[0].play();
                } else {
                    ind111idx1++;
                    ind111idx2 = 0;
                    $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                    if (ind111idx1 == 0) {
                        $('#video111')[0].pause();
                    }
                }
                ind111time111 = setInterval(function() {
                    if (ind111idx1 == 0) {
                        ind111idx2++;
                        if (ind111idx2 >= 350) {
                            $('#video111')[0].pause();
                            ind111idx2 = 0;
                            ind111idx1++;
                            $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                        }
                    } else {
                        ind111idx2++;
                        if (ind111idx2 >= 50) {
                            ind111idx2 = 0;
                            ind111idx1++;
                            if (ind111idx1 >= $('.ind111 .swiper-slide').length) {
                                ind111idx1 = 0;
                                $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                                $('#video111')[0].currentTime = 0;
                                $('#video111')[0].play();
                            } else {
                                $('.ind111 .swiper-slide').fadeOut(500).eq(ind111idx1).fadeIn(500);
                            }
                        }
                    }
                }, 100);
            });
        }
    }
    if($(window).width()<1024){
        if($('.p-index .head').length){
            var mobileindex1 = 0;
            $('.p-index .head').eq(mobileindex1).show();
            mobilebanner1(mobileindex1);
        }
    }
    /*ind2*/
    if ($('.ind2-a3').length) {
        for (var i = 0; i < $('.ind2-a3').length; i++) {
            for (var j = 0; j < $('.ind2-a3').eq(i).find('.ind2-a4').html().length; j++) {
                $('.ind2-a3').eq(i).find('.ind2-a5').append('<span class="wow counterDX" data-wow-delay="' + (0.5 + 0.2 * j) + 's">' + $('.ind2-a3').eq(i).find('.ind2-a4').html()[j] + '</span>');
            }
        }
    }
    /*ind4*/
    if ($('.ind4-a3').length) {
        $('.ind4-a3').eq(0).addClass('on1');
        $('.ind4-b1').eq(0).show();
    }
    $('.ind4-a4').mouseenter(function() {
        if (!$(this).parent().hasClass('on1')) {
            $('.ind4-a3').removeClass('on1').eq($(this).parent().index()).addClass('on1');
            $('.ind4-b1').fadeOut(500).eq($(this).parent().index()).fadeIn(500);
        }
    });
    $('.ind4-a5').mouseenter(function() {
        if (!$(this).parent().hasClass('on1')) {
            $('.ind4-a3').removeClass('on1').eq($(this).parent().index()).addClass('on1');
            $('.ind4-b1').fadeOut(500).eq($(this).parent().index()).fadeIn(500);
        }
    });
    /*ind5*/
    if ($('.ind5-a3').length) {
        $('.ind5-a3').eq(0).addClass('on1');
        $('.ind5-b1').eq(0).addClass('on1');
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            $('.ind5-b1').eq(i).find('.swiper-container').addClass('ind5' + (i + 1) + '1');
            $('.ind5-b1').eq(i).find('.swiper-button-prev').addClass('ind5' + (i + 1) + '2');
            $('.ind5-b1').eq(i).find('.swiper-button-next').addClass('ind5' + (i + 1) + '3');
            /*swiper1('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3',5000,500);
            swiper4('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3');*/
            if ($('.ind5-b1').eq(i).find('.swiper-slide').length <= 1) {
                $('.ind5-b1').eq(i).find('.swiper-button-prev').hide();
                $('.ind5-b1').eq(i).find('.swiper-button-next').hide();
            }
        }
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            for (var j = 0; j < $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').length; j++) {
                var el1 = $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').html();
                $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').empty();
                for (var k = 0; k < 12; k++) {
                    $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').append('<div class="ind5-b7"><div class="ind5-b8">' + el1 + '</div></div>');
                }
            }
        }
        for (var i = 0; i < $('.ind5-b1').length; i++) {
            $('.ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(0).addClass('on1');
        }
        var ind5tap1 = true;
        $('.ind5-b1 .swiper-button-next').click(function() {
            if (ind5tap1) {
                ind5tap1 = false;
                var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                var el1 = $(this).parents('.ind5-b1');
                console.log(idx1)
                if (idx1 == leng1 - 1) {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(0).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                } else {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 + 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                }
            }
        });
        $('.ind5-b1 .swiper-button-prev').click(function() {
            if (ind5tap1) {
                ind5tap1 = false;
                var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                var el1 = $(this).parents('.ind5-b1');
                console.log(idx1)
                if (idx1 == 0) {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(leng1 - 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                } else {
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                    $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 - 1).addClass('on1');
                    setTimeout(function() {
                        el1.find('.swiper-slide.on2').removeClass('on2');
                        ind5tap1 = true;
                    }, 600);
                }
            }
        });
    }
    $('.ind5-a3').mouseenter(function() {
        $('.ind5-a3').removeClass('on1');
        $(this).addClass('on1');
        $('.ind5-b1').removeClass('on1');
        $('.ind5-b1').eq($(this).index()).addClass('on1');
    });
    /*ind7*/
    if ($('.ind7-a2').length) {
        var province1 = ['黑龙江', '吉林', '辽宁', '内蒙古', '山西', '河北', '山东', '江苏', '安徽', '河南', '宁夏', '甘肃', '青海', '四川', '西藏', '云南', '海南', '广西', '澳门', '贵州', '重庆', '湖南', '广东', '香港', '台湾', '福建', '江西', '湖北', '陕西', '浙江', '新疆'];
        for (var i = 0; i < province1.length; i++) {
            $('.ind7-a7').append('<div>' + province1[i] + '</div>');
        }
        var company1 = ['哈尔滨', '牡丹江', '鸡西', '双鸭山', '佳木斯', '齐齐哈尔', '大庆', '绥化', '鹤岗', '伊春', '黑河', '长春', '沈阳', '辽阳', '阜新', '盘锦', '营口', '鞍山', '大连', '北京', '天津', '朔州', '大同', '阳泉', '晋中', '忻州', '太原', '吕梁', '临汾', '运城', '晋城', '长治', '青岛', '烟台', '济南', '西安', '成都', '武汉', '上海', '南昌', '福州', '泉州', '厦门', '深圳', '南京', '重庆', '湘潭'];
        for (var i = 0; i < company1.length; i++) {
            $('.ind7-a8').append('<div class="ind7-a9"><div class="ind7-a10">' + company1[i] + '</div><div class="ind7-a11"><div></div></div></div>');
        }
    }
    /*ind8*/
    if ($('.ind8-a4').length) {
        $('.ind8-a4').eq(0).addClass('on1');
        $('.ind8-a6').eq(0).show();
        $('.ind8-a8').eq(0).show();
    }
    $('.ind8-a4').mouseenter(function() {
        if (!$(this).hasClass('on1')) {
            $('.ind8-a4').removeClass('on1').eq($(this).index()).addClass('on1');
            $('.ind8-a6').hide().eq($(this).index()).show();
            $('.ind8-a8').fadeOut(500).eq($(this).index()).fadeIn(500);
        }
    });
    /*ind10*/
    /*if($('.ind10-a1').length){
    	var ind10arr1=[];
    	for(var i=0;i<$('.ind10-a5').length;i++){ind10arr1.push(false);}
    	$('.ind10-a5').mouseenter(function(){
    		if(!ind10arr1[$(this).index()]){
    			ind10arr1[$(this).index()]=true;
    			$(this).addClass('on1');
    		}
    	});
    	$('.ind10-a5').mouseleave(function(){
    		if(ind10arr1[$(this).index()]){
    			var that = $(this)
    			setTimeout(function(){
    				ind10arr1[that.index()]=false;
    				that.removeClass('on1');
    			},500)
    		}
    	});
    }*/
    $(".p-popup-nav-list-a1").click(function() {
        if (!$(this).hasClass('p-on1')) {
            $('.p-popup-nav-list-a1').removeClass('p-on1')
            $(this).addClass('p-on1')
            $('.p-popup-nav-list-a1').next('.p-popup-nav-list-a2').hide()
            $(this).next('.p-popup-nav-list-a2').slideDown(300)
        } else {
            $('.p-popup-nav-list-a1').removeClass('p-on1')
            $('.p-popup-nav-list-a1').next('.p-popup-nav-list-a2').slideUp(300)
        }
    })
    $(window).scroll(function() {
        if ($(window).scrollTop() > 0) {
            $('.nav').addClass('phone-on1');
            $(".p-bai-nav").fadeIn(500);
        } else {
            $('.nav').removeClass('phone-on1');
            $(".p-bai-nav").fadeOut(500);
        }
    });
    $(".p-popup-a1").click(function() {
        $(".p-popup").slideUp(500)
    })
    $(".nav-fr-a2").click(function() {
        $(".p-popup").slideDown(500)
    })
    if ($(".p-index-a4-line").length) {
        $(".p-index-a4-line").eq(0).addClass("on1");
    }
    $(".p-index-a4-list").click(function() {
        $(".p-index-a4-list").removeClass("p-index-a4-list-ac");
        $(this).addClass("p-index-a4-list-ac");
        $('.p-index-a4-line').removeClass('on1').eq($(this).index()).addClass('on1');
        $('.p-index-a4-line').removeClass('on1').eq($(this).index()).addClass('on1');
    })
    if ($('.swiper-container8').length) {
        var swiper8 = new Swiper(".swiper-container8", {
            loop: true,
            speed: 700,
            navigation: {
                nextEl: ".swiper-button-next8",
                prevEl: ".swiper-button-prev8",
            },
            on: {
                init: function() {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function() {
                    swiperAnimate(this);
                }
            }
        });
    }
    if ($('.swiper-container7').length) {
        var swiper7 = new Swiper('.swiper-container7', {
            autoplay: true,
            slidesPerView: 'auto',
            speed: 700,
            spaceBetween: 5,
            centeredSlides: true,
            loop: true,
        });
    }
    if ($('.swiper-container1').length) {
        var swiper1 = new Swiper('.swiper-container1', {
            slidesPerView: 2,
            slidesPerGroup: 1,
            loop: true,
            speed: 600
        });
    }
    if ($('.swiper-container2').length) {
        var swiper2 = new Swiper(".swiper-container2", {
            // effect : 'fade',
            on: {
                init: function() {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function() {
                    swiperAnimate(this);
                }
            }
        });
        var aa = $(".swiper-container2").find(".swiper-slide").length
        $(".in-page-ac").html('01')
        if (aa <= 10) {
            $(".in-page-total").append('0' + aa)
        } else {
            $(".in-page-total").append(aa)
        }
        swiper2.on('slideChangeTransitionEnd', function() {
            var page = swiper2.activeIndex + 1
            if (swiper2.activeIndex + 1 >= 10) {
                $(".in-page-ac").html(page)
            } else {
                $(".in-page-ac").html('0' + page)
            }
        });
    }
    // if($('.swiper-container3').length){
    // 	var swiper3 = new Swiper(".swiper-container3", {
    //         on: {
    //             init: function () {
    //                 swiperAnimateCache(this);
    //                 swiperAnimate(this);
    //             },
    //             slideChangeTransitionEnd: function () {
    //                 swiperAnimate(this);
    //             }
    //         }
    //     });
    //     var bb = $(".swiper-container3").find(".swiper-slide").length
    //     $(".in-page-ac11").html('01')
    //     if (bb <= 10) {
    //         $(".in-page-total11").append('0' + bb)
    //     } else {
    //         $(".in-page-total11").append(bb)
    //     }
    //     $('swiper3').on('slideChangeTransitionEnd', function () {
    //         var page = swiper3.activeIndex + 1
    //         if (swiper3.activeIndex + 1 >= 10) {
    //             $(".in-page-ac11").html(page)
    //         } else {
    //             $(".in-page-ac11").html('0' + page)
    //         }
    //     });
    // }
    if ($('.swiper-container4').length) {
        var swiper4 = new Swiper(".swiper-container4", {
            on: {
                init: function() {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function() {
                    swiperAnimate(this);
                }
            }
        });
        var cc = $(".swiper-container4").find(".swiper-slide").length
        $(".in-page-ac2").html('01')
        if (cc <= 10) {
            $(".in-page-total2").append('0' + cc)
        } else {
            $(".in-page-total2").append(cc)
        }
        swiper4.on('slideChangeTransitionEnd', function() {
            var page = swiper4.activeIndex + 1
            if (swiper4.activeIndex + 1 >= 10) {
                $(".in-page-ac2").html(page)
            } else {
                $(".in-page-ac2").html('0' + page)
            }
        });
    }
    if ($('.swiper-container4').length) {
        var swiper5 = new Swiper(".swiper-container5", {
            on: {
                init: function() {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function() {
                    swiperAnimate(this);
                }
            }
        });
        var dd = $(".swiper-container5").find(".swiper-slide").length
        $(".in-page-ac3").html('01')
        if (dd <= 10) {
            $(".in-page-total3").append('0' + dd)
        } else {
            $(".in-page-total3").append(dd)
        }
        swiper5.on('slideChangeTransitionEnd', function() {
            var page = swiper5.activeIndex + 1
            if (swiper5.activeIndex + 1 >= 10) {
                $(".in-page-ac3").html(page)
            } else {
                $(".in-page-ac3").html('0' + page)
            }
        });
    }
    if ($('.swiper-container6').length) {
        var swiper6 = new Swiper(".swiper-container6", {
            on: {
                init: function() {
                    swiperAnimateCache(this);
                    swiperAnimate(this);
                },
                slideChangeTransitionEnd: function() {
                    swiperAnimate(this);
                }
            }
        });
        var ee = $(".swiper-container6").find(".swiper-slide").length
        $(".in-page-ac4").html('01')
        if (dd <= 10) {
            $(".in-page-total4").append('0' + dd)
        } else {
            $(".in-page-total4").append(dd)
        }
        swiper6.on('slideChangeTransitionEnd', function() {
            var page = swiper6.activeIndex + 1
            if (swiper6.activeIndex + 1 >= 10) {
                $(".in-page-ac4").html(page)
            } else {
                $(".in-page-ac4").html('0' + page)
            }
        });
    };
    $(".p-index-a6-a3").click(function() {
        if (!$(this).hasClass('quest-active')) {
            $('.p-index-a6-a3').removeClass('quest-active')
            $(this).addClass('quest-active')
            $('.p-index-a6-a3').next('.p-index-a6-a4').hide()
            $(this).next('.p-index-a6-a4').slideDown()
        } else {
            $('.p-index-a6-a3').removeClass('quest-active')
            $('.p-index-a6-a3').next('.p-index-a6-a4').slideUp()
        }
    });
    shutup();
    if ($('.pagination li').length) {
        if ($(window).width() < 1024) {
            var a_length = $('.pagination li').length;
            for (var i = $('.pagination li').length - 2; i > 0; i--) {
                if (!$('.pagination li').eq(i).hasClass('active')) {
                    $('.pagination li').eq(i).remove();
                }
            }
            $('.pagination li.active').after('<li><span>/</span></li><li><span>' + (a_length - 2) + '</span></li>');
        }
        // if($('.page1-a1>a').length>9){
        //     if($('.select').index()<5){
        //         if($('.select').index()==2){
        //         	for(var i=2;i<7;i++){$('.page1-a1>a').eq(i).css({'display':'inline-block'});}
        //         }else{
        //             for(var i=0;i<7;i++){$('.page1-a1>a').eq(i).css({'display':'inline-block'});}
        //         }
        //         $('.page1-a1>a').eq($('.page1-a1>a').length-1).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq($('.page1-a1>a').length-2).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq(6).after('<a style="display: inline-block;">...</a>');
        //     }else if($('.select').index()>($('.page1-a1>a').length-6)){
        //         if($('.select').index()==($('.page1-a1>a').length-3)){
        //         	for(var i=0;i<5;i++){$('.page1-a1>a').eq($('.page1-a1>a').length-3-i).css({'display':'inline-block'});}
        //         }else{
        //         	for(var i=0;i<7;i++){$('.page1-a1>a').eq($('.page1-a1>a').length-1-i).css({'display':'inline-block'});}
        //     	}
        //         $('.page1-a1>a').eq(0).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq(1).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq($('.page1-a1>a').length-7).before('<a style="display: inline-block;">...</a>');
        //     }else{
        //         for(var i=-2;i<3;i++){$('.page1-a1>a').eq($('.select').index()+i).css({'display':'inline-block'});}
        //         $('.page1-a1>a').eq(0).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq(1).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq($('.page1-a1>a').length-1).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq($('.page1-a1>a').length-2).css({'display':'inline-block'});
        //         $('.page1-a1>a').eq($('.select').index()+2).after('<a style="display: inline-block;">...</a>');
        //         $('.page1-a1>a').eq($('.select').index()-2).before('<a style="display: inline-block;">...</a>');
        //     }
        // }else{
        //     if($('.select').index()==2){
        //         for(var i=2;i<$('.page1-a1>a').length;i++){$('.page1-a1>a').eq(i).css({'display':'inline-block'});}
        //     }else if($('.select').index()==($('.page1-a1>a').length-3)){
        //         for(var i=0;i<($('.page1-a1>a').length-2);i++){$('.page1-a1>a').eq(i).css({'display':'inline-block'});}
        //     }else{
        //         $('.page1-a1>a').css({'display':'inline-block'});
        //     }
        // }
    }
});
function mobilebanner1(a1){
    a1++;
    if(a1==$('.p-index .head').length){
        a1 = 0;
    }
    setTimeout(function(){
        $('.p-index .head').fadeOut(500).eq(a1).fadeIn(500);
        mobilebanner1(a1);
    },6000);
}
function del(a, b, c, d) {;
    for (var i = 0; i < $(d).length; i++) {
        var t1 = (a * b + b * (c * i)) + 's';
        $(d).eq(i).attr('data-wow-delay', t1);
    }
}
function dur(a1, b1) { $(a1).attr('data-wow-duration', (b1 + 's')); }
function swiper1(a1, a2, a3, t1, t2) {
    var swi1 = '.' + a1;
    var but1 = '.' + a2;
    var but2 = '.' + a3;
    var mySwiper = new Swiper(swi1, {
        loop: true,
        navigation: { prevEl: but1, nextEl: but2, },
        autoplay: { delay: t1, disableOnInteraction: false, },
        speed: t2,
        effect: 'fade',
        fadeEffect: { crossFade: true, },
        on: {
            init: function() {
                swiperAnimateCache(this);
                swiperAnimate(this);
            },
            slideChangeTransitionEnd: function() { swiperAnimate(this); },
        }
    });
}
function swiper2(a1, a2, a3) {
    var swi1 = '.' + a1;
    var but1 = '.' + a2;
    var but2 = '.' + a3;
    var mySwiper = new Swiper(swi1, {
        loop: true,
        navigation: { prevEl: but1, nextEl: but2, },
        speed: 500,
        effect: 'fade',
        fadeEffect: { crossFade: true, },
        on: {
            init: function() {
                swiperAnimateCache(this);
                swiperAnimate(this);
            },
            slideChangeTransitionEnd: function() { swiperAnimate(this); },
        }
    });
}
function swiper3(a1, a2, a3, a4, t1, t2) {
    var swi1 = '.' + a1;
    var pag1 = '.' + a2;
    var but1 = '.' + a3;
    var but2 = '.' + a4;
    var mySwiper = new Swiper(swi1, {
        loop: true,
        pagination: { el: pag1, clickable: true, },
        navigation: { prevEl: but1, nextEl: but2, },
        autoplay: { delay: t1, disableOnInteraction: false, },
        speed: t2,
        effect: 'fade',
        fadeEffect: { crossFade: true, },
        on: {
            init: function() {
                swiperAnimateCache(this);
                swiperAnimate(this);
            },
            slideChangeTransitionEnd: function() { swiperAnimate(this); },
        },
    });
}
function swiper4(a1, a2, a3) {
    var swi1 = '.' + a1;
    var but1 = '.' + a2;
    var but2 = '.' + a3;
    var mySwiper = new Swiper(swi1, {
        loop: true,
        navigation: { prevEl: but1, nextEl: but2, },
        speed: 500,
        on: {
            init: function() {
                swiperAnimateCache(this);
                swiperAnimate(this);
            },
            slideChangeTransitionEnd: function() { swiperAnimate(this); },
        }
    });
}
function swiper5(a1) { var swi1 = '.' + a1; var mySwiper = new Swiper(swi1, { loop: true, speed: 500 }); }
function swiper6(a1, t1, t2) { var swi1 = '.' + a1; var mySwiper = new Swiper(swi1, { loop: true, autoplay: { delay: t1, disableOnInteraction: false, }, speed: t2 }); }
// function shutup() {
//     var img = $("img");
//     img.on("contextmenu", function() { return false; });
//     img.on("dragstart", function() { return false; });
//     $('img').attr('oncontextmenu', 'return false;');
//     $(document).bind("contextmenu copy selectstart", function() {
//         return false;
//     });
//     // window.oncontextmenu=function(){return false;}
//     // window.onkeydown = window.onkeyup = window.onkeypress = function (e) {
//     // 	if(e.keyCode!=122){
//     // 		window.event.returnValue = false;
//     // 		return false;
//     // 	}
//     // }
// }