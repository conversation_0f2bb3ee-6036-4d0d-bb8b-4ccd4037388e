<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonTree_level_mobile extends JwpagefactoryAddons
{


    //数组多层级 递归
//    public function subTree( $data,  $pid = 1, $deep=0)
//    {   //用来存放数据
//        $arr = [];
//        //遍历数据库中的字段
//        foreach ($data as $val) {
//            //判断字段pid相等时放行
//            if ($pid == $val['parent_id']) {
//                //不同的层级
//                $val['deep']=$deep;
//                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
//
//                $val['sub'] = $this->subTree($data,$val['tag_id'],$deep+1);
//                //如果遇到pid==本条id时，将其存入数组
//                $arr[] =$val;
//            }
//        }
//        //返回数组
//        return $arr;
//    }

    public function render()
    {
        $settings = $this->addon->settings;   //获取admin.php中的样式变量
        $company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $layout_id = $_GET['layout_id'] ?? 0;

        //左侧选中文字颜色
        $tab_active_color = (isset($settings->tab_active_color) && $settings->tab_active_color) ? $settings->tab_active_color : '';
        //左侧选背景颜色
        $tab_active_bgcolor = (isset($settings->tab_active_bgcolor) && $settings->tab_active_bgcolor) ? $settings->tab_active_bgcolor : '';
        //左侧列表背景颜色
        $tab_list_bgcolor = (isset($settings->tab_list_bgcolor) && $settings->tab_list_bgcolor) ? $settings->tab_list_bgcolor : '';
        //左侧列表宽度
        $tab_list_width = (isset($settings->tab_list_width) && $settings->tab_list_width) ? $settings->tab_list_width : '120';
        //右侧图片大小
        $right_list_img = (isset($settings->right_list_img) && $settings->right_list_img) ? $settings->right_list_img : '60';
        // 指定文章详情页ID
        $detail_page_id = (isset($settings->detail_page_id)) ? $settings->detail_page_id : 0;

        $output = '';
        $output .= "<style>
			${addon_id} .jwpf-cate-wap {
				display: none;
				width: 100vw;
                width: 100%;
                height: 100%;
                position: fixed;
                left: 0;
                top: 0;
                background-color: #fff;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-left {
				width: ${tab_list_width}px;
				height: 100vh;
				overflow-y: auto;
				background-color: ${tab_list_bgcolor};
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item {
				text-align: center;
				line-height: 48px;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item:hover {
				
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item.active {
				background-color: ${tab_active_bgcolor};
				color: ${tab_active_color};
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right {
				width: calc(100% - ${tab_list_width}px);
				height: 100vh;
				overflow-y: auto;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box {
				padding: 0 30px;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .title {
				line-height: 60px;
				font-weight: bold;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list {
				display: flex;
				flex-wrap: wrap;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a {
				display: block;
				text-decoration: none;
				color: #333;
				width: calc(100% / 3);
				margin-bottom: 20px;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a img {
				width: ${right_list_img}px;
				height: ${right_list_img}px;
				object-fit: cover;
				border-radius: 50%;
				margin: 0 auto;
				display: block;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a:hover {
			    background: none;
			    color: #333;
			}
			${addon_id} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a .name {
				text-align: center;
				margin-top: 20px;
			}
			@media (max-width:768px) {
                ${addon_id} .jwpf-cate-wap {
                    display: flex;
                }
            }</style>";


        /**
         * 插件数据
         */
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;
        $item = JwpagefactoryHelperCategories::GetCategoriesthree('com_goods', $site_id, $detail_page_id, $layout_id, $company_id,0,1);
//        $item = $this->subTree($item);

        $item2 = [];
        $output .= '<div class="jwpf-cate-wap">';
        $arr = [];
        $arr2 = [];
        if ($item) {
            $output .= '	<div class="jwpf-cate-left">';
            foreach ($item as $k => $v) {
                foreach ($v['sub'] as $ky => $vl) {
                    array_push($arr, $vl);
                }
                if ($v['parent_id'] == '1') {
                    if ($k == 0) {
                        $output .= '		<div class="jwpf-cate-item active" data-id="cate-' . $v['tag_id'] . '">' . $v['title'] . '</div>';
                    } else {
                        $output .= '		<div class="jwpf-cate-item " data-id="cate-' . $v['tag_id'] . '">' . $v['title'] . '</div>';
                    }
                }
                array_push($arr,$v['tag_id']);
            }
            $output .= '	</div>';

            $pid = end($arr);
            $output .= '	 <div class="jwpf-cate-right" id="right">';
//            var_dump($arr);
//            exit();
            foreach ($item as $k => $v) {
                if ($k == 0) {
                    $output .= '<div class="list-box" id="cate-' . $v['tag_id'] . '">';

                } else {
                    $output .= '<div class="list-box" id="cate-' . $v['tag_id'] . '" style="display: none">';

                }
                foreach ($v['sub'] as $key2 => $val2) {
                    $output .= '<div class="jwpf-cate-box">';
                    $output .= '<p class="title">' . $val2['title'] . '</p>';
                    if (is_array($val2)) {
//                        foreach ($val2['sub'] as $key => $val) {
//                            array_push($arr2, $val);
//                        }
//                        var_dump($val2['sub']);
                        $output .= '<div class="jwpf-cate-right-list">';
                        foreach ($val2['sub'] as $key3 => $val3) {
                            if ($val3['parent_id'] == $val2['tag_id']) {
                                $output .= '<a href="' . $val3['link'] . '" class="jwpf-cate-right-list-item">';
                                if(!empty($val3['Lv_three_icon'])){
                                    $output .= '<img src="' . $val3['Lv_three_icon'] . '" alt="">';
                                }
                                $output .= '<p class="name">' . $val3['title'] . '</p>';
                                $output .= '</a>';
                            }
                        }
                        $output .= '</div>';
                    }
                    $output .= '</div>';
                }
                $output .= '</div>';
            }
            $output .= '	</div>';
        }
        $output .= '</div>';
        return $output;
    }

    public function js()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;
        $js = 'jQuery(function($){
			$("' . $addon_id . ' .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item").click(function(){
				$(this).addClass("active").siblings().removeClass("active");
				let id = $(this).attr("data-id");
				$("' . $addon_id . ' .jwpf-cate-wap .jwpf-cate-right .list-box").hide()
				$("' . $addon_id . ' #" + id).show();
			});
		})';

        return $js;
    }
    public static function getTemplate()
    {
        $output = '
		<#
		var addonId = "#jwpf-addon-"+data.id;
		//左侧选中文字颜色
		var tab_active_color = (!_.isEmpty(data.tab_active_color) && data.tab_active_color) ? data.tab_active_color : "";
		//左侧选背景颜色
		var tab_active_bgcolor = (!_.isEmpty(data.tab_active_bgcolor) && data.tab_active_bgcolor) ? data.tab_active_bgcolor : "";
		//左侧列表背景颜色
		var tab_list_bgcolor = (!_.isEmpty(data.tab_list_bgcolor) && data.tab_list_bgcolor) ? data.tab_list_bgcolor : "";
		//左侧列表宽度
		var tab_list_width = (!_.isEmpty(data.tab_list_width) && data.tab_list_width) ? data.tab_list_width : "120";
		//自定义右侧分类图标大小
		var right_list_img = (!_.isEmpty(data.right_list_img) && data.right_list_img) ? data.right_list_img : "60";
		
		// 指定文章详情页ID
		var detail_page_id = (!_.isEmpty(data.detail_page_id) && data.detail_page_id) ? data.detail_page_id : "";
		#>
		
		<style>
			{{addonId}} .jwpf-cate-wap {
			    display: none;width: 100%;
                height: 100%;
                position: fixed;
                left: 0;
                top: 0;
                background-color: #fff;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-left {
				width: {{tab_list_width}}px;
				height: 100vh;
				overflow-y: auto;
				background-color: {{tab_list_bgcolor}};
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item {
				text-align: center;
				line-height: 48px;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-left .jwpf-cate-item.active {
				background-color: {{tab_active_bgcolor}};
				color: {{tab_active_color}};
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right {
				width: calc(100% - {{tab_list_width}}px);
				height: 100vh;
				overflow-y: auto;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box {
				padding: 0 30px;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .title {
				line-height: 60px;
				font-weight: bold;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list {
				display: flex;
				flex-wrap: wrap;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a {
				display: block;
				text-decoration: none;
				width: calc(100% / 3);
				margin-bottom: 20px;
				color: #333;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a:hover {
			    background: none;
			    color: #333;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a img {
				width: {{right_list_img}}px;
				height: {{right_list_img}}px;
				object-fit: cover;
				border-radius: 50%;
				margin: 0 auto;
				display: block;
			}
			{{addonId}} .jwpf-cate-wap .jwpf-cate-right .jwpf-cate-box .jwpf-cate-right-list a .name {
				text-align: center;
				margin-top: 20px;
			}
			@media (max-width:768px) {
                {{addonId}} .jwpf-cate-wap {
                    display: flex;
                }
            }
		</style>
		
		
		
		<div class="jwpf-cate-wap">
			<div class="jwpf-cate-left">
				<div class="jwpf-cate-item active" data-id="cate-1">推荐分类</div>
				<div class="jwpf-cate-item" data-id="cate-2">好记产品</div>
				<div class="jwpf-cate-item" data-id="cate-3">广告产品</div>
				<div class="jwpf-cate-item" data-id="cate-4">广告需求</div>
			</div>
			<div class="jwpf-cate-right">
				<div id="cate-1">
					<div class="jwpf-cate-box">
						<p class="title">条幅旗帜</p>
						<div class="jwpf-cate-right-list">
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">条幅</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">旗帜</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">锦旗</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">袖标</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">绶带</p>
							</a>
						</div>
					</div>
					<div class="jwpf-cate-box">
						<p class="title">腰线刻字</p>
						<div class="jwpf-cate-right-list">
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">腰线</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">不干胶字</p>
							</a>
						</div>
					</div>
				</div>
				<div id="cate-2">
					<div class="jwpf-cate-box">
						<p class="title">激光打标</p>
						<div class="jwpf-cate-right-list">
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">条幅</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">旗帜</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">锦旗</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">袖标</p>
							</a>
							<a href="" class="jwpf-cate-right-list-item">
								<img src="https://oss.lcweb01.cn/joomla/20220514/d0db8bf5f99894f370d1473e829e0776.jpg" alt="">
								<p class="name">绶带</p>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div>
           <div style="margin:100px">此位置仅在编辑器中站位使用,不影响预览页面结构</div>
        </div>';
        return $output;
    }
}