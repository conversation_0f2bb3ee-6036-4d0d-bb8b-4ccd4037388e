<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'video_list',
        'title' => '视频列表',
        'desc' => '视频列表布局相关插件',
        'category' => '媒体',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'theme' => array(
                    'type' => 'select',
                    'title' => '选择主题',
                    'values' => array(
                        'theme01' => '布局01',
                        'theme02' => '布局02',
                    ),
                    'std' => 'theme01'
                ),
                //布局01选项组
                'jw_tab_item01' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '投资创新的未来',
                            'title_s' => 'The future of investment innovation',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                        array(
                            'title' => '我们正朝着更加光明的未来努力。',
                            'title_s' => 'We\'re working towards a brighter future',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                        array(
                            'title' => '天空中有更多的空间',
                            'title_s' => 'More Space in the Sky',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                        array(
                            'title' => '把开关翻转到玻璃飞行甲板上',
                            'title_s' => 'Flipping the Switch to a Glass Flight Deck',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '投资创新的未来',
                        ),
                        'title_s' => array(
                            'type' => 'text',
                            'title' => '副标题',
                            'std' => 'The future of investment innovation',
                        ),
                        'cover_img' => array(
                            'type' => 'media',
                            'title' => '视频封面图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg'
                        ),
                        'video' => array(
                            'type' => 'media',
                            'title' => '视频',
                            'format' => 'video',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4'
                        ),
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'part01' => array(
                    'type' => 'separator',
                    'title' => '点击放大效果请到预览查看（设置区块层级大于其他区块层级）',
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'item_num' => array(
                    'type' => 'slider',
                    'title' => '一行展示列数',
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 4,
                        'sm' => 4,
                        'xs' => 3
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'item_mg' => array(
                    'type' => 'slider',
                    'title' => '列间距',
                    'max' => 100,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 20,
                        'sm' => 20,
                        'xs' => 20
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'item_img_height' => array(
                    'type' => 'slider',
                    'title' => '视频封面图高度',
                    'max' => 1000,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 154,
                        'sm' => 120,
                        'xs' => 120
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'item_img_fit' => array(
                    'type' => 'select',
                    'title' => '视频封面图展示方式',
                    'std' => 'cover',
                    'values' => array(
                        'cover' => '占满切割',
                        'contain' => '自适应',
                        'fill' => '占满拉伸',
                    ),
                ),
                'video_icon' => array(
                    'type' => 'media',
                    'title' => '视频播放图标',
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220301/44b419a30024df68e93805d02f470c99.png',
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                'title_icon' => array(
                    'type' => 'media',
                    'title' => '标题右侧箭头图标',
                    'format' => 'image',
                    'std' => 'https://oss.lcweb01.cn/joomla/20220301/ea48c4d318676b06e0c52b359581f5d8.png',
                    'depends' => array(
                        array('theme', '=', 'theme01'),
                    ),
                ),
                //                布局02
                'jw_tab_item02' => array(
                    'title' => '选项组',
                    'std' => array(
                        array(
                            'title' => '手工精选优质茶树尖嫩芽',
                            'title_s' => '嫩度一般嫩度好的茶叶，符合外形要求（“光、扁、平、直”）但是不能仅从茸毛多少来判别嫩度，因各种茶的具体要求不一样，如极好的狮峰龙井是体表无茸毛的',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                        array(
                            'title' => '讲述适当喝茶对身体的保健作用',
                            'title_s' => '绿茶需冷藏保存，将茶叶密封后放入罐子内，放置冰箱冷藏保存；红茶常温密封保存；乌龙茶用铁罐、锡罐等双层盖容器常温储存；白茶需要用纸箱与铝箔袋密封保存；',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                        array(
                            'title' => '品茶论道悟人生,把酒听禅观世态',
                            'title_s' => '人生就像一杯茶。第一口苦,第二口涩,第三口甜。回味一下,甘甜清香。平淡是它的本色,苦涩是它的历程,清甜是它的馈赠，细品人的一生，恰似品茶，看似苦涩，可香味尽在其中。一杯茶，品人生沉浮; 平常心，造万年世界。',
                            'cover_img' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg',
                            'video' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4',
                        ),
                    ),
                    'attr' => array(
                        'title' => array(
                            'type' => 'text',
                            'title' => '标题',
                            'std' => '手工精选优质茶树尖嫩芽',
                        ),
                        'title_s' => array(
                            'type' => 'textarea',
                            'title' => '简介',
                            'std' => '嫩度一般嫩度好的茶叶，符合外形要求（“光、扁、平、直”）但是不能仅从茸毛多少来判别嫩度，因各种茶的具体要求不一样，如极好的狮峰龙井是体表无茸毛的',
                        ),
                        'cover_img' => array(
                            'type' => 'media',
                            'title' => '视频封面图',
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20220111/9eba824bd44a3d17fb98ab25f6d0d20b.jpg'
                        ),
                        'video' => array(
                            'type' => 'media',
                            'title' => '视频',
                            'format' => 'video',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210827/b6347c62f1bea30424e6e2953a902b37.mp4'
                        ),
                        'is_link'=>array(
                            'type'=>'checkbox',
                            'title'=>'是否为该项添加链接',
                            'std'=>0
                        ),
                        'tz_page_type' => array(
                            'type' => 'select',
                            'title' => JText::_('跳转方式'),
                            'desc' => JText::_('跳转方式'),
                            'values' => array(
                                'Internal_pages' => JText::_('内部页面'),
                                'external_links' => JText::_('外部链接'),
                            ),
                            'std' => 'Internal_pages',
                            'depends' => array(
                                array('is_link' , '=',  1),
                            ),
                        ),
                        'detail_page_id' => array(
                            'type' => 'select',
                            'title' => '选择跳转页面',
                            'desc' => '',
                            'depends' => array(
                                array('tz_page_type', '=', 'Internal_pages'),
                                array('is_link' , '=',  1),
                            ),
                            'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        ),
                        'link'=>array(
                            'type'=>'text',
                            'title'=>'跳转链接',
                            'std'=>'',
                            'depends'=>array(
                                array('is_link','=',1),
                                array('tz_page_type','=','external_links'),
                            )
                        )
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                    ),
                ),

                'video_display_item01'=>array(
                    'type'=>'select',
                    'title'=>'展示方式',
                    'desc'=>'展示方式',
                    'values'=>array(
                        'list'=>'列表模式',
                        'swiper'=>'轮播模式',
                    ),
                    'std'=>'list',
                    'depends'=>array(
                        array('theme','=','theme01'),
                    )

                ),

                'video_display_item02'=>array(
                    'type'=>'select',
                    'title'=>'展示方式',
                    'desc'=>'展示方式为轮播模式时轮播标题配置项可忽略',
                    'values'=>array(
                        'swiper'=>'轮播模式',
                        'list'=>'列表模式',
                    ),
                    'std'=>'swiper',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                    )

                ),
                'item_settings_item02'=>array(
                    'title'=>'设置项',
                    'type'=>'buttons',
                    'tabs'=>true,
                    'values'=>array(
                        array(
                            'label'=>'列表',
                            'value'=>'list'
                        ),
                        array(
                            'label'=>'视频',
                            'value'=>'video'
                        ),
                    ),
                    'std'=>'list',
                    'depends'=>array(
                        array('theme','=','theme02'),
                    )
                ),
                'item_settings_item_item02'=>array(
                    'title'=>'展示项设置',
                    'type'=>'buttons',
                    'tabs'=>true,
                    'values'=>array(
                        array(
                            'label'=>'列表项',
                            'value'=>'middle'
                        ),
                        array(
                            'label'=>'标题和内容',
                            'value'=>'left'
                        ),
                        array(
                            'label'=>'轮播标题',
                            'value'=>'right'
                        ),
                    ),
                    'std'=>'middle',
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                    ),
                ),
                'video_icon_item02' => array(
                    'type' => 'icon',
                    'title' => '视频播放图标',
                    'std' => 'fa-play',
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                    ),
                ),
                'video_icon_size_item02' => array(
                    'type' => 'slider',
                    'title' => '视频播放图标大小',
                    'std' => 30,
                    'max'=>50,
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                    ),
                ),
                'item_width_item02'=>array(
                    'title'=>'视频占位元素宽度（占屏幕宽度的百分比，移动端无效）',
                    'type'=>'slider',
                    'std' => array(
                        'md' => 28,
                        'sm' => 28,
                        'xs' => 80
                    ),
                    'max'=>100,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                    'responsive'=>true
                ),
                'item_num_site02' => array(
                    'type' => 'slider',
                    'title' => '一行展示列数',
                    'max' => 10,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 2,
                        'sm' => 2,
                        'xs' => 1
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('video_display_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                    ),
                ),
                'item_mg_site02' => array(
                    'type' => 'slider',
                    'title' => '列左右间距',
                    'max' => 500,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 275,
                        'sm' => 100,
                        'xs' => 0
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('video_display_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                    ),
                ),
                'item_mg_bottom_site02' => array(
                    'type' => 'slider',
                    'title' => '列上下间距',
                    'max' => 500,
                    'min' => 0,
                    'responsive' => true,
                    'std' => array(
                        'md' => 110,
                        'sm' => 90,
                        'xs' => 50
                    ),
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('video_display_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                    ),
                ),
                'item_bottom_item02'=>array(
                    'title'=>'移动端视频占位元素下边距',
                    'type'=>'slider',
                    'std' => 50,
                    'max'=>1000,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                ),
                'item_border_width_item02'=>array(
                    'title'=>'视频占位元素边框宽度（占屏幕宽度的百分比）',
                    'type'=>'slider',
                    'std' => array('md'=>4,'sm'=>4,'xs'=>9),
                    'max'=>100,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                    'responsive'=>true
                ),
                'item_border_color_item02'=>array(
                    'title'=>'视频占位元素边框颜色',
                    'type'=>'color',
                    'std' => '#fff',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                ),
                'item_left_show_item02'=>array(
                    'title'=>'是否展示视频标题和内容（列表模式和移动端无效）',
                    'desc'=>'列表模式和移动端无效',
                    'type'=>'checkbox',
                    'std'=>1,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                    ),
                ),
                'item_intro_width_item02'=>array(
                    'title'=>'视频文字内容宽度（单位：%，移动端无效）',
                    'type'=>'slider',
                    'std' => array('md'=>32,'sm'=>32,'xs'=>100),
                    'max'=>100,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                    'responsive'=>true
                ),
                'item_intro_height_item02'=>array(
                    'title'=>'视频文字内容高度',
                    'type'=>'slider',
                    'std' => 166,
                    'max'=>1000,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                ),
                'item_intro_top_item02'=>array(
                    'title'=>'视频标题上边距',
                    'type'=>'slider',
                    'std' => 60,
                    'max'=>1000,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                        array('video_display_item02', '=', 'list'),
                    ),
                    'responsive'=>true
                ),
                'item_intro_title_font_size_item02'=>array(
                    'title'=>'视频标题字号',
                    'type'=>'slider',
                    'std' => array('md'=>24,'sm'=>24,'xs'=>13),
                    'max'=>50,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                    ),
                    'responsive'=>true
                ),
                'item_intro_title_color_item02'=>array(
                    'title'=>'视频标题字体颜色',
                    'type'=>'color',
                    'std' => '#404040',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                    )
                ),
                'item_intro_title_hover_color_item02'=>array(
                    'title'=>'鼠标移入视频标题字体颜色',
                    'type'=>'color',
                    'std' => '#e6ab43',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                        array('video_display_item02', '=', 'list'),
                    )
                ),
                'item_intro_intro_item02'=>array(
                    'title'=>'视频简介上边距',
                    'type'=>'slider',
                    'std' => array('md'=>41,'sm'=>41,'xs'=>20),
                    'max'=>100,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                    ),
                    'responsive'=>true
                ),
                'item_intro_intro_font_size_item02'=>array(
                    'title'=>'视频简介字号',
                    'type'=>'slider',
                    'std' => array('md'=>16,'sm'=>16,'xs'=>13),
                    'max'=>50,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                    ),
                    'responsive'=>true
                ),
                'item_intro_intro_color_item02'=>array(
                    'title'=>'视频简介字体颜色',
                    'type'=>'color',
                    'std' => 'grey',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'left'),
                        array('item_left_show_item02', '=', 1),
                    )
                ),
                'item_page_dot_size_item02'=>array(
                    'title'=>'移动端分页大小',
                    'type'=>'slider',
                    'std' => 6,
                    'max' => 20,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_page_dot_bg_item02'=>array(
                    'title'=>'分页背景色',
                    'type'=>'color',
                    'std' => '#d9d9d9',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_page_dot_border_item02'=>array(
                    'title'=>'选中分页边框色',
                    'type'=>'color',
                    'std' => '#e6ab43',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'middle'),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_show_item02'=>array(
                    'title'=>'是否展示右侧内容（列表模式和移动端无效）',
                    'type'=>'checkbox',
                    'desc'=>'列表模式和移动端无效',
                    'std'=>1,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('video_display_item02', '=', 'swiper'),
                    ),
                ),
                'item_right_right_item02'=>array(
                    'title'=>'右侧内容右边距（%）',
                    'type'=>'slider',
                    'std' => 10,
                    'max'=>100,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_title_margin_item02'=>array(
                    'title'=>'右侧内容间距',
                    'type'=>'margin',
                    'std' => '0 0 0 28px',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_letter_spacing_item02'=>array(
                    'title'=>'右侧内容字间距',
                    'type'=>'slider',
                    'std' => 4,
                    'max' => 10,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_font_size_item02'=>array(
                    'title'=>'右侧内容字号',
                    'type'=>'slider',
                    'std' => 16,
                    'max' => 50,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_line_height_item02'=>array(
                    'title'=>'右侧内容行高',
                    'type'=>'slider',
                    'std' => 2,
                    'max' => 10,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_color_item02'=>array(
                    'title'=>'右侧正常状态字体颜色',
                    'type'=>'color',
                    'std' => '#737373',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_right_active_color_item02'=>array(
                    'title'=>'右侧选中状态字体颜色和线条颜色',
                    'type'=>'color',
                    'std' => '#e6ab43',
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'list'),
                        array('item_settings_item_item02', '=', 'right'),
                        array('item_right_show_item02', '=', 1),
                        array('video_display_item02', '=', 'swiper'),
                    )
                ),
                'item_video_title_font_size_item02'=>array(
                    'title'=>'视频标题字号',
                    'type'=>'slider',
                    'std' => 16,
                    'max'=>50,
                    'depends'=>array(
                        array('theme','=','theme02'),
                        array('item_settings_item02', '=', 'video'),
                    )
                ),
                'item_video_title_color_item02'=>array(
                    'title'=>'视频标题字体颜色',
                    'type'=>'color',
                    'std' => '#2e2e2e',
                    'depends'=>array(
                        array(
                            'theme','=','theme02'
                        ),
                        array('item_settings_item02', '=', 'video'),
                    )
                ),
                'item_video_intro_font_size_item02'=>array(
                    'title'=>'视频简介字号',
                    'type'=>'slider',
                    'std' => 13,
                    'max'=>50,
                    'depends'=>array(
                        array(
                            'theme','=','theme02'
                        ),
                        array('item_settings_item02', '=', 'video'),
                    )
                ),
                'item_video_intro_color_item02'=>array(
                    'title'=>'视频简介字体颜色',
                    'type'=>'color',
                    'std' => '#7d7d7d',
                    'depends'=>array(
                        array(
                            'theme','=','theme02'
                        ),
                        array('item_settings_item02', '=', 'video'),
                    )
                ),
                'item_video_intro_top_item02'=>array(
                    'title'=>'视频简介上边距',
                    'type'=>'slider',
                    'std' => 6,
                    'max'=>50,
                    'depends'=>array(
                        array(
                            'theme','=','theme02'
                        ),
                        array('item_settings_item02', '=', 'video'),
                    )
                ),
                'part02' => array(
                    'type' => 'separator',
                    'title' => '点击放大效果请到预览查看（设置区块层级大于其他区块层级）',
                    'depends' => array(
                        array('theme', '=', 'theme02'),
                    ),
                ),
            ),
        ),
    )
);
