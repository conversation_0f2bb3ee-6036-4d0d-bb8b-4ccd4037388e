<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('Restricted access');

class JwpagefactoryAddonCase_xcx extends JwpagefactoryAddons
{

	public function render()
	{
		$company_id = $_GET['company_id'] ?? 0;
        $site_id = $_GET['site_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $layout_id = $_GET['layout_id'] ?? 0;
		$addon_id = '#jwpf-addon-' . $this->addon->id;
        //指定获得产品列表的pid
        $detail = $_GET['detail'] ?? 0;

        if (!is_numeric($_GET['page'])) {
            $page = 1;
        }
        $cpcatid = $_GET['cpcatid'] ?? 0;
        $page_view_name = isset($_GET['view']);

		$settings = $this->addon->settings;
		$addonId = $this->addon->id;
        $addon_id = '#jwpf-addon-' . $this->addon->id;
		$class = (isset($settings->class) && $settings->class) ? $settings->class : '';
		$title = (isset($settings->title) && $settings->title) ? $settings->title : '';
		$heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
		$limit = (isset($settings->limit) && $settings->limit) ? $settings->limit : 6;
		$goods_catid = (isset($settings->goods_catid) && $settings->goods_catid) ? $settings->goods_catid : 0;
        $tagids = (isset($settings->tagids) && $settings->tagids) ? $settings->tagids : array();
        $show_page = (isset($settings->show_page) && $settings->show_page) ? $settings->show_page : false;
        $show_title = (isset($settings->show_title) && $settings->show_title) ? $settings->show_title : false;
        $ordering = (isset($settings->ordering) && $settings->ordering) ? $settings->ordering : 'latest';

        $page1_fontcolor = (isset($settings->page1_fontcolor)) ? $settings->page1_fontcolor : '#2a68a7';
        $page1_bordercolor = (isset($settings->page1_bordercolor)) ? $settings->page1_bordercolor : '#2a68a7';
        $page1_bgcolor = (isset($settings->page1_bgcolor)) ? $settings->page1_bgcolor : '#ffffff';
        $page1_cur_fontcolor = (isset($settings->page1_cur_fontcolor)) ? $settings->page1_cur_fontcolor : '#2a68a7';
        $page1_cur_bordercolor = (isset($settings->page1_cur_bordercolor)) ? $settings->page1_cur_bordercolor : '#2a68a7';
        $page1_cur_bgcolor = (isset($settings->page1_cur_bgcolor)) ? $settings->page1_cur_bgcolor : '#ffffff';
        $type3fontsize = (isset($settings->type3fontsize)) ? $settings->type3fontsize : '22';
        $type3fontcolor = (isset($settings->type3fontcolor)) ? $settings->type3fontcolor : '#fff';
        $type3bgclolor = (isset($settings->type3bgclolor)) ? $settings->type3bgclolor : 'rgba(0, 0, 0, 0.5)';
        $img_check_2_ani = (isset($settings->img_check_2_ani)) ? $settings->img_check_2_ani : '0';
        $img_check_2_line = (isset($settings->img_check_2_line)) ? $settings->img_check_2_line : '0';

        /* 翻页样式 */
        $page_style_selector = (isset($settings->page_style_selector)) ? $settings->page_style_selector : 'page02';
        $page_style_current_bg_color = (isset($settings->page_style_current_color)) ? $settings->page_style_current_color : '#f00';
        $page_style_font_color = (isset($settings->page_style_font_color)) ? $settings->page_style_font_color : '#000';

		// 手机开启滚动
        $phone_open = (isset($settings->phone_open)) ? $settings->phone_open : '0';

        if ($cpcatid != $goods_catid) {
            $page = 1;
        };

        $article_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/goods.php';
		require_once $article_helper;

      
        $items = JwpagefactoryHelperGoods::getGoodsList($limit, $ordering, $goods_catid, $include_subcat, $post_type, $tagids, $detail_page_id, $page, $company_id, $layout_id, $site_id);
        $items_count = JwpagefactoryHelperGoods::getGoodsCount($ordering, $goods_catid, $include_subcat, $company_id, $site_id);


        // 数据为空
        if (!count($items)) {
            $output = '<p class="alert alert-warning">' . JText::_('COM_JWPAGEFACTORY_NO_ITEMS_FOUNDS') . '</p>';
            return $output;
        }
        // 有数据
        if (count((array)$items)) {

			//Output
			$output = '<div class="jwpf-addon jwpf-addon-text-block nogd">
				<div class="eshop_list3">
				    <ul class="clearfix">';
				    foreach ($items as $key => $item) {
				        $output .='<li>
				            <div class="eshop_s_m1">
				                <div class="eshop_s_img1">';
					                $image = $item->image_thumbnail;
			                        if (isset($image) && $image) {
			                            $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
			                            $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
			                        }

				                $output .='</div>
				                <div class="eshop_s_info1">';
				                 	if(!empty($item->many_img)){
						                $many_img = explode(";",$item->many_img);
						            }else{
						                $many_img ='';
						            }

						            if ($many_img) {
				                    	$output .='<div class="eshop_s_logo">';
				                    	// 左侧logo

				                        	foreach ($many_img as $keys => $log){
				                        		if($keys==0){
								                    $output .= '  
								                        <img src="'.$log.'" alt="案例logo">
								                    ';
				                        		}
							                }
				                    	$output .='</div>';
				                    }

				                    $output .='<div class="eshop_s_n1">
				                        <div class="h2">项目名称：'.mb_substr(strip_tags($item->title), 0, 6, 'UTF-8').'</div>
				                        <div class="h3">应用平台：<span>'.mb_substr(strip_tags($item->label1), 0, 7, 'UTF-8').'</span></div>
				                        <div class="h3">所属行业：<span>'.mb_substr(strip_tags($item->label2), 0, 7, 'UTF-8').'</span></div>
				                    </div>
				                </div>
				            </div>
				            <div class="eshop_s_m2">';
				            	// 二维码
				            	$image1 = $item->qrcode;
		                        if (isset($image1) && $image1) {
		                            $default_placeholder = $image1 == '' ? false : $this->get_image_placeholder($image1);
		                            $output .= '<img class="proImg" src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image1) . '" alt="' . $item->title . '"  loading="lazy">';
		                        }

				                $output .='<p>扫码查看案例</p>
				            </div>
							
				        </li>';
				    }
				    $output .='</ul>
				</div>';
				if ($show_page) {
                    if ($page_style_selector == 'page01') {

                        $all_page = 1;
                        if ($limit) {
                            $all_page = ceil($items_count / $limit);
                        }
                        $output .= '<div class="page_plug">';

                        for ($i = 1; $i <= $all_page; $i++) {
                            if ($page == $i) {
                                $output .= '<a class="curPage">' . $i . '</a>';
                            } else {
                                $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                                $output .= '<a class="page_num" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';
                            }
                        }

                        $output .= '</div>';

                    }


                    if ($page_style_selector == 'page02') {
                        $all_page = 1;
                        if ($limit) {
                            $all_page = ceil($items_count / $limit);
                        }

                        /* 编辑器省略号分页 */
                        $output .= '<div class="zxf_pagediv " id="false_page" style="width:100%;display:flex;align-items: center;justify-content: center;opic">';
                        if ($page != 1) {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                            $output .= '    <a href="' . $url . '&page=' . ($page - 1) . '&cpcatid=' . $goods_catid . '" class="prebtn">上一页</a>';
                        }

                        $output .= ' <div class="page">';
                        for ($i = 1; $i <= $all_page; $i++) {

                            if ($page == $i) {
                                $output .= '<a class="current">' . $i . '</a>';
                            } else {
                                $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                                $output .= '<a class="zxfPagenum" href="' . $url . '&page=' . $i . '&cpcatid=' . $goods_catid . '">' . $i . '</a>';

                            }
                        }
                        $output .= '</div>';
                        // 判断是不是最后一页
                        if ($page != $all_page) {
                            $url = $this->removeqsvar($_SERVER['REQUEST_URI'], ['page', 'zcpcatid', 'cpcatid']);
                            $output .= '    <a href="' . $url . '&page=' . ($page + 1) . '&cpcatid=' . $goods_catid . '" class="nextbtn">下一页</a>';
                        }

                        $output .= '</div>';
                        //下面是假的

                        $output .= '<script>
                       
                        </script>';
                    }
                }
			$output .='</div>';
			
			$output .='
			<div class="eshop_m_list4">
				<ul class="clearfix">';
				foreach ($items as $key => $item) {
					$output .='<li>
						<div class="eshop_m_img6">';
							$image = $item->image_thumbnail;
							if (isset($image) && $image) {
								$default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
								$output .= '<img  src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  loading="lazy">';
							}

							$output .='</div>
						<div class="eshop_m_u3">';
							
							if(!empty($item->many_img)){
								$many_img = explode(";",$item->many_img);
							}else{
								$many_img ='';
							}
							if ($many_img) {
								$output .= '<div class="eshop_m_logo1">';
								foreach ($many_img as $keys => $log){
									if($keys==0){
										$output .= '  
											<img src="'.$log.'" alt="案例logo">
										';
									}
								}
							$output .= '</div>';

							}

							$output .= '
							<div class="eshop_m_info5">
								<h2>'.mb_substr(strip_tags($item->title), 0, 6, 'UTF-8').'</h2>
								<p>应用平台：<span>'.mb_substr(strip_tags($item->label1), 0, 7, 'UTF-8').'</span></p>
								<p>所属行业：<span>'.mb_substr(strip_tags($item->label2), 0, 7, 'UTF-8').'</span></p>
							</div>
						</div>
						<div class="eshop_ewm_mask">';
							// 二维码
							$image1 = $item->qrcode;
							if (isset($image1) && $image1) {
								$default_placeholder = $image1 == '' ? false : $this->get_image_placeholder($image1);
								$output .= '<img src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image1) . '" alt="' . $item->title . '"  loading="lazy">';
							}
							$output .='<p>扫码查看案例</p>
						</div>
					</li>';
				}
					
					$output .='</ul>
			</div>

			<div class="eshop_mask" ></div>';

			$output .= '
				<div class="xc5-a1 clear wow fadeInUp animated animated" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-name: fadeInUp;">';
					foreach ($items as $key => $item) {
						$output .='<div class="xc5-a2">
							<div class="xc5-a21">
								<div class="xc5-a21a i300">';
									$image = $item->image_thumbnail;
			                        if (isset($image) && $image) {
			                            $default_placeholder = $image == '' ? false : $this->get_image_placeholder($image);
			                            $output .= '<img  src="' . ($default_placeholder && $page_view_name != 'form' ? $default_placeholder : $image) . '" alt="' . $item->title . '"  >';
			                        }
									$output .='</div>
								<div class="xc5-a21b"><img src="/static/four_index/img/t_06.jpg"></div>
							</div>
							<div class="xc5-a22">' . $item->title . '</div>
							<div class="xc5-a23">行业类目：'.mb_substr(strip_tags($item->label2), 0, 7, 'UTF-8').'</div>
							<div class="xc5-a24"><a href="https://p.qiao.baidu.com/cps/chat?siteId=17959967&amp;userId=28669567&amp;siteToken=3b79a6442b505f3d55bd31e1451deb0c" target="_blank">点击查看</a></div>
						</div>';
					}
					
				$output .='</div>
			';

			$output .='
			<script type="text/javascript">
				jQuery(function($){
				
					$("'.$addon_id.' .eshop_m_list4 ul li").click(function() {
						$("'.$addon_id.' .eshop_mask").show();
						$(this).find(".eshop_ewm_mask").addClass("show");
					})
					$("'.$addon_id.' .eshop_mask").click(function() {
						$(this).hide();
						$("'.$addon_id.' .eshop_ewm_mask").removeClass("show");
					})
				})
			</script>

			';

		}
		return $output;
	}


	public function css()
	{
		$settings = $this->addon->settings;
		$css = '';

		$img_height = '';
		$img_height_sm = '';
		$img_height_xs = '';

		if($settings->imgs!=0){
			$img_height .= (isset($settings->img_height) && $settings->img_height) ? "height: " . $settings->img_height . "px;" : "225px;";
			$img_height_sm .= (isset($settings->img_height_sm) && $settings->img_height_sm) ? "height: " . $settings->img_height_sm . "px;" : "200px;";
			$img_height_xs .= (isset($settings->img_height_xs) && $settings->img_height_xs) ? "height: " . $settings->img_height_xs . "px;" : "180px;";
		}

		// 项目属性
		$xmzt = '';
		$xmzt_sm = '';
		$xmzt_xs = '';

		$xmzt .= (isset($settings->text_fontsize) && $settings->text_fontsize) ? "font-size: " . $settings->text_fontsize . "px;" : "20px;";
		$xmzt .= (isset($settings->text_color) && $settings->text_color) ? "color: " . $settings->text_color . ";" : "#3c3c50;";

		$xmzt_sm .= (isset($settings->text_fontsize_sm) && $settings->text_fontsize_sm) ? "font-size: " . $settings->text_fontsize_sm . "px;" : "16px;";
		$xmzt_xs .= (isset($settings->text_fontsize_xs) && $settings->text_fontsize_xs) ? "font-size: " . $settings->text_fontsize_xs . "px;" : "16px;";

		// 简介属性
		$wb = '';
		$wb_sm = '';
		$wb_xs = '';

		$wb .= (isset($settings->wbtext_fontsize) && $settings->wbtext_fontsize) ? "font-size: " . $settings->wbtext_fontsize . "px;" : "14px;";
		$wb .= (isset($settings->wbtext_color) && $settings->wbtext_color) ? "color: " . $settings->wbtext_color . ";" : "#3c3c50;";
		$wb .= (isset($settings->wbtext_lineheight) && $settings->wbtext_lineheight) ? "line-height: " . $settings->wbtext_lineheight . "px;" : "30px;";

		$wb_sm .= (isset($settings->wbtext_fontsize_sm) && $settings->wbtext_fontsize_sm) ? "font-size: " . $settings->wbtext_fontsize_sm . "px;" : "14px;";
		$wb_xs .= (isset($settings->wbtext_fontsize_xs) && $settings->wbtext_fontsize_xs) ? "font-size: " . $settings->wbtext_fontsize_xs . "px;" : "12px;";
		$wb_sm .= (isset($settings->wbtext_lineheight_sm) && $settings->wbtext_lineheight_sm) ? "line-height: " . $settings->wbtext_lineheight_sm . "px;" : "25px;";
		$wb_xs .= (isset($settings->wbtext_lineheight_xs) && $settings->wbtext_lineheight_xs) ? "line-height: " . $settings->wbtext_lineheight_xs . "px;" : "25px;";

		// 标签属性
		$bq = '';
		$bq_sm = '';
		$bq_xs = '';

		$bq .= (isset($settings->bqtext_fontsize) && $settings->bqtext_fontsize) ? "font-size: " . $settings->bqtext_fontsize . "px;" : "15px;";
		$bq .= (isset($settings->bqtext_color) && $settings->bqtext_color) ? "color: " . $settings->bqtext_color . ";" : "#fd701b;";
		$bq .= (isset($settings->bqbg_color) && $settings->bqbg_color) ? "background: " . $settings->bqbg_color . ";" : "rgba(234,112,27,.1);";
		$bq .= (isset($settings->border_color) && $settings->border_color) ? "border: " . $settings->border_color . ";" : "rgba(255,75,75,.1);";
		$bq_xs .= (isset($settings->bqtext_color) && $settings->bqtext_color) ? "color: " . $settings->bqtext_color . ";" : "#fd701b;";
		$bq_xs .= (isset($settings->bqbg_color) && $settings->bqbg_color) ? "background: " . $settings->bqbg_color . ";" : "rgba(234,112,27,.1);";
		$bq_sm .= (isset($settings->bqtext_fontsize_sm) && $settings->bqtext_fontsize_sm) ? "font-size: " . $settings->bqtext_fontsize_sm . "px;" : "14px;";
		$bq_xs .= (isset($settings->bqtext_fontsize_xs) && $settings->bqtext_fontsize_xs) ? "font-size: " . $settings->bqtext_fontsize_xs . "px;" : "12px;";


		$cou = '';
		$cou_sm = '';
		$cou .= (isset($settings->columns) && $settings->columns) ? $settings->columns : '3';
		$cou_sm .= (isset($settings->columns_xs) && $settings->columns_xs) ? $settings->columns_xs : '2';

		// 手机开启滚动
        $phone_open = (isset($settings->phone_open)) ? $settings->phone_open : '0';
        $img_tcfs = (isset($settings->img_tcfs)) ? $settings->img_tcfs : 'fill';

        $wbtext_height = (isset($settings->wbtext_height) && $settings->wbtext_height) ? $settings->wbtext_height : "128";
		$wbtext_height_sm = (isset($settings->wbtext_height_sm) && $settings->wbtext_height_sm) ?  $settings->wbtext_height_sm : "120";
		$wbtext_height_xs = (isset($settings->wbtext_height_xs) && $settings->wbtext_height_xs) ?  $settings->wbtext_height_xs : "80";


		$widthbig=100/$cou;
		$width=100/$cou_sm;

		$css .= '
			#jwpf-addon-' . $this->addon->id . ' p{margin:0px;padding:0px;}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_list4{display:none;}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_img6 img {
				width: 100%;
				'.$img_height_xs.'';
				if($img_tcfs){
		        	$css .= 'object-fit: '.$img_tcfs.';';
		        }
				
			$css .= '}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_list4 ul li {
				width:  calc('.$width.'% - 3%);
				float: left;
				margin:0px 1.5% 20px 1.5%;
				background: #fff;
				box-shadow: 0 2px 3px rgba(9, 30, 51, 0.07);
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_list4 ul li:nth-child(2n) {
				margin-right: 0;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_u3 {
				display: flex;
				padding:5px 0px;
				align-items: center;
				height:'.$wbtext_height_xs.'px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_logo1 {
				width: 46px;
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_logo1::before {
				position: absolute;
				content: "";
				width: 0;
				height: 30px;
				border: 1px dashed rgba(210,215,226,.6);
				right: 0;
				top: 50%;
				transform: translateY(-50%);
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_logo1 img {
				height: 30px;
				width: 30px;
				border-radius: 5px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_info5 {
				padding-left: 8px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_info5 h2 {
				margin-bottom:0px;
				'.$xmzt_xs.'
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_info5 p {
				display: flex;
				align-items: center;
				margin-top:2px;
				'.$wb_xs.'
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_m_info5 p span {
				'.$bq_xs.'
				padding:0px 3px;
				border-radius: 5px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_ewm_mask {
				position: fixed;
				left: 50%;
				top: 50%;
				transform: translate(-50%,-50%);
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				z-index: 11;
				display: none;
				width: 100%;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_ewm_mask.show {
				display: flex;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_ewm_mask img {
				height: 230px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_ewm_mask p {
				color: #fff;
				font-size: 16px;
				margin-top: 10px;
			}
			#jwpf-addon-' . $this->addon->id . ' .eshop_mask {
				position: fixed;
				width: 100%;
				height: 100%;
				left: 0;
				top: 0;
				background: rgba(0,0,0,.6);
				z-index: 10;
				display: none;
			}

			#jwpf-addon-' . $this->addon->id . ' eshop_ewm_mask{display:none;}
			#jwpf-addon-' . $this->addon->id . ' ul{
				margin:0;padding:0px;
			}
			#jwpf-addon-' . $this->addon->id . ' li{
		        list-style-type:none;
		    }
			#jwpf-addon-' . $this->addon->id . ' .eshop_list3{width:100%;margin:0 auto;}
		    #jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li {
		        width:  calc('.$widthbig.'% - 3%);
		        float: left;
		        margin:0px 1.5% 40px 1.5%;
		        position: relative;
		        background: #fff;
		        box-shadow: 0px 15px 57px rgba(9, 30, 51, 0.07);
		        cursor: pointer;
		        transition: all .3s;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_img1 {
		    	'.$img_height.'
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_img1 img {
		        width: 100%;
		        height: 100%;';
		        if($img_tcfs){
		        	$css .= 'object-fit: '.$img_tcfs.';';
		        }
		        
		    $css .= '}
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_info1 {
		        display: flex;
		        align-items: center;
		        padding:10px;
		        overflow:hidden;
		        height:'.$wbtext_height.'px;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_logo {
		        width: 30%;
		        display: flex;
		        justify-content: center;
		        position: relative;
		        overflow:hidden;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_logo img {
		        width: 73px;
		        height: 73px;
		        border-radius: 10px;
		    }
		
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h2 {
		        
		        overflow:hidden;
		        font-weight: bold;
		        text-overflow:ellipsis;
		        white-space:nowrap;
		       '.$xmzt.'
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 {
		        
		        max-width:100%;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 {
		        display: flex;
		        align-items: center;
		        margin-top: 8px;
		        '.$wb.'
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 span {
		        display: inline-block;
		        padding: 0 6px;
		        border-radius: 4px;
		        margin-left: 4px;
		        '.$bq.'
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_m2 {
		        position: absolute;
		        width: 100%;
		        height: 100%;
		        left: 0;
		        top: 0;
		        display: flex;
		        background: #fff;
		        justify-content: center;
		        align-items: center;
		        flex-direction: column;
		        transition: all .36s;
		        opacity: 0;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_m2 img {
		        width: 60%;

		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_s_m2 p {
		        color: #3c3c50;
		        font-size: 20px;
		        margin-top: 30px;
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li:hover {
		        transform: translateY(-12px);
		    }
		    #jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li:hover .eshop_s_m2 {
		        opacity: 1;
		    }
			
			#jwpf-addon-' . $this->addon->id . ' .page_plug{
	            width: 90%;
	            margin: 5px auto;
	            text-align: center;
	        }
	        #jwpf-addon-' . $this->addon->id . ' .page_plug a{
	            padding: 3px 8px;
	            border: 1px solid '.$settings->page1_bordercolor.';
	            margin-right: 5px;
	            text-decoration: none;
	            color: '.$settings->page1_fontcolor.';
	            background:'.$settings->page1_bgcolor.';
	        }
	        #jwpf-addon-' . $this->addon->id . ' .page_plug .curPage {
	          border: 1px solid '.$settings->page1_cur_bordercolor.';
	          color: '.$settings->page1_cur_fontcolor.';
	          background:'.$settings->page1_cur_bgcolor.';
	        }
		   	#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-article{
			    position: relative;
			    overflow: hidden;
			}
			#jwpf-addon-' . $this->addon->id . ' .jwpf-addon-article:hover .jwpf-article-info-wrap{
			    bottom: 0px;
			    background:'.$settings->hover_font_color.' !important;
			}
			#jwpf-addon-' . $this->addon->id . ' .introtext_type2{
			  text-overflow: ellipsis;
			  display: -webkit-box;
			  -webkit-box-orient: vertical;
			  -webkit-line-clamp: 1;
			  overflow: hidden;
			}

			/* 省略号翻页样式 */
		 	#jwpf-addon-' . $this->addon->id . ' div.zxf_pagediv{
		    	margin: 0 auto;
		    }
		    #jwpf-addon-' . $this->addon->id . ' div.zxf_pagediv a.current {
			    background: '.$settings->page1_cur_bgcolor.';
			    color: '.$settings->page1_cur_fontcolor.';
			    border: 1px solid '.$settings->page1_cur_bordercolor.';
			    width: 30px;
			    height: 30px;
			    line-height: 30px;
			    display:inline-block;
			    border-radius:3px;
			    text-align:center;

		    }
		    #jwpf-addon-' . $this->addon->id . ' .zxfPagenum {
			    color:'.$settings->page1_fontcolor.';
			    border: 1px solid '.$settings->page1_bordercolor.';
			    width: 30px;
			    height: 30px;
			    line-height: 30px;
			    display:inline-block;
			    border-radius:3px;
			    margin:0 5px;
			    text-align:center;
			    background:'.$settings->page1_bgcolor.'; 
		    }
		    #jwpf-addon-' . $this->addon->id . ' .nextbtn, .prebtn, span.disabled{
		        color:'.$settings->page1_fontcolor.';
		    }
		    #jwpf-addon-' . $this->addon->id . ' div.zxf_pagediv span{
		    	color:'.$settings->page1_fontcolor.';
		    }
			#jwpf-addon-' . $this->addon->id . ' .xc5-a1 {
				display: none;
			}
			@media (min-width: 768px) and (max-width: 991px) {
    			#jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li {
			        width:  calc('.$widthbig.'% - 3%);
			        float: left;
			        margin:0px 1.5% 30px 1.5%;
			        position: relative;
			        background: #fff;
			        box-shadow: 0px 15px 57px rgba(9, 30, 51, 0.07);
			        cursor: pointer;
			        transition: all .3s;
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_img1 {
			    	'.$img_height_sm.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h2 {
			        
			        overflow:hidden;
			        font-weight: bold;
			       '.$xmzt_sm.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 {
			        display: flex;
			        align-items: center;
			        margin-top: 8px;
			        '.$wb_sm.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 span {
			        display: inline-block;
			        padding: 0 6px;
			        border-radius: 4px;
			        margin-left: 4px;
			        '.$bq_sm.'
			    }
			}

			@media (max-width: 767px) {
				#jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li:hover .eshop_s_m2 {
					opacity: 0;
				}
				#jwpf-addon-' . $this->addon->id . ' .eshop_list3{display:none;}
				#jwpf-addon-' . $this->addon->id . ' .eshop_m_list4{display:block;}
				#jwpf-addon-' . $this->addon->id . ' .show{display:block;}
    			#jwpf-addon-' . $this->addon->id . ' .eshop_list3 ul li {
			        width:  calc('.$width.'% - 3%);
			        float: left;
			        margin:0px 1.5% 25px 1.5%;
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_img1 {
			    	'.$img_height_xs.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h2 {
			        
			        overflow:hidden;
			        font-weight: bold;
			       '.$xmzt_xs.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 {
			        display: flex;
			        align-items: center;
			        margin-top: 8px;
			        '.$wb_xs.'
			    }
			    #jwpf-addon-' . $this->addon->id . ' .eshop_s_n1 .h3 span {
			        display: inline-block;
			        padding: 0 6px;
			        border-radius: 4px;
			        margin-left: 4px;
			        '.$bq_xs.'
			    }
			}';
			if($phone_open==1){
				$css .= '
					@media only screen and (max-width: 1023px) {
						#jwpf-addon-' . $this->addon->id . ' .xc5-a1 {
							display: block;
						}
						#jwpf-addon-' . $this->addon->id . ' .nogd{display:none;}
						#jwpf-addon-' . $this->addon->id . ' .i300 {
							overflow: hidden;
						}
						#jwpf-addon-' . $this->addon->id . ' .i300>img {
							width: 100%;
							height: 100%;
						}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a1{width:100%;margin-top: calc(62px * 0.58);overflow: hidden;overflow-x: auto;display: -webkit-box;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a2{width: calc(100% - 18.6px * 2);background: #fff;margin-left: 18.6px;box-shadow: 0 0 6px rgba(0, 0, 0, 0.08);padding-bottom: 31px;position: relative;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a21{width: 100%;height: 242px;position: relative;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a21a{width: 100%;height: 100%;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a21b{width: 100%;height: 100%;position: absolute;top: 0;left: 0;right: 0;bottom: 0;margin: 0;background: rgba(0, 0, 0, 0.7);transform: scale(0);transition: 0.5s;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a21b img{width: 128px;height: 128px;position: absolute;top: calc(50% - 128px/2);left: calc(50% - 128px/2);}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a22{font-size:21px;line-height: 21px;margin-top: 21px;padding-left: 18px;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a23{font-size:21px;line-height: 18px;padding-left: 18px;color: #999999;margin-top: 15px;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a24{display: none;}
						#jwpf-addon-' . $this->addon->id . ' .xc5-a3{width: 173px;height: 55px;line-height: 55px;text-align: center;background: #d9012a;color: #fff;font-size: 18px;margin: 0 auto;margin-top: 37px;border-radius: 62px;}
					}	
				';
			}



		$css .= '';

		return $css;
	}

	public static function getTemplate()
	{
		$output = '
		<#
			var columns = (typeof data.columns !== "undefined" && data.columns) ? data.columns : "3";
			var columns_xs = (typeof data.columns_xs !== "undefined" && data.columns_xs) ? data.columns_xs : "1";
			
	        var width = 100 / columns_xs;
	        var widthbig = 100 / columns;
		#>
		<style type="text/css">

			#jwpf-addon-{{ data.id }} ul{
				margin:0;padding:0px;
			}
			#jwpf-addon-{{ data.id }} li{
		        list-style-type:none
		    }
			#jwpf-addon-{{ data.id }} .eshop_list3{width:100%;margin:0 auto;}
		    #jwpf-addon-{{ data.id }} .eshop_list3 ul li {
		        width:  calc({{widthbig}}% - 3%);
		        float: left;
		        margin:0px 1.5% 40px 1.5%;
		        position: relative;
		        background: #fff;
		        box-shadow: 0px 15px 57px rgba(9, 30, 51, 0.07);
		        cursor: pointer;
		        transition: all .3s;
		    }

		    #jwpf-addon-{{ data.id }} .eshop_s_img1 {
		    	<# if(data.imgs==1){ #>

		    		<# if(_.isObject(data.img_height)){ #>
						height: {{data.img_height.md}}px;
					<# } else { #>
						height: {{data.img_height}}px;
					<# } #>

				<# } #>
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_img1 img {
		        width: 100%;
		        height: 100%;
		        object-fit: cover;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_info1 {
		        display: flex;
		        align-items: center;
		        padding-top:10px;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_logo {
		        width: 30%;
		        display: flex;
		        justify-content: center;
		        position: relative;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_logo img {
		        width: 73px;
		        height: 73px;
		        border-radius: 10px;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h2 {
		        color: {{data.text_color}};
		        overflow:hidden;
		        font-weight: bold;
		        <# if(_.isObject(data.text_fontsize)){ #>
					font-size: {{ data.text_fontsize.md }}px;
				<# } else { #>
					font-size: {{ data.text_fontsize }}px;
				<# } #>
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 {
		        display: flex;
		        align-items: center;
		        color: {{data.wbtext_color}};
		        margin-top: 8px;
		        <# if(_.isObject(data.wbtext_fontsize)){ #>
					font-size: {{ data.wbtext_fontsize.md }}px;
				<# } else { #>
					font-size: {{ data.wbtext_fontsize }}px;
				<# } #>
				<# if(_.isObject(data.wbtext_lineheight)){ #>
					line-height: {{ data.wbtext_lineheight.md }}px;
				<# } else { #>
					line-height: {{ data.wbtext_lineheight }}px;
				<# } #>
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 span {
		        display: inline-block;
		        color:{{data.bqtext_color}};
		        
		        <# if(_.isObject(data.bqtext_fontsize)){ #>
					font-size: {{ data.bqtext_fontsize.md }}px;
				<# } else { #>
					font-size: {{ data.bqtext_fontsize }}px;
				<# } #>

		        padding: 0 6px;
		        <# if(_.isObject(data.wbtext_lineheight)){ #>
					line-height: {{ data.wbtext_lineheight.md }}px;
				<# } else { #>
					line-height: {{ data.wbtext_lineheight }}px;
				<# } #>
		        background: {{data.bqbg_color}};
		        border: 1px solid {{data.border_color}};
		        border-radius: 4px;
		        margin-left: 4px;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_m2 {
		        position: absolute;
		        width: 100%;
		        height: 100%;
		        left: 0;
		        top: -1px;
		        display: flex;
		        background: #fff;
		        justify-content: center;
		        align-items: center;
		        flex-direction: column;
		        transition: all .36s;
		        opacity: 0;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_m2 img {
		        width: 60%;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_s_m2 p {
		        color: #3c3c50;
		        font-size: 20px;
		        margin-top: 30px;
		    }
		    #jwpf-addon-{{ data.id }} .eshop_list3 ul li:hover {
		        transform: translateY(-12px);
		    }
		    #jwpf-addon-{{ data.id }} .eshop_list3 ul li:hover .eshop_s_m2 {
		        opacity: 1;
		    }

		    @media (min-width: 768px) and (max-width: 991px) {
			    #jwpf-addon-{{ data.id }} .eshop_s_img1 {
			    	<# if(data.imgs==1){ #>

			    		<# if(_.isObject(data.img_height)){ #>
							height: {{data.img_height.sm}}px;
						<# } #>

					<# } #>
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_m2 p {
			        color: #3c3c50;
			        font-size: 18px;
			        margin-top: 30px;
			    }
				#jwpf-addon-{{ data.id }} .eshop_list3 ul li {
			        width:  calc({{widthbig}}% - 3%);
			        float: left;
			        margin:0px 1.5% 30px 1.5%;
			        position: relative;
			        background: #fff;
			        box-shadow: 0px 15px 57px rgba(9, 30, 51, 0.07);
			        cursor: pointer;
			        transition: all .3s;
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h2 {
			        <# if(_.isObject(data.text_fontsize)){ #>
						font-size: {{ data.text_fontsize.sm }}px;
					<# } #>
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 {
			       
			        <# if(_.isObject(data.wbtext_fontsize)){ #>
						font-size: {{ data.wbtext_fontsize.sm }}px;
					
					<# } #>
					<# if(_.isObject(data.wbtext_lineheight)){ #>
						line-height: {{ data.wbtext_lineheight.sm }}px;
					
					<# } #>
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 span {
			       
			        <# if(_.isObject(data.bqtext_fontsize)){ #>
						font-size: {{ data.bqtext_fontsize.sm }}px;
					<# } #>

			        <# if(_.isObject(data.wbtext_lineheight)){ #>
						line-height: {{ data.wbtext_lineheight.sm }}px;
					<# } #>
			       
			    }
			}
			@media (max-width: 767px) {
				#jwpf-addon-{{ data.id }} .eshop_s_m2 p {
			        color: #3c3c50;
			        font-size: 16px;
			        margin-top: 30px;
			    }
				#jwpf-addon-{{ data.id }} .eshop_list3 ul li {
			        width:  calc({{width}}% - 3%);
			        float: left;
			        margin:0px 1.5% 25px 1.5%;
			        position: relative;
			        background: #fff;
			        box-shadow: 0px 15px 57px rgba(9, 30, 51, 0.07);
			        cursor: pointer;
			        transition: all .3s;
			    }
				#jwpf-addon-{{ data.id }} .eshop_s_img1 {
			    	<# if(data.imgs==1){ #>

			    		<# if(_.isObject(data.img_height)){ #>
							height: {{data.img_height.xs}}px;
						<# } #>

					<# } #>
			    }

			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h2 {
			        <# if(_.isObject(data.text_fontsize)){ #>
						font-size: {{ data.text_fontsize.xs }}px;
					<# } #>
					height:25px;
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 {
			       
			        <# if(_.isObject(data.wbtext_fontsize)){ #>
						font-size: {{ data.wbtext_fontsize.xs }}px;
					
					<# } #>
					<# if(_.isObject(data.wbtext_lineheight)){ #>
						line-height: {{ data.wbtext_lineheight.xs }}px;
					
					<# } #>
					margin-bottom:5px;
			    }
			    #jwpf-addon-{{ data.id }} .eshop_s_n1 .h3 span {
			       
			        <# if(_.isObject(data.bqtext_fontsize)){ #>
						font-size: {{ data.bqtext_fontsize.xs }}px;
					<# } #>

			        <# if(_.isObject(data.wbtext_lineheight)){ #>
						line-height: {{ data.wbtext_lineheight.xs }}px;
					<# } #>
			       
			    }
			}
		</style>
		<div class="jwpf-addon jwpf-addon-text-block">
			<div class="eshop_list3">
			    <ul class="clearfix">
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/hjw.png">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img11.png"></div>
			                    <div class="eshop_s_n1">
			                        <div class="h2">项目名称：吼鲸网</div>
			                        <div class="h3">应用平台：<span>小程序</span></div>
			                        <div class="h3">所属行业：<span>图书文教</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm1.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/hjw.png">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img11.png"></div>

			                    <div class="eshop_s_n1">

			                        <div class="h2">项目名称：北极珍品汇</div>
			                        <div class="h3">应用平台：<span>APP</span></div>
			                        <div class="h3">所属行业：<span>图书文教</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm1.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/hjw.png">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img11.png"></div>
			                    <div class="eshop_s_n1">
			                        <div class="h2">项目名称：北极珍品汇</div>
			                        <div class="h3">应用平台：<span>APP</span></div>
			                        <div class="h3">所属行业：<span>图书文教</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm1.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img05.jpg">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img14.png"></div>

			                    <div class="eshop_s_n1">
			                        <div class="h2">项目名称：乐淘快装</div>
			                        <div class="h3">应用平台：<span>小程序</span></div>
			                        <div class="h3">所属行业：<span>家装厨具</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm6.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img05.jpg">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img14.png"></div>
			                    <div class="eshop_s_n1">
			                        <div class="h2">项目名称：乐淘快装</div>
			                        <div class="h3">应用平台：<span>小程序</span></div>
			                        <div class="h3">所属行业：<span>家装厨具</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm6.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			        <li>
			            <div class="eshop_s_m1">
			                <div class="eshop_s_img1">
			                    <img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img05.jpg">
			                </div>
			                <div class="eshop_s_info1">
			                    <div class="eshop_s_logo"><img src="components/com_jwpagefactory/addons/case_xcx/assets/images/e_img14.png"></div>
			                    <div class="eshop_s_n1">
			                        <div class="h2">项目名称：乐淘快装</div>
			                        <div class="h3">应用平台：<span>小程序</span></div>
			                        <div class="h3">所属行业：<span>家装厨具</span></div>
			                    </div>
			                </div>
			            </div>
			            <div class="eshop_s_m2">
			                <img src="/components/com_jwpagefactory/addons/case_xcx/assets/images/e_ewm6.png">
			                <p>扫码查看案例</p>
			            </div>
			        </li>
			    </ul>
			</div>
		</div>';
		return $output;
	}
	//去掉url指定参数
    function removeqsvar($url, $var_names)
    {
        foreach ($var_names as $param) {
            $url = preg_replace('/([?&])' . $param . '=[^&]+(&|$)/', '$1', $url);
            $url = preg_replace('/([?&])' . $param . '=/', '$1', $url);
        }
        $url = trim($url, "?");
        $url = trim($url, "#");
        $url = trim($url, "&");
        $url = trim($url, "/");
        return $url;
    }
}
