<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 Joom<PERSON>or<PERSON>
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');

class JwpagefactoryAddonSwiper_tabtop extends JwpagefactoryAddons
{

    public function render()
    {
        $addonId  = '#jwpf-addon-' . $this->addon->id;
        $settings = $this->addon->settings;
        $title_color_pc = (isset($settings->title_color_pc) && $settings->title_color_pc) ? $settings->title_color_pc : '#303030';
        $title_color_sj = (isset($settings->title_color_sj) && $settings->title_color_sj) ? $settings->title_color_sj : '#fff';
        $title_size_pc = (isset($settings->title_size_pc) && $settings->title_size_pc) ? $settings->title_size_pc : 36;
        $desc_color_pc = (isset($settings->desc_color_pc) && $settings->desc_color_pc) ? $settings->desc_color_pc : '#535353';
        $desc_color_sj = (isset($settings->desc_color_sj) && $settings->desc_color_sj) ? $settings->desc_color_sj : '#f0f0f0';
        $desc_size_pc = (isset($settings->desc_size_pc) && $settings->desc_size_pc) ? $settings->desc_size_pc : 24;
        // $output .= '<link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'css/swiper.min.css">';
        // $output .= '<style>';
        // $output .= $addonId . ' .i300 { overflow: hidden;}';
        // $output .= $addonId . ' .ind5-a1{width:100%;position:relative;z-index:1;background:linear-gradient(180deg,#fff,#f6f6f6)}';
        // $output .= $addonId . ' .ind5-a2{width:1560px;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;z-index:1;padding:20px}';
        // $output .= $addonId . ' .ind5-a3{float:left;cursor:pointer}';
        // $output .= $addonId . ' .ind5-a3.on1 .ind5-a5{color: #d90029 !important;}';
        // $output .= $addonId . ' .ind5-a4{width:0;height:5px;background:#d90029;float:left;position:relative;top:6px;margin-right:16px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3:hover .ind5-a4{width:26px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3.on1 .ind5-a4{width:26px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a5{font-size:18px;line-height:18px;color:#333333;font-weight:bold;font-style:italic;float:left;margin:0 24px 0 0;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3:hover .ind5-a5{color:#d90029;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a6{width:1560px;height:690px;margin:0 auto;position:relative}';
        // $output .= $addonId . ' .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:0;z-index:1;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}';
        // $output .= $addonId . ' .ind5-b2{width:100%;height:100%;position:relative}';
        // $output .= $addonId . ' .ind5-b2 img{min-height:100%;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}';
        // $output .= $addonId . ' .ind5-b3{position:absolute;bottom:104px;right:60px}';
        // $output .= $addonId . ' .ind5-b4{font-size:36px;line-height:36px;color:#303030;font-weight:bold;text-align:right;margin-bottom:22px}';
        // $output .= $addonId . ' .ind5-b5{font-size:24px;line-height:30px;color:#535353;text-align:right}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev{width:12px;height:22px;padding:0;position:absolute;bottom:36px;right:100px; background-image:none !important;    left: unset !important; top:90% !important;}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-next{width:12px;height:22px;padding:0;position:absolute;bottom:36px;right:60px; background-image:none !important;    left: unset !important; top:90% !important;}';
        // $output .= $addonId . ' .swiper-button-prev:after, .swiper-container-rtl .swiper-button-next:after{content: "" !important;}';
        // $output .= $addonId . ' .swiper-button-next:after, .swiper-container-rtl .swiper-button-prev:after{content: "" !important;}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}';
        // $output .= $addonId . ' .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}';
        // $output .= $addonId . ' .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}';
        // $output .= $addonId . ' .ind5-b8{width:1200%;height:100%;position:absolute;top:0}';
        // $output .= $addonId . ' .ind5-b7:nth-child(1) .ind5-b8{left:0}';
        // $output .= $addonId . ' .ind5-b7:nth-child(2) .ind5-b8{left:-100%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(3) .ind5-b8{left:-200%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(4) .ind5-b8{left:-300%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(5) .ind5-b8{left:-400%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(6) .ind5-b8{left:-500%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(7) .ind5-b8{left:-600%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(8) .ind5-b8{left:-700%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(9) .ind5-b8{left:-800%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(10) .ind5-b8{left:-900%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(11) .ind5-b8{left:-1000%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(12) .ind5-b8{left:-1100%}';
        // $output .= ' @media only screen and (max-width:1599px) and (min-width:1400px){';
        // $output .= $addonId . ' .ind5-a1{width:100%;position:relative;z-index:1;background:linear-gradient(180deg,#fff,#f6f6f6)}';
        // $output .= $addonId . ' .ind5-a2{width:1360px;margin:0 auto 50px;position:relative}';
        // $output .= $addonId . ' .ind5-a3{float:left;cursor:pointer}';
        // $output .= $addonId . ' .ind5-a4{width:0;height:5px;background:#d90029;float:left;position:relative;top:6px;margin-right:16px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3:hover .ind5-a4{width:26px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3.on1 .ind5-a4{width:26px;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a5{font-size:18px;line-height:18px;color:#333333;font-weight:bold;font-style:italic;float:left;margin:0 24px 0 0;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a3:hover .ind5-a5{color:#d90029;transition:0.8s}';
        // $output .= $addonId . ' .ind5-a6{width:1360px;height:600px;margin:0 auto;position:relative}';
        // $output .= $addonId . ' .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:0;z-index:1;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}';
        // $output .= $addonId . ' .ind5-b2{width:100%;height:100%;position:relative}';
        // $output .= $addonId . ' .ind5-b2 img{min-height:100%;transition:0.8s}';
        // $output .= $addonId . ' .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}';
        // $output .= $addonId . ' .ind5-b3{position:absolute;bottom:104px;right:60px}';
        // $output .= $addonId . ' .ind5-b4{font-size:36px;line-height:36px;color:#303030;font-weight:bold;text-align:right;margin-bottom:22px}';
        // $output .= $addonId . ' .ind5-b5{font-size:24px;line-height:30px;color:#535353;text-align:right}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev{width:32px;height:42px;padding:0;position:absolute;bottom:36px;right:96px}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-next{width:32px;height:42px;padding:0;position:absolute;bottom:36px;right:60px}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}';
        // $output .= $addonId . ' .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}';
        // $output .= $addonId . ' .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}';
        // $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}';
        // $output .= $addonId . ' .ind5-b8{width:1200%;height:100%;position:absolute;top:0}';
        // $output .= $addonId . ' .ind5-b7:nth-child(1) .ind5-b8{left:0}';
        // $output .= $addonId . ' .ind5-b7:nth-child(2) .ind5-b8{left:-100%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(3) .ind5-b8{left:-200%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(4) .ind5-b8{left:-300%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(5) .ind5-b8{left:-400%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(6) .ind5-b8{left:-500%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(7) .ind5-b8{left:-600%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(8) .ind5-b8{left:-700%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(9) .ind5-b8{left:-800%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(10) .ind5-b8{left:-900%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(11) .ind5-b8{left:-1000%}';
        // $output .= $addonId . ' .ind5-b7:nth-child(12) .ind5-b8{left:-1100%}';
        // $output .= $addonId . ' }';
        // $output .= ' @media only screen and (max-width:1023px){';
        // $output .= $addonId . ' .ind5-a1{width:100%;overflow:hidden;padding:0;}';
        // $output .= $addonId . ' .ind5-a1-home{overflow-x:auto;}';
        // $output .= $addonId . ' .ind5-a2{padding-top:.8rem;overflow-x:auto;width:200%;white-space:nowrap;padding-bottom:.5rem;font-size:.32rem}';
        // $output .= $addonId . ' .ind5-a3{display:inline-block;width:160px;color:#333333;font-weight:bolder;text-align:center;display:inline-block}';
        // $output .= $addonId . ' ind5-a4{width:.26rem;height:.05rem;background:#d90029;vertical-align:middle;transition:.3s}';
        // $output .= $addonId . ' .ind5-a3.on1 .ind5-a5{color:#d90029 !important}';
        // $output .= $addonId . ' .ind5-b2 img{min-height:100%;transition:0.8s;width:100%;position:absolute;left:-500px}';
        // $output .= $addonId . ' .ind5-b3 {right:unset !important;left: 8% !important;}';
        // $output .= $addonId . ' .ind5-b4 {color: #fff !important;}';
        // $output .= $addonId . ' .ind5-b5 {color: #fff !important;}';
        // $output .= ' }';
        // $output .= $addonId . '</style>';
        // $output .= ' <div class="ind5-a1">';
        // $output .= '    <div class="ind5-a1-home">';
        // $output .= '            <div class="ind5-a2 clear wow fadeInUp" data-wow-delay="0.3s">';
        // $output .= '                            <div class="ind5-a3 clear">';
        // $output .= '                    <div class="ind5-a4"></div>';
        // $output .= '                    <div class="ind5-a5">高端网站</div>';
        // $output .= '                </div>';
        // $output .= '                            <div class="ind5-a3 clear">';
        // $output .= '                    <div class="ind5-a4"></div>';
        // $output .= '                    <div class="ind5-a5">APP案例</div>';
        // $output .= '                </div>';
        // $output .= '                            <div class="ind5-a3 clear">';
        // $output .= '                    <div class="ind5-a4"></div>';
        // $output .= '                    <div class="ind5-a5">电商案例</div>';
        // $output .= '                </div>';
        // $output .= '                 <div class="ind5-a3 clear">';
        // $output .= '                    <div class="ind5-a4"></div>';
        // $output .= '                    <div class="ind5-a5">小程序案例</div>';
        // $output .= '                </div>';
        // $output .= '            </div>';
        // $output .= '        </div>';
        // $output .= '        <div class="ind5-a6 wow fadeInUp" data-wow-delay="0.5s">';
        // $output .= '                        <div class="ind5-b1">';
        // $output .= '                <div class="swiper-container">';
        // $output .= '                    <div class="swiper-wrapper">';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/8080b2d44ee6cdd4ba0fec5ea231d1ae.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">KC皮草</div>';
        // $output .= '                                    <div class="ind5-b5">高端响应式网站</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/80b3aea7171bf937a96fdea00ce396ab.png"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">敷尔佳Voolga</div>';
        // $output .= '                                    <div class="ind5-b5">高端网站定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                            </div>';
        // $output .= '                </div>';
        // $output .= '                <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a35.png"></div>';
        // $output .= '                <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a36.png"></div>';
        // $output .= '            </div>';
        // $output .= '                        <div class="ind5-b1">';
        // $output .= '                <div class="swiper-container">';
        // $output .= '                    <div class="swiper-wrapper">';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/fc77edb82d51fe7fc2feb3aacda45d55.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">SHEGRIE</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/9f44066630d3fbd7b46a0ebd84788d64.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">东运优宜酷爱车家</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/09aecfcc0436d934fd6fc3deed079e55.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">吉林敖东</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/8ad5e9f86df227f8b2c854fc544ada4b.png"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">九亿直播</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/720a71f0e3ba6b31cb5534d005388341.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">如祭</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/e21f5d9f283ef36caa8d381adfae238c.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">疯天然</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/57b8025fde76d3757470eb1f24cd347c.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">正和</div>';
        // $output .= '                                    <div class="ind5-b5">高端APP定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                            </div>';
        // $output .= '                </div>';
        // $output .= '                <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a35.png"></div>';
        // $output .= '                <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a36.png"></div>';
        // $output .= '            </div>';
        // $output .= '                        <div class="ind5-b1">';
        // $output .= '                <div class="swiper-container">';
        // $output .= '                    <div class="swiper-wrapper">';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/d0d32736c98e80c5c9375aec56a8098b.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">资海E店</div>';
        // $output .= '                                    <div class="ind5-b5">高端电商定制方案</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                            </div>';
        // $output .= '                </div>';
        // $output .= '                <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a35.png"></div>';
        // $output .= '                <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a36.png"></div>';
        // $output .= '            </div>';
        // $output .= '                        <div class="ind5-b1">';
        // $output .= '                <div class="swiper-container">';
        // $output .= '                    <div class="swiper-wrapper">';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/6c63c5a7d394b0e732653f91675e8228.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">艺星整形</div>';
        // $output .= '                                    <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/af9dd28ef61d708bd1134136b40ee6a2.png"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">宝宇雪猪肉</div>';
        // $output .= '                                    <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                                <div class="swiper-slide">';
        // $output .= '                            <div class="ind5-b6">';
        // $output .= '                                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/d8149b7c3592177f38e8362192406f47.jpg"></div>';
        // $output .= '                                <div class="ind5-b3">';
        // $output .= '                                    <div class="ind5-b4">海芸青再生资源回收  </div>';
        // $output .= '                                    <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '                                </div>';
        // $output .= '                            </div>';
        // $output .= '                        </div>';
        // $output .= '                                            </div>';
        // $output .= '                </div>';
        // $output .= '                <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a35.png"></div>';
        // $output .= '                <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'img/a36.png"></div>';
        // $output .= '            </div>';
        // $output .= '                    </div>';
        // $output .= '    </div>';
        // $output .= '   ';
        // $output .= '<script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'js/jquery.min.js"></script>';
        // $output .= '<script src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'js/swiper.min.js"></script> ';

        // $output .= '<script src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/' . 'js/js.js"></script> ';
//        $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/css/swiper.min.css">';
//        $output .= ' <link rel="stylesheet" href="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/css/swiper-bundle.min.css">';
        $output = '<style>';
        $output .= $addonId . ' .i300 {overflow:hidden;}';
        $output .= $addonId . ' .ind5-a1{width:100%;position:relative;z-index:1;background:linear-gradient(180deg,#fff,#f6f6f6)}';
        $output .= $addonId . ' .ind5-a2{width:1560px;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;z-index:1;padding:20px}';
        $output .= $addonId . ' .ind5-a3{float:left;cursor:pointer}';
        $output .= $addonId . ' .ind5-a3.on1 .ind5-a5{color:#d90029 !important}';
        $output .= $addonId . ' .ind5-a4{width:0;height:5px;background:#d90029;float:left;position:relative;top:6px;margin-right:16px;transition:0.8s}';
        $output .= $addonId . ' .ind5-a3:hover .ind5-a4{width:26px;transition:0.8s}';
        $output .= $addonId . ' .ind5-a3.on1 .ind5-a4{width:26px;transition:0.8s}';
        $output .= $addonId . ' .ind5-a5{font-size:18px;line-height:18px;color:#333333;font-weight:bold;font-style:italic;float:left;margin:0 24px 0 0;transition:0.8s}';
        $output .= $addonId . ' .ind5-a3:hover .ind5-a5{color:#d90029;transition:0.8s}';
        $output .= $addonId . ' .ind5-a6{width:1560px;height:690px;margin:0 auto;position:relative}';
        $output .= $addonId . ' .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:0;z-index:1;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}';
        $output .= $addonId . ' .ind5-b2{width:100%;height:100%;position:relative}';
        $output .= $addonId . ' .ind5-b2 img{min-height:100%;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}';
        $output .= $addonId . ' .ind5-b3{position:absolute;bottom:104px;right:60px}';
        $output .= $addonId . ' .ind5-b4{font-size:'.$title_size_pc.'px;line-height:36px;color:'.$title_color_pc.';font-weight:bold;text-align:right;margin-bottom:22px}';
        $output .= $addonId . ' .ind5-b5{font-size:'.$desc_size_pc.'px;line-height:30px;color:'.$desc_color_pc.';text-align:right}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev{width:12px;height:22px;padding:0px;position:absolute;bottom:36px;right:100px;background-image:none !important;left:unset !important;top:92% !important}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-next{width:12px;height:22px;padding:0px;position:absolute;bottom:36px;right:60px;background-image:none !important;left:unset !important;top:92% !important}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}';
        $output .= $addonId . ' .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}';
        $output .= $addonId . ' .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}';
        $output .= $addonId . ' .ind5-b8{width:1200%;height:100%;position:absolute;top:0}';
        $output .= $addonId . ' .ind5-b7:nth-child(1) .ind5-b8{left:0}';
        $output .= $addonId . ' .ind5-b7:nth-child(2) .ind5-b8{left:-100%}';
        $output .= $addonId . ' .ind5-b7:nth-child(3) .ind5-b8{left:-200%}';
        $output .= $addonId . ' .ind5-b7:nth-child(4) .ind5-b8{left:-300%}';
        $output .= $addonId . ' .ind5-b7:nth-child(5) .ind5-b8{left:-400%}';
        $output .= $addonId . ' .ind5-b7:nth-child(6) .ind5-b8{left:-500%}';
        $output .= $addonId . ' .ind5-b7:nth-child(7) .ind5-b8{left:-600%}';
        $output .= $addonId . ' .ind5-b7:nth-child(8) .ind5-b8{left:-700%}';
        $output .= $addonId . ' .ind5-b7:nth-child(9) .ind5-b8{left:-800%}';
        $output .= $addonId . ' .ind5-b7:nth-child(10) .ind5-b8{left:-900%}';
        $output .= $addonId . ' .ind5-b7:nth-child(11) .ind5-b8{left:-1000%}';
        $output .= $addonId . ' .ind5-b7:nth-child(12) .ind5-b8{left:-1100%}';
        $output .= '@media only screen and (min-width:1024px){';
        $output .= $addonId . ' .p-index-a4{ display:none;} ';
        $output .= $addonId . ' .in-page11{ display:none;}  ';
        $output .= $addonId . ' .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {content: "";}';
        $output .= $addonId . ' .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {content: "";}';
        $output .= '}';
        $output .= ' @media only screen and (max-width:1599px) and (min-width:1400px){';
        $output .= $addonId . ' .ind5-a2{width:1360px;margin:0 auto 50px;}';
        $output .= $addonId . ' .ind5-a6{width:1360px;height:600px;margin:0 auto;position:relative}';
        $output .= $addonId . ' .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:0;z-index:1;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}';
        $output .= $addonId . ' .ind5-b2{width:100%;height:100%;position:relative}';
        $output .= $addonId . ' .ind5-b2 img{min-height:100%;transition:0.8s}';
        $output .= $addonId . ' .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}';
        $output .= $addonId . ' .ind5-b3{position:absolute;bottom:104px;right:60px}';
        $output .= $addonId . ' .ind5-b4{font-size:'.$title_size_pc.'px;line-height:36px;color:'.$title_color_pc.';font-weight:bold;text-align:right;margin-bottom:22px}';
        $output .= $addonId . ' .ind5-b5{font-size:'.$desc_size_pc.'px;line-height:30px;color:'.$desc_color_pc.';text-align:right}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev{width:32px;height:42px;padding:0px;position:absolute;bottom:36px;right:96px}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-next{width:32px;height:42px;padding:0px;position:absolute;bottom:36px;right:60px}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}';
        $output .= $addonId . ' .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}';
        $output .= $addonId . ' .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}';
        $output .= $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}';
        $output .= $addonId . ' .ind5-b8{width:1200%;height:100%;position:absolute;top:0}';
        $output .= ' }';
        $output .= '@media only screen and (max-width: 1399px) and (min-width: 1200px) {';
            $output .= $addonId . ' .ind5-a1 {
				width: 100%;
				position: relative;
				z-index: 1;
				background: linear-gradient(180deg, #fff, #f6f6f6);
			}
			' . $addonId . ' .ind5-a2 {
				width: 1160px;
				margin: 0 auto 36px;
				position: relative;
			}
			' . $addonId . ' .ind5-a3 {
				float: left;
				cursor: pointer;
			}
			' . $addonId . ' .ind5-a4 {
				width: 0;
				height: 5px;
				background: #d90029;
				float: left;
				position: relative;
				top: 6px;
				margin-right: 16px;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-a3:hover .ind5-a4 {
				width: 26px;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-a3.on1 .ind5-a4 {
				width: 26px;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-a5 {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
				font-weight: bold;
				font-style: italic;
				float: left;
				margin: 0 24px 0 0;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-a3:hover .ind5-a5 {
				color: #d90029;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-a6 {
				width: 1160px;
				height: 510px;
				margin: 0 auto;
				position: relative;
			}
			' . $addonId . ' .ind5-b1 {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				opacity: 0;
				z-index: 1;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-b1.on1 {
				opacity: 1;
				z-index: 2;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-b1 .swiper-container {
				width: 100% !important;
				height: 100% !important;
				position: relative;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide {
				width: 100% !important;
				height: 100% !important;
				position: relative;
			}
			' . $addonId . ' .ind5-b2 {
				width: 100%;
				height: 100%;
				position: relative;
			}
			' . $addonId . ' .ind5-b2 img {
				min-height: 100%;
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-b1:hover .ind5-b2 img {
				transform: scale(1.08);
				transition: 0.8s;
			}
			' . $addonId . ' .ind5-b3 {
				position: absolute;
				bottom: 84px;
				right: 40px;
			}
			' . $addonId . ' .ind5-b4 {
				font-size: 28px;
				line-height: 28px;
				color: '.$title_color_pc.';
				font-weight: bold;
				/*font-style: italic;*/
				text-align: right;
				margin-bottom: 16px;
			}
			' . $addonId . ' .ind5-b5 {
				font-size: 20px;
				line-height: 24px;
				color: '.$desc_color_pc.';
				text-align: right;
			}
			' . $addonId . ' .ind5-b1 .swiper-button-prev {
				width: 32px;
				height: 42px;
				padding: 10px;
				position: absolute;
				bottom: 28px;
				right: 76px;
			}
			' . $addonId . ' .ind5-b1 .swiper-button-next {
				width: 32px;
				height: 42px;
				padding: 10px;
				position: absolute;
				bottom: 28px;
				right: 40px;
			}
			' . $addonId . ' .ind5-b1 .swiper-button-prev img,
			' . $addonId . ' .ind5-b1 .swiper-button-next img {
				opacity: 0.5;
				transition: 0.5s;
			}
			' . $addonId . ' .ind5-b1 .swiper-button-prev:hover img,
			' . $addonId . ' .ind5-b1 .swiper-button-next:hover img {
				opacity: 1;
				transition: 0.5s;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-wrapper {
				width: 100%;
				height: 100%;
				position: relative;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide {
				width: 100% !important;
				height: 100% !important;
				position: absolute;
				top: 0;
				left: 0;
				z-index: 1;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 {
				z-index: 2;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 {
				z-index: 3;
			}
			' . $addonId . ' .ind5-b6 {
				width: 100%;
				height: 100%;
				position: relative;
				overflow: hidden;
			}
			' . $addonId . ' .ind5-b7 {
				width: calc(100%/12);
				height: 100%;
				position: relative;
				overflow: hidden;
				float: left;
				transition: 0.5s;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7 {
				transform: rotateY(0deg);
				transition: 0.5s;
			}
			' . $addonId . ' .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7 {
				transform: rotateY(90deg);
				transition: 0.5s;
			}
			' . $addonId . ' .ind5-b8 {
				width: 1200%;
				height: 100%;
				position: absolute;
				top: 0;
			}
            ';
        $output .= '}';
        $output .= '@media only screen and (max-width:1199px) and (min-width:1024px){';
            $output .= $addonId . ' .ind5-a2 {
				width: 960px;
				margin: 0 auto 36px;
			}
			' . $addonId . ' .ind5-a6 {
				width: 960px;
				height: 426px;
			}';
        $output .= '}';
        $output .= '@media only screen and (max-width:1023px){';
            $output .= $addonId . ' .i300>img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                filter: brightness(0.75);
            }';
            $output .= $addonId . ' .ind5-a1 {
                display: none;
            }';
            $output .= $addonId . ' .in-page11 {
                display: block;
            }';
            $output .= $addonId . ' .p-index-a4 {
                width: 100%;
                overflow: hidden;
                padding: 0 12px;
            }';
            $output .= $addonId . ' .p-index-a4-tab {
                padding-top: 20px;
                overflow-x: auto;
                width: 100%;
                white-space: nowrap;
                padding-bottom: 30px;
                font-size: 18px;
            }';
            $output .= $addonId . ' .p-index-a4-list {
                display: inline-block;
                width: calc(100% / 3);
                color: #333333;
                font-weight: bolder;
                text-align: center;
            }';
            $output .= $addonId . ' .p-index-a4-list div {
                display: inline-block;
            }';
            $output .= $addonId . ' .p-index-a4-list i {
                display: inline-block;
                width: 0;
                transition: .3s;
            }';
            $output .= $addonId . ' .p-index-a4-list.p-index-a4-list-ac {
                color: #d90029;
            }';
            $output .= $addonId . ' .p-index-a4-list.p-index-a4-list-ac i {
                width: 26px;
                height: 5px;
                background: #d90029;
                vertical-align: middle;
                transition: .3s;
            }';
            $output .= $addonId . ' .p-index-a4-box {
                position: relative;
                height: 148vw;
                width: 100%;
            }';
            $output .= $addonId . ' .p-index-a4-line {
                position: relative;
                height: 100%;
            }';
            $output .= $addonId . ' .p-index-a4-box .swiper-container3 {
                overflow: hidden;
                height: 100%;
            }';
            $output .= $addonId . ' .p-index-a4-box .swiper .swiper-slide .p-index-a4-pos {
                position: absolute;
                width: 100%;
                height: 100%;
                bottom: 4rem;
                left: 0;
            }';
            $output .= $addonId . ' .p-index-a4-pos>div {
                position: absolute;
                width: 100%;
                left: 0;
                bottom: 5.3rem;
                text-align: center;
            }';
            $output .= $addonId . ' .p-index-a4-pos .p-index-a4-t1 {
                font-size: 2.3rem;
                color: '.$title_color_sj.';
                font-weight: bolder;
                margin-bottom: 1.4rem;
                line-height: 1;
            }';
            $output .= $addonId . ' .p-index-a4-pos .p-index-a4-t2 {
                color: '.$desc_color_sj.';
                font-size: 1.32rem;
            }';
            $output .= $addonId . ' .in-page1 {
                position: absolute;
                width: 100%;
                /*top: 30rem;*/
                left: 0;
                z-index: 6;
                color: #fff;
                text-align: center;
                font-size: 1rem;
                bottom: 30px;
            }';
            $output .= $addonId . ' .in-page1 .swiper-pagination-current {
                font-size: 1.5rem;
                font-weight: bold;
            }';
            $output .= $addonId . ' .p-index-a4-box .in-page1 span {
                font-family: "rubik";
            }';
            $output .= $addonId . ' .p-index-a4-box .in-page1 i {
                font-style: normal;
                display: inline-block;
                margin: 0 .1rem;
            }';
            $output .= $addonId . ' .p-index-a4-box .in-page1 .in-page-ac1 {
                font-size: .42rem;
                font-weight: bolder;
            }';
            $output .= $addonId . ' .p-index-a4-box .in-page1 .in-page-total1 {
                font-size: .26rem;
            }';
            $output .= $addonId . ' .p-index-a4-box .p-index-a4-line {
                opacity: 0;
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                z-index: -1;
            }';
            $output .= $addonId . ' .p-index-a4-box .p-index-a4-line.on1 {
                opacity: 1;
                z-index: 0;
            }';
        $output .= '}';
        $output .= '</style>';
        $jw_tab_item_goods_eight = (isset($settings->jw_tab_item_goods_eight) && $settings->jw_tab_item_goods_eight) ? $settings->jw_tab_item_goods_eight : array(
            array(
                'title' => '高端网站',
                'goods_desc' => array(
                    array(
                        'title_list' => 'KC皮草',
                        'intro_list' => '高端响应式网站',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/1f556e21b9c9c99998df67954343337b.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/d7fcbb15832de2d07ca29d8306af5b79.png',

                    ),
                    array(
                        'title_list' => '敷尔佳Voolga',
                        'intro_list' => '高端网站定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/4650350f9b85f3ee99e5ec468cf624a7.png',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/8e634a831b70f978e3d8f1b292d5ff4c.png',

                    ),
                )
            ),
            array(
                'title' => 'APP案例',
                'goods_desc' => array(
                    array(
                        'title_list' => 'SHEGRIE',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/8903d1dff5acdd0ab5bba094abda6154.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/b5e14a4eca5e73bcd12382a5e972190f.png',

                    ),
                    array(
                        'title_list' => '东运优宜酷爱车家',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/e1ca697bae88cdc5484f1be238ef7765.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/76d4b9ab5ffc6f47f1be34dfae4c0221.jpg',

                    ),
                    array(
                        'title_list' => '吉林敖东',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/6a6c0124183a4cb04a0b50eaa85d431f.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/1f2a463fa4ef1aec0f1767cad31cdb29.jpg',

                    ),
                    array(
                        'title_list' => '九亿直播',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/91d8e56c0df14e7dc120f78ad85afc18.png',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c3ffacc9d92bb1bd030bee2e22cedd6e.png',

                    ),
                    array(
                        'title_list' => '如祭',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/ffe836ae195715e86697b2076af83b13.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/0ac894a3ff40cdcbe00bc8512ff3dae0.jpg',

                    ),
                    array(
                        'title_list' => '疯天然',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/92f32f52d4d029f904d7e3caa1928275.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c256a0302ad1171150852d4c007e6a67.jpg',

                    ),
                    array(
                        'title_list' => '正和',
                        'intro_list' => '高端APP定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/26193ebbbc419f808ad1af27a7a32c02.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c218dd3c0ac8fe6fae3f2f1687b1c487.jpg',

                    ),
                ),
            ),
            array(
                'title' => '电商案例',
                'goods_desc' => array(
                    array(
                        'title_list' => '资海E店',
                        'intro_list' => '高端电商定制方案',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/446dade6fd1a6dfe4166bed765affe56.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/c6cb4fead4c5211650783349373a03a1.jpg',

                    ),
                ),
            ),
            array(
                'title' => '小程序案例',
                'goods_desc' => array(
                    array(
                        'title_list' => '艺星整形',
                        'intro_list' => '高端小程序定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/7a1bf1e1edc841ef4a0cee11f9eb12b3.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/597e98baaad7a47bddd890e8a879bac6.jpg',

                    ),
                    array(
                        'title_list' => '宝宇雪猪肉',
                        'intro_list' => '高端小程序定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/3a6ae478d4c61ebd5277a0f7f9a85e92.png',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/08976148d8b3ab3566ed90d1aa560673.png',

                    ),
                    array(
                        'title_list' => '海芸青再生资源回收',
                        'intro_list' => '高端小程序定制',
                        'icon_img' => 'https://oss.lcweb01.cn/joomla/20220701/dd6daa918ccbf10037180451f791065f.jpg',
                        'phone_img' => 'https://oss.lcweb01.cn/joomla/20220701/e3f5719a1dae3b75972d1689fe96a4ef.jpg',

                    ),
                ),
            ),
        );
        


        $output .= '<div class="ind5-a1">';
        $output .= '  <div style="overflow-x: auto;">';
        $output .= '    <div class="ind5-a2 clear wow fadeInUp" data-wow-delay="0.3s">';
        foreach ($jw_tab_item_goods_eight as $k => $v) {
            
            $output .= '      <div class="ind5-a3 clear">';
            $output .= '        <div class="ind5-a4"></div>';
            $output .= '        <div class="ind5-a5">' . $v->title . '</div>';
            $output .= '      </div>';
        }
        // $output .= '      <div class="ind5-a3 clear">';
        // $output .= '        <div class="ind5-a4"></div>';
        // $output .= '        <div class="ind5-a5">APP案例</div>';
        // $output .= '      </div>';
        // $output .= '      <div class="ind5-a3 clear">';
        // $output .= '        <div class="ind5-a4"></div>';
        // $output .= '        <div class="ind5-a5">电商案例</div>';
        // $output .= '      </div>';
        // $output .= '      <div class="ind5-a3 clear">';
        // $output .= '        <div class="ind5-a4"></div>';
        // $output .= '        <div class="ind5-a5">小程序案例</div>';
        // $output .= '      </div>';
        $output .= '    </div>';
        $output .= '  </div>';
        $output .= '  <div class="ind5-a6 wow fadeInUp" data-wow-delay="0.5s">';
        $output .= '    <div class="ind5-b1">';
        $output .= '      <div class="swiper-container">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==0){

                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide">';
                    $output .= '            <div class="ind5-b6">';
                    $output .= '              <div class="ind5-b2 i100"><img src="'.$va->icon_img.'"></div>';
                    $output .= '              <div class="ind5-b3">';
                    $output .= '                <div class="ind5-b4">'.$va->title_list.'</div>';
                    $output .= '                <div class="ind5-b5">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }

        }

        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/80b3aea7171bf937a96fdea00ce396ab.png"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">敷尔佳Voolga</div>';
        // $output .= '                <div class="ind5-b5">高端网站定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '      <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>';
        $output .= '      <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>';
        $output .= '    </div>';
        $output .= '    <div class="ind5-b1">';
        $output .= '      <div class="swiper-container">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==1){

                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide">';
                    $output .= '            <div class="ind5-b6">';
                    $output .= '              <div class="ind5-b2 i100"><img src="'.$va->icon_img.'"></div>';
                    $output .= '              <div class="ind5-b3">';
                    $output .= '                <div class="ind5-b4">'.$va->title_list.'</div>';
                    $output .= '                <div class="ind5-b5">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }

        }

        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/fc77edb82d51fe7fc2feb3aacda45d55.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">SHEGRIE</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/9f44066630d3fbd7b46a0ebd84788d64.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">东运优宜酷爱车家</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/09aecfcc0436d934fd6fc3deed079e55.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">吉林敖东</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/8ad5e9f86df227f8b2c854fc544ada4b.png"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">九亿直播</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/720a71f0e3ba6b31cb5534d005388341.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">如祭</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/e21f5d9f283ef36caa8d381adfae238c.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">疯天然</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/57b8025fde76d3757470eb1f24cd347c.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">正和</div>';
        // $output .= '                <div class="ind5-b5">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '      <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>';
        $output .= '      <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>';
        $output .= '    </div>';
        $output .= '    <div class="ind5-b1">';
        $output .= '      <div class="swiper-container">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==2){

                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide">';
                    $output .= '            <div class="ind5-b6">';
                    $output .= '              <div class="ind5-b2 i100"><img src="'.$va->icon_img.'"></div>';
                    $output .= '              <div class="ind5-b3">';
                    $output .= '                <div class="ind5-b4">'.$va->title_list.'</div>';
                    $output .= '                <div class="ind5-b5">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }

        }

        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/d0d32736c98e80c5c9375aec56a8098b.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">资海E店</div>';
        // $output .= '                <div class="ind5-b5">高端电商定制方案</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '      <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>';
        $output .= '      <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>';
        $output .= '    </div>';
        $output .= '    <div class="ind5-b1">';
        $output .= '      <div class="swiper-container">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==3){

                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide">';
                    $output .= '            <div class="ind5-b6">';
                    $output .= '              <div class="ind5-b2 i100"><img src="'.$va->icon_img.'"></div>';
                    $output .= '              <div class="ind5-b3">';
                    $output .= '                <div class="ind5-b4">'.$va->title_list.'</div>';
                    $output .= '                <div class="ind5-b5">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }

        }

        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/6c63c5a7d394b0e732653f91675e8228.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">艺星整形</div>';
        // $output .= '                <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/af9dd28ef61d708bd1134136b40ee6a2.png"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">宝宇雪猪肉</div>';
        // $output .= '                <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide">';
        // $output .= '            <div class="ind5-b6">';
        // $output .= '              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/d8149b7c3592177f38e8362192406f47.jpg"></div>';
        // $output .= '              <div class="ind5-b3">';
        // $output .= '                <div class="ind5-b4">海芸青再生资源回收 </div>';
        // $output .= '                <div class="ind5-b5">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '      <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>';
        $output .= '      <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>';
        $output .= '      <div class="swiper-pagination"></div>';
        $output .= '    </div>';
        $output .= '  </div>';
        $output .= '</div>';
        $output .= '<div class="p-index-a4">';
        $output .= '  <div class="p-index-a4-tab wow fadeInLeft animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInLeft;">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            $output .= '    <div class="p-index-a4-list  '; if($k==0){ $output .= 'p-index-a4-list-ac';} $output .= '"> <i></i>';
            $output .= '      <div>' . $v->title . '</div>';
            $output .= '    </div>';
        }

        // $output .= '    <div class="p-index-a4-list   p-index-a4-list-ac"> <i></i>';
        // $output .= '      <div>高端网站</div>';
        // $output .= '    </div>';
        // $output .= '    <div class="p-index-a4-list"> <i></i>';
        // $output .= '      <div>APP案例</div>';
        // $output .= '    </div>';
        // $output .= '    <div class="p-index-a4-list"> <i></i>';
        // $output .= '      <div>电商案例</div>';
        // $output .= '    </div>';
        // $output .= '    <div class="p-index-a4-list"> <i></i>';
        // $output .= '      <div>小程序案例</div>';
        // $output .= '    </div>';
        $output .= '  </div>';
        $output .= '  <div class="p-index-a4-box wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">';
        $output .= '    <div class="p-index-a4-line  on1">';
        $output .= '      <div class="swiper mySwiper swiper-container3 swiper33311 swiper-container-horizontal swiper-container-ios">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==0){
                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide"> <i class="i300"><img src="'.$va->phone_img.'" alt=""></i>';
                    $output .= '            <div class="p-index-a4-pos">';
                    $output .= '              <div>';
                    $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">'.$va->title_list.'</div>';
                    $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }
        }


        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/89373c0266b2fd449136870ac0002831.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">敷尔佳Voolga</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端网站定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/2a21aa2e1edeb351ab52c88442ee6750.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">KC皮草</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端响应式网站</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide " > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/89373c0266b2fd449136870ac0002831.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">敷尔佳Voolga</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端网站定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide "> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/2a21aa2e1edeb351ab52c88442ee6750.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">KC皮草</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端响应式网站</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>      ';
        $output .= '    </div>';
        $output .= '    <div class="p-index-a4-line">';
        $output .= '      <div class="swiper mySwiper swiper-container3 swiper33321 swiper-container-horizontal swiper-container-ios">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==1){
                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide"> <i class="i300"><img src="'.$va->phone_img.'" alt=""></i>';
                    $output .= '            <div class="p-index-a4-pos">';
                    $output .= '              <div>';
                    $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">'.$va->title_list.'</div>';
                    $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }
        }

        // $output .= '          <div class="swiper-slide" data-swiper-slide-index="0"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/169533f41ea4e3a6b8ee1cfbaef78c69.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">SHEGRIE</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide" > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/51086c5def17d8f64678906a857f76a6.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">东运优宜酷爱车家</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/b68772139a666b8f3fe2fee2ec5e8992.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">吉林敖东</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/e67e72ea2ed91ff77ae869e1afba4d1e.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">九亿直播</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/bd505c88982201670666965e8e5a8813.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">如祭</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/68bd09be0604b08d0559c739f6038c30.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">疯天然</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide " > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/77ee25c34acaa0b7cf372c3c2db6cfd0.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">正和</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '    </div>';
        $output .= '    <div class="p-index-a4-line">';
        $output .= '      <div class="swiper mySwiper swiper-container3 swiper33331">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==2){
                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide"> <i class="i300"><img src="'.$va->phone_img.'" alt=""></i>';
                    $output .= '            <div class="p-index-a4-pos">';
                    $output .= '              <div>';
                    $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">'.$va->title_list.'</div>';
                    $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }
        }

        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/c9c4d14931f464c187ac013fd84e3f9b.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">资海E店</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端电商定制方案</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>';
        $output .= '    </div>';
        $output .= '    <div class="p-index-a4-line">';
        $output .= '      <div class="swiper mySwiper swiper-container3 swiper33341 swiper-container-horizontal swiper-container-ios">';
        $output .= '        <div class="swiper-wrapper">';

        foreach ($jw_tab_item_goods_eight as $k => $v) {
            if($k==3){
                foreach ($v->goods_desc as $ka => $va) {

                    $output .= '          <div class="swiper-slide"> <i class="i300"><img src="'.$va->phone_img.'" alt=""></i>';
                    $output .= '            <div class="p-index-a4-pos">';
                    $output .= '              <div>';
                    $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">'.$va->title_list.'</div>';
                    $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">'.$va->intro_list.'</div>';
                    $output .= '              </div>';
                    $output .= '            </div>';
                    $output .= '          </div>';
                }
            }
        }

        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/922517feb0e597e0a8d9f0028453be5c.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">海芸青再生资源回收 </div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/81a161191969beac8ddb9e81df24e2f0.jpg" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">艺星整形</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        // $output .= '          <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/074bdcc2f229d313cc49056795543efc.png" alt=""></i>';
        // $output .= '            <div class="p-index-a4-pos">';
        // $output .= '              <div>';
        // $output .= '                <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">宝宇雪猪肉</div>';
        // $output .= '                <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端小程序定制</div>';
        // $output .= '              </div>';
        // $output .= '            </div>';
        // $output .= '          </div>';
        $output .= '        </div>';
        $output .= '      </div>     ';
        $output .= '    </div>';
        $output .= '   <div class="swiper-pagination in-page1 in-page11"><span class="swiper-pagination-current">1</span> / <span class="swiper-pagination-total">9</span></div>';
        $output .= '  </div>';

        $output .= '</div>';
//        $output .= '<script type="text/javascript" src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/js/jquery.min.js"></script> ';
//        $output .= '<script src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/js/swiper.min.js"></script> ';
        $output .= "<script>
             /*ind5*/
            if ($('".$addonId." .ind5-a3').length) {
                $('".$addonId." .ind5-a3').eq(0).addClass('on1');
                $('".$addonId." .ind5-b1').eq(0).addClass('on1');
                for (var i = 0; i < $('.ind5-b1').length; i++) {
                    $('".$addonId." .ind5-b1').eq(i).find('.swiper-container').addClass('ind5' + (i + 1) + '1');
                    $('".$addonId." .ind5-b1').eq(i).find('.swiper-button-prev').addClass('ind5' + (i + 1) + '2');
                    $('".$addonId." .ind5-b1').eq(i).find('.swiper-button-next').addClass('ind5' + (i + 1) + '3');
                    /*swiper1('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3',5000,500);
                    swiper4('ind5'+(i+1)+'1','ind5'+(i+1)+'2','ind5'+(i+1)+'3');*/
                    if ($('".$addonId." .ind5-b1').eq(i).find('.swiper-slide').length <= 1) {
                        $('".$addonId." .ind5-b1').eq(i).find('.swiper-button-prev').hide();
                        $('".$addonId." .ind5-b1').eq(i).find('.swiper-button-next').hide();
                    }
                }
                for (var i = 0; i < $('".$addonId." .ind5-b1').length; i++) {
                    for (var j = 0; j < $('".$addonId." .ind5-b1').eq(i).find('.swiper-container .swiper-slide').length; j++) {
                        var el1 = $('".$addonId." .ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').html();
                        $('".$addonId." .ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').empty();
                        for (var k = 0; k < 12; k++) {
                         $('".$addonId." .ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(j).find('.ind5-b6').append('<div class=\"ind5-b7\"><div class=\"ind5-b8\">' + el1 + '</div></div>');
                        }
                    }
                }
                for (var i = 0; i < $('.ind5-b1').length; i++) {
                    $('".$addonId." .ind5-b1').eq(i).find('.swiper-container .swiper-slide').eq(0).addClass('on1');
                }
                var ind5tap1 = true;
                $('".$addonId." .ind5-b1 .swiper-button-next').click(function() {
                    if (ind5tap1) {
                        ind5tap1 = false;
                        var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                        var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                        var el1 = $(this).parents('.ind5-b1');
                        console.log(idx1)
                        if (idx1 == leng1 - 1) {
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(0).addClass('on1');
                            setTimeout(function() {
                                el1.find('.swiper-slide.on2').removeClass('on2');
                                ind5tap1 = true;
                            }, 600);
                        } else {
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 + 1).addClass('on1');
                            setTimeout(function() {
                                el1.find('.swiper-slide.on2').removeClass('on2');
                                ind5tap1 = true;
                            }, 600);
                        }
                    }
                });
                $('".$addonId." .ind5-b1 .swiper-button-prev').click(function() {
                    if (ind5tap1) {
                        ind5tap1 = false;
                        var leng1 = $(this).parents('.ind5-b1').find('.swiper-slide').length;
                        var idx1 = $(this).parents('.ind5-b1').find('.swiper-slide.on1').index();
                        var el1 = $(this).parents('.ind5-b1');
                        console.log(idx1)
                        if (idx1 == 0) {
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(leng1 - 1).addClass('on1');
                            setTimeout(function() {
                                el1.find('.swiper-slide.on2').removeClass('on2');
                                ind5tap1 = true;
                            }, 600);
                        } else {
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1).removeClass('on1').addClass('on2');
                            $(this).parents('.ind5-b1').find('.swiper-slide').eq(idx1 - 1).addClass('on1');
                            setTimeout(function() {
                                el1.find('.swiper-slide.on2').removeClass('on2');
                                ind5tap1 = true;
                            }, 600);
                        }
                    }
                });
            }
            $('".$addonId." .ind5-a3').mouseenter(function() {
                $('".$addonId." .ind5-a3').removeClass('on1');
                $(this).addClass('on1');
                $('".$addonId." .ind5-b1').removeClass('on1');
                $('".$addonId." .ind5-b1').eq($(this).index()).addClass('on1');
            });
             $('".$addonId." .p-index-a4-list').click(function() {
                $('".$addonId." .p-index-a4-list').removeClass('p-index-a4-list-ac');
                $(this).addClass('p-index-a4-list-ac');
                $('".$addonId." .p-index-a4-line').removeClass('on1').eq($(this).index()).addClass('on1');
                $('".$addonId." .p-index-a4-line').removeClass('on1').eq($(this).index()).addClass('on1');
                var c= $('".$addonId." .in-page11 .swiper-pagination-current').html();
                  console.log('current:'+c);
                  if(c !==1){
                   $('".$addonId." .in-page11 .swiper-pagination-current').html(1);
                   $('".$addonId." .p-index-a4 .p-index-a4-box .on1 .mySwiper .swiper-wrapper').find('.swiper-slide').removeClass('swiper-slide-prev');
                   $('".$addonId." .p-index-a4 .p-index-a4-box .on1 .mySwiper .swiper-wrapper').find('.swiper-slide').removeClass('swiper-slide-active').eq(0).addClass('swiper-slide-active');
                   $('".$addonId." .p-index-a4 .p-index-a4-box .on1 .mySwiper .swiper-wrapper').find('.swiper-slide').removeClass('swiper-slide-next').eq(1).addClass('swiper-slide-next');

                  }
                  var a= $('".$addonId." .p-index-a4 .p-index-a4-box .on1 .mySwiper .swiper-wrapper').find('.swiper-slide').length;
                  $('".$addonId." .in-page11 .swiper-pagination-total').html(a);
                  console.log('total:'+a);
                 var d= $('".$addonId." .in-page11 .swiper-pagination-current').html();
                  console.log('d_current:'+d);
            })

            </script>
            <script>
                var swiper = new Swiper('".$addonId." .mySwiper', {
                    //loop: true,
                    pagination: {
                      el: '".$addonId." .in-page11',
                      type: 'fraction',
                    },
                });
                var a= $('".$addonId." .p-index-a4 .p-index-a4-box .on1 .mySwiper .swiper-wrapper').find('.swiper-slide').length;
                $('".$addonId." .in-page11 .swiper-pagination-total').html(a);
                console.log('total:'+a);

            </script>";

        return $output;

    }

    public function scripts()
    {
        $scripts = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/js/swiper-bundle.min.js',
        );
        return $scripts;
    }

    public function stylesheets()
    {
        $style_sheet = array(
            JURI::base(true) . '/components/com_jwpagefactory/assets/css/swiper-bundle.min.css',
        );
        return $style_sheet;
    }

    public static function getTemplate()
    {
        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        // 轮播项11
        var jw_tab_item = data.jw_tab_item;
        // 是否开启切换按钮
        var is_swiper_button = data.is_swiper_button || 0;
        // 上翻页按钮
        var swiper_button_prev = data.swiper_button_prev || "https://oss.lcweb01.cn/joomla/20210813/d264788e8b520dcef402370995f34e1b.png";
        // 下翻页按钮
        var swiper_button_next = data.swiper_button_next || "https://oss.lcweb01.cn/joomla/20210813/159993b9590c90e4951c76e7cf980c2e.png";
        // 切换按钮宽度
        var swiper_button_width = data.swiper_button_width || 24;
        // 切换按钮高度
        var swiper_button_height = data.swiper_button_height || 24;
        // 切换按钮上边距（百分比）
        var swiper_button_top = data.swiper_button_top || 48;
        // 切换按钮两侧边距（px）
        var swiper_button_left = data.swiper_button_left || 10;
        // 是否开启轮播点
        var is_swiper_pagination = data.is_swiper_pagination || 0;
        // 轮播点宽度
        var swiper_p_width = data.swiper_p_width || 8;
        // 轮播点高度
        var swiper_p_height = data.swiper_p_height || 8;
        // 轮播点间距
        var swiper_p_margin = data.swiper_p_margin || 5;
        // 轮播点颜色
        var swiper_p_color = data.swiper_p_color || "#f0f0f0";
        // 选中轮播点宽度
        var swiper_p_width_a = data.swiper_p_width_a;
        // 选中轮播点高度
        var swiper_p_height_a = data.swiper_p_height_a;
        // 选中轮播点颜色
        var swiper_p_color_a = data.swiper_p_color_a || "#007aff";
        #>
        

        <style>
        {{addonId}} .i300 {overflow:hidden;}
        {{addonId}} .ind5-a1{width:100%;position:relative;z-index:1;background:linear-gradient(180deg,#fff,#f6f6f6)}
        {{addonId}} .ind5-a2{width:1560px;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;z-index:1;padding:20px}
        {{addonId}} .ind5-a3{float:left;cursor:pointer}
        {{addonId}} .ind5-a3.on1 .ind5-a5{color:#d90029 !important}
        {{addonId}} .ind5-a4{width:0;height:5px;background:#d90029;float:left;position:relative;top:6px;margin-right:16px;transition:0.8s}
        {{addonId}} .ind5-a3:hover .ind5-a4{width:26px;transition:0.8s}
        {{addonId}} .ind5-a3.on1 .ind5-a4{width:26px;transition:0.8s}
        {{addonId}} .ind5-a5{font-size:18px;line-height:18px;color:#333333;font-weight:bold;font-style:italic;float:left;margin:0 24px 0 0;transition:0.8s}
        {{addonId}} .ind5-a3:hover .ind5-a5{color:#d90029;transition:0.8s}
        {{addonId}} .ind5-a6{width:1560px;height:690px;margin:0 auto;position:relative}
        {{addonId}} .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:1;z-index:1;transition:0.8s}
        {{addonId}} .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}
        {{addonId}} .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}
        {{addonId}} .ind5-b2{width:100%;height:100%;position:relative}
        {{addonId}} .ind5-b2 img{min-height:100%;transition:0.8s}
        {{addonId}} .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}
        {{addonId}} .ind5-b3{position:absolute;bottom:104px;right:60px}
        {{addonId}} .ind5-b4{font-size:{{data.title_size_pc}}px;line-height:36px;color:{{data.title_color_pc}};font-weight:bold;text-align:right;margin-bottom:22px}
        {{addonId}} .ind5-b5{font-size:{{data.desc_size_pc}}px;line-height:30px;color:{{data.desc_color_pc}};text-align:right}
        {{addonId}} .ind5-b1 .swiper-button-prev{width:12px;height:22px;padding:0px;position:absolute;bottom:36px;right:100px;background-image:none !important;left:unset !important;top:92% !important}
        {{addonId}} .ind5-b1 .swiper-button-next{width:12px;height:22px;padding:0px;position:absolute;bottom:36px;right:60px;background-image:none !important;left:unset !important;top:92% !important}
        {{addonId}} .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}
        {{addonId}} .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}
        {{addonId}} .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}
        {{addonId}} .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}
        {{addonId}} .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}
        {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}
        {{addonId}} .ind5-b8{width:1200%;height:100%;position:absolute;top:0}
        {{addonId}} .ind5-b7:nth-child(1) .ind5-b8{left:0}
        {{addonId}} .ind5-b7:nth-child(2) .ind5-b8{left:-100%}
        {{addonId}} .ind5-b7:nth-child(3) .ind5-b8{left:-200%}
        {{addonId}} .ind5-b7:nth-child(4) .ind5-b8{left:-300%}
        {{addonId}} .ind5-b7:nth-child(5) .ind5-b8{left:-400%}
        {{addonId}} .ind5-b7:nth-child(6) .ind5-b8{left:-500%}
        {{addonId}} .ind5-b7:nth-child(7) .ind5-b8{left:-600%}
        {{addonId}} .ind5-b7:nth-child(8) .ind5-b8{left:-700%}
        {{addonId}} .ind5-b7:nth-child(9) .ind5-b8{left:-800%}
        {{addonId}} .ind5-b7:nth-child(10) .ind5-b8{left:-900%}
        {{addonId}} .ind5-b7:nth-child(11) .ind5-b8{left:-1000%}
        {{addonId}} .ind5-b7:nth-child(12) .ind5-b8{left:-1100%}
        @media only screen and (min-width:1024px){
            {{addonId}} .p-index-a4{ display:none;} 
            {{addonId}} .in-page11{ display:none;}  
            {{addonId}} .swiper-button-prev:after, .swiper-rtl .swiper-button-next:after {content: "";}
            {{addonId}} .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {content: "";}
        }
        @media only screen and (max-width:1599px) and (min-width:1400px){
            {{addonId}} .ind5-a2{width:1360px;margin:0 auto 50px;}
            {{addonId}} .ind5-a6{width:1360px;height:600px;margin:0 auto;position:relative}
            {{addonId}} .ind5-b1{width:100%;height:100%;position:absolute;top:0;left:0;opacity:1;z-index:1;transition:0.8s}
            {{addonId}} .ind5-b1.on1{opacity:1;z-index:2;transition:0.8s}
            {{addonId}} .ind5-b1 .swiper-container{width:100%!important;height:100%!important;position:relative}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:relative}
            {{addonId}} .ind5-b2{width:100%;height:100%;position:relative}
            {{addonId}} .ind5-b2 img{min-height:100%;transition:0.8s}
            {{addonId}} .ind5-b1:hover .ind5-b2 img{transform:scale(1.08);transition:0.8s}
            {{addonId}} .ind5-b3{position:absolute;bottom:104px;right:60px}
            {{addonId}} .ind5-b4{font-size:{{data.title_size_pc}}px;line-height:36px;color:{{data.title_color_pc}};font-weight:bold;text-align:right;margin-bottom:22px}
            {{addonId}} .ind5-b5{font-size:{{data.desc_size_pc}}px;line-height:30px;color:{{data.desc_color_pc}};text-align:right}
            {{addonId}} .ind5-b1 .swiper-button-prev{width:32px;height:42px;padding:0px;position:absolute;bottom:36px;right:96px}
            {{addonId}} .ind5-b1 .swiper-button-next{width:32px;height:42px;padding:0px;position:absolute;bottom:36px;right:60px}
            {{addonId}} .ind5-b1 .swiper-button-prev img,.ind5-b1 .swiper-button-next img{opacity:0.5;transition:0.5s}
            {{addonId}} .ind5-b1 .swiper-button-prev:hover img,.ind5-b1 .swiper-button-next:hover img{opacity:1;transition:0.5s}
            {{addonId}} .ind5-b1 .swiper-container .swiper-wrapper{width:100%;height:100%;position:relative}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide{width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:1}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1{z-index:2}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2{z-index:3}
            {{addonId}} .ind5-b6{width:100%;height:100%;position:relative;overflow:hidden}
            {{addonId}} .ind5-b7{width:calc(100%/12);height:100%;position:relative;overflow:hidden;float:left;transition:0.5s}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7{transform:rotateY(0deg);transition:0.5s}
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7{transform:rotateY(90deg);transition:0.5s}
            {{addonId}} .ind5-b8{width:1200%;height:100%;position:absolute;top:0}
        }
        @media only screen and (max-width: 1399px) and (min-width: 1200px) {
            {{addonId}} .ind5-a1 {
                width: 100%;
                position: relative;
                z-index: 1;
                background: linear-gradient(180deg, #fff, #f6f6f6);
            }
            {{addonId}} .ind5-a2 {
                width: 1160px;
                margin: 0 auto 36px;
                position: relative;
            }
            {{addonId}} .ind5-a3 {
                float: left;
                cursor: pointer;
            }
            {{addonId}} .ind5-a4 {
                width: 0;
                height: 5px;
                background: #d90029;
                float: left;
                position: relative;
                top: 6px;
                margin-right: 16px;
                transition: 0.8s;
            }
            {{addonId}} .ind5-a3:hover .ind5-a4 {
                width: 26px;
                transition: 0.8s;
            }
            {{addonId}} .ind5-a3.on1 .ind5-a4 {
                width: 26px;
                transition: 0.8s;
            }
            {{addonId}} .ind5-a5 {
                font-size: 18px;
                line-height: 18px;
                color: #333333;
                font-weight: bold;
                font-style: italic;
                float: left;
                margin: 0 24px 0 0;
                transition: 0.8s;
            }
            {{addonId}} .ind5-a3:hover .ind5-a5 {
                color: #d90029;
                transition: 0.8s;
            }
            {{addonId}} .ind5-a6 {
                width: 1160px;
                height: 510px;
                margin: 0 auto;
                position: relative;
            }
            {{addonId}} .ind5-b1 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                opacity: 1;
                z-index: 1;
                transition: 0.8s;
            }
            {{addonId}} .ind5-b1.on1 {
                opacity: 1;
                z-index: 2;
                transition: 0.8s;
            }
            {{addonId}} .ind5-b1 .swiper-container {
                width: 100% !important;
                height: 100% !important;
                position: relative;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide {
                width: 100% !important;
                height: 100% !important;
                position: relative;
            }
            {{addonId}} .ind5-b2 {
                width: 100%;
                height: 100%;
                position: relative;
            }
            {{addonId}} .ind5-b2 img {
                min-height: 100%;
                transition: 0.8s;
            }
            {{addonId}} .ind5-b1:hover .ind5-b2 img {
                transform: scale(1.08);
                transition: 0.8s;
            }
            {{addonId}} .ind5-b3 {
                position: absolute;
                bottom: 84px;
                right: 40px;
            }
            {{addonId}} .ind5-b4 {
                font-size: 28px;
                line-height: 28px;
                color: {{data.title_color_pc}};
                font-weight: bold;
                /*font-style: italic;*/
                text-align: right;
                margin-bottom: 16px;
            }
            {{addonId}} .ind5-b5 {
                font-size: 20px;
                line-height: 24px;
                color: {{data.desc_color_pc}};
                text-align: right;
            }
            {{addonId}} .ind5-b1 .swiper-button-prev {
                width: 32px;
                height: 42px;
                padding: 10px;
                position: absolute;
                bottom: 28px;
                right: 76px;
            }
            {{addonId}} .ind5-b1 .swiper-button-next {
                width: 32px;
                height: 42px;
                padding: 10px;
                position: absolute;
                bottom: 28px;
                right: 40px;
            }
            {{addonId}} .ind5-b1 .swiper-button-prev img,
            {{addonId}} .ind5-b1 .swiper-button-next img {
                opacity: 0.5;
                transition: 0.5s;
            }
            {{addonId}} .ind5-b1 .swiper-button-prev:hover img,
            {{addonId}} .ind5-b1 .swiper-button-next:hover img {
                opacity: 1;
                transition: 0.5s;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-wrapper {
                width: 100%;
                height: 100%;
                position: relative;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide {
                width: 100% !important;
                height: 100% !important;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 1;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1 {
                z-index: 2;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2 {
                z-index: 3;
            }
            {{addonId}} .ind5-b6 {
                width: 100%;
                height: 100%;
                position: relative;
                overflow: hidden;
            }
            {{addonId}} .ind5-b7 {
                width: calc(100%/12);
                height: 100%;
                position: relative;
                overflow: hidden;
                float: left;
                transition: 0.5s;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on1 .ind5-b7 {
                transform: rotateY(0deg);
                transition: 0.5s;
            }
            {{addonId}} .ind5-b1 .swiper-container .swiper-slide.on2 .ind5-b7 {
                transform: rotateY(90deg);
                transition: 0.5s;
            }
            {{addonId}} .ind5-b8 {
                width: 1200%;
                height: 100%;
                position: absolute;
                top: 0;
            }
            
        }
        @media only screen and (max-width:1199px) and (min-width:1024px){
            {{addonId}} .ind5-a2 {
                width: 960px;
                margin: 0 auto 36px;
            }
            {{addonId}} .ind5-a6 {
                width: 960px;
                height: 426px;
            }
        }
        @media only screen and (max-width:1023px){
            {{addonId}} .i300>img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                filter: brightness(0.75);
            }
            {{addonId}} .ind5-a1 {
                display: none;
            }
            {{addonId}} .in-page11 {
                display: block;
            }
            {{addonId}} .p-index-a4 {
                width: 100%;
                overflow: hidden;
                padding: 0 12px;
            }
            {{addonId}} .p-index-a4-tab {
                padding-top: 20px;
                overflow-x: auto;
                width: 100%;
                white-space: nowrap;
                padding-bottom: 30px;
                font-size: 18px;
            }
            {{addonId}} .p-index-a4-list {
                display: inline-block;
                width: calc(100% / 3);
                color: #333333;
                font-weight: bolder;
                text-align: center;
            }
            {{addonId}} .p-index-a4-list div {
                display: inline-block;
            }
            {{addonId}} .p-index-a4-list i {
                display: inline-block;
                width: 0;
                transition: .3s;
            }
            {{addonId}} .p-index-a4-list.p-index-a4-list-ac {
                color: #d90029;
            }
            {{addonId}} .p-index-a4-list.p-index-a4-list-ac i {
                width: 26px;
                height: 5px;
                background: #d90029;
                vertical-align: middle;
                transition: .3s;
            }
            {{addonId}} .p-index-a4-box {
                position: relative;
                height: 148vw;
                width: 100%;
            }
            {{addonId}} .p-index-a4-line {
                position: relative;
                height: 100%;
            }
            {{addonId}} .p-index-a4-box .swiper-container3 {
                overflow: hidden;
                height: 100%;
            }
            {{addonId}} .p-index-a4-box .swiper .swiper-slide .p-index-a4-pos {
                position: absolute;
                width: 100%;
                height: 100%;
                bottom: 4rem;
                left: 0;
            }
            {{addonId}} .p-index-a4-pos>div {
                position: absolute;
                width: 100%;
                left: 0;
                bottom: 5.3rem;
                text-align: center;
            }
            {{addonId}} .p-index-a4-pos .p-index-a4-t1 {
                font-size: 2.3rem;
                color: {{data.title_color_sj}};
                font-weight: bolder;
                margin-bottom: 1.4rem;
                line-height: 1;
            }
            {{addonId}} .p-index-a4-pos .p-index-a4-t2 {
                color: {{data.desc_color_sj}};
                font-size: 1.32rem;
            }
            {{addonId}} .in-page1 {
                position: absolute;
                width: 100%;
                /*top: 30rem;*/
                left: 0;
                z-index: 6;
                color: #fff;
                text-align: center;
                font-size: 1rem;
                bottom: 30px;
            }
            {{addonId}} .in-page1 .swiper-pagination-current {
                font-size: 1.5rem;
                font-weight: bold;
            }
            {{addonId}} .p-index-a4-box .in-page1 span {
                font-family: "rubik";
            }
            {{addonId}} .p-index-a4-box .in-page1 i {
                font-style: normal;
                display: inline-block;
                margin: 0 .1rem;
            }
            {{addonId}} .p-index-a4-box .in-page1 .in-page-ac1 {
                font-size: .42rem;
                font-weight: bolder;
            }
            {{addonId}} .p-index-a4-box .in-page1 .in-page-total1 {
                font-size: .26rem;
            }
            {{addonId}} .p-index-a4-box .p-index-a4-line {
                opacity: 0;
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                z-index: -1;
            }
            {{addonId}} .p-index-a4-box .p-index-a4-line.on1 {
                opacity: 1;
                z-index: 0;
            }
        }
        </style>

        
        <div class="ind5-a1">
            <div style="overflow-x: auto;">
            <div class="ind5-a2 clear wow fadeInUp" data-wow-delay="0.3s">
            <div class="ind5-a3 clear">
            <div class="ind5-a4"></div>
             <div class="ind5-a5">高端网站</div>
              </div>
               <div class="ind5-a3 clear">
               <div class="ind5-a4"></div>
                <div class="ind5-a5">APP案例</div>
              </div>
               <div class="ind5-a3 clear">
             <div class="ind5-a4"></div>
               <div class="ind5-a5">电商案例</div>
             </div>
             <div class="ind5-a3 clear">
               <div class="ind5-a4"></div>
                <div class="ind5-a5">小程序案例</div>
              </div>
             </div>
             </div>
            <div class="ind5-a6 wow fadeInUp" data-wow-delay="0.5s">
              <div class="ind5-b1">
               <div class="swiper-container">
                <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div class="ind5-b6">
                  <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/8080b2d44ee6cdd4ba0fec5ea231d1ae.jpg"></div>
              <div class="ind5-b3">
                     <div class="ind5-b4">KC皮草</div>
                 <div class="ind5-b5">高端响应式网站</div>
                  </div>
                </div>
               </div>
                 <div class="swiper-slide">
              <div class="ind5-b6">
                    <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/80b3aea7171bf937a96fdea00ce396ab.png"></div>
                <div class="ind5-b3">
                      <div class="ind5-b4">敷尔佳Voolga</div>
                  <div class="ind5-b5">高端网站定制</div>
               </div>
               </div>
               </div>
               </div>
               </div>
             <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>
              <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>
              </div>
              <div class="ind5-b1">
              <div class="swiper-container">
               <div class="swiper-wrapper">
                 <div class="swiper-slide">
                 <div class="ind5-b6">
                  <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/fc77edb82d51fe7fc2feb3aacda45d55.jpg"></div>
              <div class="ind5-b3">
                    <div class="ind5-b4">SHEGRIE</div>
                        <div class="ind5-b5">高端APP定制</div>
                   </div>
                   </div>
             </div>
              <div class="swiper-slide">
                  <div class="ind5-b6">
               <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/9f44066630d3fbd7b46a0ebd84788d64.jpg"></div>
             <div class="ind5-b3">
                   <div class="ind5-b4">东运优宜酷爱车家</div>
                 <div class="ind5-b5">高端APP定制</div>
                 </div>
                </div>
                 </div>
              <div class="swiper-slide">
                <div class="ind5-b6">
              <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/09aecfcc0436d934fd6fc3deed079e55.jpg"></div>
               <div class="ind5-b3">
                 <div class="ind5-b4">吉林敖东</div>
                   <div class="ind5-b5">高端APP定制</div>
                 </div>
                   </div>
                 </div>
              <div class="swiper-slide">
                 <div class="ind5-b6">
                  <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/8ad5e9f86df227f8b2c854fc544ada4b.png"></div>
                 <div class="ind5-b3">
                  <div class="ind5-b4">九亿直播</div>
                   <div class="ind5-b5">高端APP定制</div>
              </div>
                  </div>
               </div>
               <div class="swiper-slide">
                <div class="ind5-b6">
                  <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/720a71f0e3ba6b31cb5534d005388341.jpg"></div>
                <div class="ind5-b3">
                <div class="ind5-b4">如祭</div>
              <div class="ind5-b5">高端APP定制</div>
             </div>
               </div>
            </div>
            <div class="swiper-slide">
              <div class="ind5-b6">
                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/e21f5d9f283ef36caa8d381adfae238c.jpg"></div>
             <div class="ind5-b3">
               <div class="ind5-b4">疯天然</div>
               <div class="ind5-b5">高端APP定制</div>
                 </div>
                 </div>
               </div>
               <div class="swiper-slide">
                 <div class="ind5-b6">
                   <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/57b8025fde76d3757470eb1f24cd347c.jpg"></div>
                    <div class="ind5-b3">
               <div class="ind5-b4">正和</div>
                  <div class="ind5-b5">高端APP定制</div>
                   </div>
               </div>
                </div>
             </div>
            </div>
              <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>
              <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>
             </div>
            <div class="ind5-b1">
            <div class="swiper-container">
              <div class="swiper-wrapper">
               <div class="swiper-slide">
              <div class="ind5-b6">
                <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/d0d32736c98e80c5c9375aec56a8098b.jpg"></div>
              <div class="ind5-b3">
                <div class="ind5-b4">资海E店</div>
                  <div class="ind5-b5">高端电商定制方案</div>
                 </div>
              </div>
               </div>
              </div>
            </div>
              <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>
              <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>
              </div>
             <div class="ind5-b1">
              <div class="swiper-container">
              <div class="swiper-wrapper">
             <div class="swiper-slide">
                 <div class="ind5-b6">
                    <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/6c63c5a7d394b0e732653f91675e8228.jpg"></div>
                <div class="ind5-b3">
                   <div class="ind5-b4">艺星整形</div>
                 <div class="ind5-b5">高端小程序定制</div>
                  </div>
                 </div>
                </div>
              <div class="swiper-slide">
                   <div class="ind5-b6">
                   <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/af9dd28ef61d708bd1134136b40ee6a2.png"></div>
               <div class="ind5-b3">
                      <div class="ind5-b4">宝宇雪猪肉</div>
                     <div class="ind5-b5">高端小程序定制</div>
                  </div>
                   </div>
                  </div>
                 <div class="swiper-slide">
                  <div class="ind5-b6">
                    <div class="ind5-b2 i100"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/d8149b7c3592177f38e8362192406f47.jpg"></div>
                 <div class="ind5-b3">
                     <div class="ind5-b4">海芸青再生资源回收 </div>
                  <div class="ind5-b5">高端小程序定制</div>
                 </div>
                  </div>
                  </div>
                </div>
                </div>
               <div class="swiper-button-prev i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a35.png"></div>
             <div class="swiper-button-next i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/a36.png"></div>
               <div class="swiper-pagination"></div>
             </div>
            </div>
            </div>
            <div class="p-index-a4">
              <div class="p-index-a4-tab wow fadeInLeft animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInLeft;">
               <div class="p-index-a4-list   p-index-a4-list-ac"> <i></i>
               <div>高端网站</div>
               </div>
               <div class="p-index-a4-list"> <i></i>
               <div>APP案例</div>
             </div>
              <div class="p-index-a4-list"> <i></i>
                <div>电商案例</div>
              </div>
              <div class="p-index-a4-list"> <i></i>
                <div>小程序案例</div>
               </div>
             </div>
             <div class="p-index-a4-box wow fadeInUp animated" data-wow-delay="0.3s" data-wow-duration="2s" style="visibility: visible; animation-duration: 2s; animation-delay: 0.3s; animation-name: fadeInUp;">
              <div class="p-index-a4-line  on1">
              <div class="swiper mySwiper swiper-container3 swiper33311 swiper-container-horizontal swiper-container-ios">
                <div class="swiper-wrapper">
                 <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/89373c0266b2fd449136870ac0002831.png" alt=""></i>
                 <div class="p-index-a4-pos">
                    <div>
                       <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">敷尔佳Voolga</div>
                       <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端网站定制</div>
                     </div>
                     </div>
                  </div>
                <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/2a21aa2e1edeb351ab52c88442ee6750.png" alt=""></i>
                   <div class="p-index-a4-pos">
                   <div>
                     <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">KC皮草</div>
                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端响应式网站</div>
                     </div>
                </div>
                   </div>
                <div class="swiper-slide " > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/89373c0266b2fd449136870ac0002831.png" alt=""></i>
            <div class="p-index-a4-pos">
                <div>
                  <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">敷尔佳Voolga</div>
                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端网站定制</div>
                </div>
            </div>
            </div>
            <div class="swiper-slide "> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/2a21aa2e1edeb351ab52c88442ee6750.png" alt=""></i>
                <div class="p-index-a4-pos">
                <div>
                    <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">KC皮草</div>
                   <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端响应式网站</div>
                   </div>
                    </div>
                   </div>
                 </div>
               </div>      
              </div>
            <div class="p-index-a4-line">
                <div class="swiper mySwiper swiper-container3 swiper33321 swiper-container-horizontal swiper-container-ios">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" data-swiper-slide-index="0"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/169533f41ea4e3a6b8ee1cfbaef78c69.png" alt=""></i>
                            <div class="p-index-a4-pos">
                                <div>
                                    <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">SHEGRIE</div>
                                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                                </div>
                            </div>
                        </div>
                     <div class="swiper-slide" > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/51086c5def17d8f64678906a857f76a6.jpg" alt=""></i>
                    <div class="p-index-a4-pos">
                    <div>
                        <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">东运优宜酷爱车家</div>
                        <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                    </div>
                    </div>
                   </div>
                  <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/b68772139a666b8f3fe2fee2ec5e8992.jpg" alt=""></i>
                <div class="p-index-a4-pos">
                    <div>
                   <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">吉林敖东</div>
                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                     </div>
                      </div>
                 </div>
                  <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/e67e72ea2ed91ff77ae869e1afba4d1e.png" alt=""></i>
                 <div class="p-index-a4-pos">
                <div>
                    <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">九亿直播</div>
                     <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                 </div>
               </div>
                   </div>
               <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/bd505c88982201670666965e8e5a8813.jpg" alt=""></i>
                  <div class="p-index-a4-pos">
                      <div>
                   <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">如祭</div>
                   <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                     </div>
                   </div>
                 </div>
                <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/68bd09be0604b08d0559c739f6038c30.jpg" alt=""></i>
                 <div class="p-index-a4-pos">
                      <div>
                      <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">疯天然</div>
                   <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                   </div>
                  </div>
                 </div>
                <div class="swiper-slide " > <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/77ee25c34acaa0b7cf372c3c2db6cfd0.jpg" alt=""></i>
               <div class="p-index-a4-pos">
                <div>
                     <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">正和</div>
                   <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端APP定制</div>
                   </div>
                   </div>
                  </div>
                 </div>
               </div>
            </div>
            <div class="p-index-a4-line">
                <div class="swiper mySwiper swiper-container3 swiper33331">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide"> <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/c9c4d14931f464c187ac013fd84e3f9b.jpg" alt=""></i>
                            <div class="p-index-a4-pos">
                                <div>
                                    <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">资海E店</div>
                                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端电商定制方案</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-index-a4-line">
                <div class="swiper mySwiper swiper-container3 swiper33341 swiper-container-horizontal swiper-container-ios">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide"> 
                            <i class="i300"><img src="' . str_replace('administrator/', '', JURI::base()) . 'components/com_jwpagefactory/addons/swiper_tabtop/assets/img/922517feb0e597e0a8d9f0028453be5c.jpg" alt=""></i>
                            <div class="p-index-a4-pos">
                                <div>
                                    <div class="p-index-a4-t1 ani" swiper-animate-effect="fadeInUp">海芸青再生资源回收 </div>
                                    <div class="p-index-a4-t2 ani" swiper-animate-effect="fadeInUp">高端小程序定制</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>     
            </div>
            <div class="swiper-pagination in-page1 in-page11"><span class="swiper-pagination-current">1</span> / <span class="swiper-pagination-total">9</span></div>
            </div>

           </div>

        ';

        return $output;
    }

}
