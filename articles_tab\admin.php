<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die('Restricted access');
$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);


JwAddonsConfig::addonConfig(
    array(
        'type' => 'repeatable',
        'addon_name' => 'articles_tab',
        'title' => '文章列表选项卡',
        'desc' => '文章列表选项卡',
        'category' => '文章',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'site_id' => array(
                    'std' => $site_id,
                ),
                'company_id' => array(
                    'std' => $company_id,
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H1'),
                        'h2' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H2'),
                        'h3' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H3'),
                        'h4' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H4'),
                        'h5' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H5'),
                        'h6' => JText::_('COM_JWPAGEFACTORY_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),
                'catid' => array(
                    'type' => 'category',
                    'title' => '选择分类',
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_ARTICLES_CATID_DESC'),
                    'multiple' => true,
                ),
                'title_font_family' => array(
                    'type' => 'fonts',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_FAMILY_DESC'),
                    'depends' => array(array('title', '!=', '')),
                    'selector' => array(
                        'type' => 'font',
                        'font' => '{{ VALUE }}',
                        'css' => '.jwpf-addon-title { font-family: {{ VALUE }}; }'
                    )
                ),

                'title_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE'),
                    'desc' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_SIZE_DESC'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_LINE_HEIGHT'),
                    'std' => '',
                    'depends' => array(array('title', '!=', '')),
                    'max' => 400,
                    'responsive' => true
                ),
                'title_font_style' => array(
                    'type' => 'fontstyle',
                    'title' => JText::_('COM_JWPAGEFACTORY_ADDON_TITLE_FONT_STYLE'),
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_letterspace' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_JWPAGEFACTORY_GLOBAL_LETTER_SPACING'),
                    'values' => array(
                        '0' => 'Default',
                        '1px' => '1px',
                        '2px' => '2px',
                        '3px' => '3px',
                        '4px' => '4px',
                        '5px' => '5px',
                        '6px' => '6px',
                        '7px' => '7px',
                        '8px' => '8px',
                        '9px' => '9px',
                        '10px' => '10px'
                    ),
                    'std' => '0',
                    'depends' => array(array('title', '!=', '')),
                ),
                'title_text_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题文字颜色'),
                    'desc' => JText::_('标题文字颜色'),
                    'std' => '#ffffff',
                ),
                'title_text_color_z' => array(
                    'type' => 'color',
                    'title' => JText::_('选中标题文字颜色'),
                    'desc' => JText::_('选中标题文字颜色'),
                    'std' => '#ffffff',
                ),
                'title_bg_color' => array(
                    'type' => 'color',
                    'title' => JText::_('标题背景颜色'),
                    'desc' => JText::_('标题背景颜色'),
                    'std' => '#4e4c4c',
                ),
                'title_bg_color_z' => array(
                    'type' => 'color',
                    'title' => JText::_('选中标题背景颜色'),
                    'desc' => JText::_('选中标题背景颜色'),
                    'std' => '#0056ff',
                ),
                'title_size' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题文字大小'),
                    'desc' => JText::_('标题文字大小'),
                    'max' => 100,
                    'min' => 1,
                    'std' => 16,
                ),
                'title_width' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题宽度'),
                    'desc' => JText::_('标题宽度'),
                    'max' => 1000,
                    'std' => 125,
                ),
                'title_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题高度'),
                    'desc' => JText::_('标题高度'),
                    'max' => 1000,
                    'std' => 35,
                ),
                'title_jj' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题间距'),
                    'desc' => JText::_('标题间距'),
                    'max' => 100,
                    'std' => 0,
                ),
                'content_jj' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题内容间距'),
                    'desc' => JText::_('标题内容间距'),
                    'max' => 300,
                    'min' => 40,
                    'std' => 40,
                ),
                'title_border' => array(
                    'type' => 'slider',
                    'title' => JText::_('标题圆角'),
                    'desc' => JText::_('标题圆角'),
                    'max' => 50,
                    'min' => 0,
                    'std' => 1,
                ),
            ),
        ),
    )
);