<?php
/*
 * @Author: your name
 * @Date: 2021-04-02 09:27:17
 * @LastEditTime: 2021-04-14 10:11:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \joomla_test\components\com_jwpagefactory\addons\search_box\admin.php
 */

defined('_JEXEC') or die ('resticted aceess');

$app = JFactory::getApplication();

$input = $app->input;
$layout_id = $input->get('layout_id', '');
$site_id = $input->get('site_id', 0);
$company_id = $input->get('company_id', 0);

JwAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'pie_chart',
        'title' => JText::_('饼图'),
        'desc' => JText::_(''),
        'category' => '其他',
        'attr' => array(
            'general' => array(
                
                'pie_width' => array(
                    'type' => 'slider',
                    'title' => '饼图宽高',
                    'max' => 1200,
                    'min' => 0,
                    'std' => '600',
                ),
                'pie_startAngle' => array(
                    'type' => 'slider',
                    'title' => '起始角度',
                    'max' => 360,
                    'min' => 0,
                    'std' => '120',
                ),
                'pie_border' => array(
                    'type' => 'slider',
                    'title' => '边框大小',
                    'max' => 100,
                    'min' => 0,
                    'std' => '10',
                ),
                'piesm_zb' => array(
                    'type' => 'slider',
                    'title' => '中心圆占比(%)',
                    'max' => 100,
                    'min' => 0,
                    'std' => '30',
                ),
                'piebig_xz' => array(
                    'type' => 'select',
                    'title' => '文字效果',
                    'values' => array(
                        'tangential' => JText::_('切向旋转'),
                        'radial' => JText::_('径向旋转'),
                    ),
                    'std' => 'tangential',
                    
                ),
                'piebig_wz' => array(
                    'type' => 'select',
                    'title' => '文字位置',
                    'values' => array(
                        'right' => JText::_('居外'),
                        'center' => JText::_('居中'),
                        'left' => JText::_('居内'),
                    ),
                    'std' => 'right',
                    
                ),
                'piebig_wzpy' => array(
                    'type' => 'slider',
                    'title' => '文字上下位置偏移',
                    'max' => 300,
                    'min' => 0,
                    'std' => '70',
                    'depends' => array(
                        array('piebig_wz', '!=', 'center'),
                    ),
                ),
                'piebig_wzpyzy' => array(
                    'type' => 'slider',
                    'title' => '文字左右位置偏移',
                    'max' => 300,
                    'min' => '-300',
                    'std' => '-60',
                    'depends' => array(
                        array('piebig_wz', '!=', 'center'),
                    ),
                ),
                'piesm_wzpy' => array(
                    'type' => 'slider',
                    'title' => '内圆小图标位置偏移量',
                    'max' => 300,
                    'min' => '-300',
                    'std' => '-30',
                   
                ),
                //饼图属性
                'jw_carousel_item' => array(
                    'title' => JText::_('饼图设置'),
                    'std' => array(
                        array(
                            'title' => '01',
                            'china_title' => '医疗产品',
                            'english_title' => 'Medical products',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#FF5A4D',
                            'pie_bgcolor2' => '#FF7D77',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                        array(
                            'title' => '02',
                            'china_title' => '科技创新',
                            'english_title' => 'Technology',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#FDA15F',
                            'pie_bgcolor2' => '#FF7727',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                        array(
                            'title' => '03',
                            'china_title' => '呼吸百科',
                            'english_title' => 'encyclopedia',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#FFCA43',
                            'pie_bgcolor2' => '#FEC436',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                        array(
                            'title' => '04',
                            'china_title' => '全称呵护',
                            'english_title' => 'The whole care',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#16FDA6',
                            'pie_bgcolor2' => '#49FDC7',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                        array(
                            'title' => '05',
                            'china_title' => '健康百科',
                            'english_title' => 'Diseases',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#389BFF',
                            'pie_bgcolor2' => '#65B3F9',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                         array(
                            'title' => '06',
                            'china_title' => '解决方案',
                            'english_title' => 'treatment plan',
                            'pieqk_zb' => '15',
                            'qkbg_checked' => 'color1',
                            'pie_bgcolor1' => '#FF6BDA',
                            'pie_bgcolor2' => '#FB6ED1',
                            'piesm_imgwidth' => '30',
                            'piesm_imgheight' => '30',
                            'piesm_zb' => '15',
                            'piesm_bgcolor' => 'rgba(255,255,255,0.2)',

                        ),
                    ),
                    'attr' => array(
                        'equipment' => array(
                            'type' => 'buttons',
                            'title' => '设置项',
                            'std' => 'img_title',
                            'values' => array(
                                array(
                                    'label' => '外圆',
                                    'value' => 'img_title'
                                ),
                                array(
                                    'label' => '内圆',
                                    'value' => 'ban_cont'
                                ),
                            ),
                            'tabs' => true,
                        ),
                        'pieqk_zb' => array(
                            'type' => 'slider',
                            'title' => '外圆扇形占比',
                            'max' => 100,
                            'min' => 1,
                            'std' => '15',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                            ),
                        ),
                        'qkbg_checked' => array(
                            'type' => 'select',
                            'title' => JText::_('背景色'),
                            'values' => array(
                                'color1' => JText::_('渐变色'),
                                'color2' => JText::_('纯色'),
                            ),
                            'std' => 'color1',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                            ),
                        ),
                        'pie_bgcolor' => array(
                            'type' => 'color',
                            'title' => JText::_('纯色'),
                            'std' => '#FF5A4D',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                                array('qkbg_checked', '=', 'color2'),
                            ),
                        ),
                        'pie_bgcolor1' => array(
                            'type' => 'color',
                            'title' => JText::_('左侧色值'),
                            'std' => '#FF6BDA',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                                array('qkbg_checked', '=', 'color1'),
                            ),
                        ),
                        'pie_bgcolor2' => array(
                            'type' => 'color',
                            'title' => JText::_('右侧色值'),
                            'std' => '#FB6ED1',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                                array('qkbg_checked', '=', 'color1'),
                            ),
                        ),

                        'title' => array(
                            'type' => 'text',
                            'title' => JText::_('序号名称'),
                            'std' => '01',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                            ),
                        ),
                        'china_title' => array(
                            'type' => 'text',
                            'title' => JText::_('中文名称'),
                            'std' => '解决方案',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                            ),
                        ),
                        'english_title' => array(
                            'type' => 'text',
                            'title' => JText::_('英文名称'),
                            'std' => 'Solution',
                            'depends' => array(
                                array('equipment', '=', 'img_title'),
                            ),
                        ),
                        // 'media_url_show' => array(
                        //     'type' => 'checkbox',
                        //     'title' => JText::_('是否启用链接跳转'),
                        //     'desc' => JText::_('开启可设置图片点击跳转地址'),
                        //     'values' => array(
                        //         1 => JText::_('JYES'),
                        //         0 => JText::_('JNO'),
                        //     ),
                        //     'std' => 0,
                        //     'depends' => array(
                        //         array('equipment', '=', 'img_title'),
                        //     ),
                        // ),

                        // 'tz_page_type' => array(
                        //     'type' => 'select',
                        //     'title' => JText::_('跳转方式'),
                        //     'desc' => JText::_('跳转方式'),
                        //     'values' => array(
                        //         'Internal_pages' => JText::_('内部页面'),
                        //         'external_links' => JText::_('外部链接'),
                        //     ),
                        //     'std' => 'external_links',
                        //     'depends' => array(
                        //         array('media_url_show', '=', 1),
                        //         array('equipment', '=', 'img_title'),
                        //     ),
                        // ),

                        // 'media_url' => array(
                        //     'type' => 'text',
                        //     'title' => JText::_('链接跳转地址'),
                        //     'desc' => JText::_('链接必须以http://或https://开始'),
                        //     'placeholder' => 'http://',
                        //     'std' => '',
                        //     'depends' => array(
                        //         array('media_url_show', '=',1),
                        //         array('equipment', '=', 'img_title'),
                        //         array('tz_page_type', '=', 'external_links'),
                        //     )
                        // ),
                        // 'detail_page_id' => array(
                        //     'type' => 'select',
                        //     'title' => '选择跳转页面',
                        //     'desc' => '',
                        //     'values' => !empty($layout_id) ? JwPageFactoryBase::getPagesByTemplate($layout_id) : array(),
                        //     'depends' => array(
                        //         array('media_url_show', '=',1),
                        //         array('equipment', '=', 'img_title'),
                        //         array('tz_page_type', '=', 'Internal_pages')
                        //     ),
                        // ),
                        // 新加跳转方式
                        // 'media_target' => array(
                        //     'type' => 'select',
                        //     'title' => JText::_('链接跳转方式'),
                        //     'desc' => JText::_('链接跳转方式'),
                        //     'values' => array(
                        //         '' => JText::_('当前页面'),
                        //         '_blank' => JText::_('新窗口'),
                        //     ),
                        //     'depends' => array(
                        //         array('media_url_show', '=',1),
                        //         array('equipment', '=', 'img_title'),

                        //     )
                        // ),

                        // 内圆
                        'piesm_zb' => array(
                            'type' => 'slider',
                            'title' => '内圆扇形占比',
                            'max' => 100,
                            'min' => 1,
                            'std' => '15',
                            'depends' => array(
                                array('equipment', '=', 'ban_cont'),
                            ),
                        ),
                        'piesm_bgcolor' => array(
                            'type' => 'color',
                            'title' => JText::_('内圆背景色'),
                            'std' => 'rgba(255,255,255,0.2)',
                            'depends' => array(
                                array('equipment', '=', 'ban_cont'),
                            ),
                        ),
                        'piesm_img' => array(
                            'type' => 'media',
                            'title' => JText::_('内圆小图标'),
                            'format' => 'image',
                            'std' => 'https://oss.lcweb01.cn/joomla/20210818/7946af25c7d1940abd738932e71d3bc8.png',
                            'depends' => array(
                                array('equipment', '=', 'ban_cont'),
                            )
                        ),
                        'piesm_imgwidth' => array(
                            'type' => 'slider',
                            'title' => '图标宽',
                            'max' => 100,
                            'min' => 0,
                            'std' => '30',
                            'depends' => array(
                                array('equipment', '=', 'ban_cont'),
                            ),
                        ),
                        'piesm_imgheight' => array(
                            'type' => 'slider',
                            'title' => '图标高',
                            'max' => 100,
                            'min' => 0,
                            'std' => '30',
                            'depends' => array(
                                array('equipment', '=', 'ban_cont'),
                            ),
                        ),
                    ),
                ),

                //文字属性
                'wz_equipment' => array(
                    'type' => 'buttons',
                    'title' => '文字属性',
                    'std' => 'big_title',
                    'values' => array(
                        array(
                            'label' => '序号标题',
                            'value' => 'big_title'
                        ),
                        array(
                            'label' => '中文标题',
                            'value' => 'china_title'
                        ),
                        array(
                            'label' => '英文标题',
                            'value' => 'english_title'
                        ),
                    ),
                    'tabs' => true,
                ),
                'xhbold_checked' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('字体加粗'),
                    'std' => 1,
                    'depends' => array(
                        array('wz_equipment', '=', 'big_title'),
                    ),
                ),
                'xhtitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'max' => 100,
                    'std' => array('md' => 46, 'sm' => 36, 'xs' => 16),
                    'responsive' => true,
                    'depends' => array(
                        array('wz_equipment', '=', 'big_title'),
                    ),
                ),
                'xhtitle_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('wz_equipment', '=', 'big_title'),
                    ),
                ),
                
                // 中文标题
                'chinabold_checked' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('字体加粗'),
                    'std' => 1,
                    'depends' => array(
                        array('wz_equipment', '=', 'china_title'),
                    ),
                ),
                'chinatitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'max' => 100,
                    'std' => array('md' => 16, 'sm' => 16, 'xs' => 14),
                    'responsive' => true,
                    'depends' => array(
                        array('wz_equipment', '=', 'china_title'),
                    ),
                ),
                'chinatitle_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('wz_equipment', '=', 'china_title'),
                    ),
                ),
                'chinatitle_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('行高'),
                    'max' => 100,
                    'std' => 25,
                    'depends' => array(
                        array('wz_equipment', '=', 'china_title'),
                    ),
                ),
                // 英文标题
                'engbold_checked' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('字体加粗'),
                    'std' => 0,
                    'depends' => array(
                        array('wz_equipment', '=', 'english_title'),
                    ),
                ),
                'engtitle_fontsize' => array(
                    'type' => 'slider',
                    'title' => JText::_('字体大小'),
                    'max' => 100,
                    'std' => array('md' => 14, 'sm' => 14, 'xs' => 12),
                    'responsive' => true,
                    'depends' => array(
                        array('wz_equipment', '=', 'english_title'),
                    ),
                ),
                'engtitle_color' => array(
                    'type' => 'color',
                    'title' => JText::_('字体颜色'),
                    'std' => '#fff',
                    'depends' => array(
                        array('wz_equipment', '=', 'english_title'),
                    ),
                ),
                'engtitle_lineheight' => array(
                    'type' => 'slider',
                    'title' => JText::_('行高'),
                    'max' => 100,
                    'std' => 25,
                    'depends' => array(
                        array('wz_equipment', '=', 'english_title'),
                    ),
                ),
                
     
            ),
        ),
    )
);
