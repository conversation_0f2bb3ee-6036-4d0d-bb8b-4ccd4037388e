<?php
/**
 * <AUTHOR>
 * @email        <EMAIL>
 * @url          http://www.joomla.work
 * @copyright    Copyright (c) 2010 - 2019 JoomWorker
 * @license      GNU General Public License version 2 or later
 * @date         2019/01/01 09:30
 */
//no direct accees
defined('_JEXEC') or die ('resticted access');

class JwpagefactoryAddonRaking_list extends JwpagefactoryAddons
{
    public function render()
    {
        $company_id = $_GET['company_id'] ?? 0;
        $site_id    = $_GET['site_id'] ?? 0;
        $layout_id  = $_GET['layout_id'] ?? 0;

        $yuming=$_SERVER['HTTP_HOST'];
        if($yuming=='jzt_dev_2.china9.cn'){
            $urlpath='http://jzt_dev_1.china9.cn';
        }elseif($yuming=='ijzt.china9.cn'){
            $urlpath='https://zhjzt.china9.cn';
        }else{
            $config = new JConfig();
            $urlpath = $config->jzt_url;
        }

        $addon_id = '#jwpf-addon-' . $this->addon->id;


        $settings = $this->addon->settings;
        //分类显示,选择几级分类
        $type_parent = (isset($settings->type_parent) && $settings->type_parent) ? $settings->type_parent : 'type1';
        //从第n个分类开始显示(导航)
        $type_start = (isset($settings->type_start) && $settings->type_start) ? $settings->type_start : 1;
        //显示n个分类(导航)
        $type_num = (isset($settings->type_num) && $settings->type_num) ? $settings->type_num : 4;
        //指定更多跳转页ID
        $gengduo = (isset($settings->gengduo)) ? $settings->gengduo : 0;
        //指定每个分类下展示几条
        $limit = (isset($settings->limit)) ? $settings->limit : 4;
        //是否详情页
        $isLinkDetail = (isset($settings->isLinkDetail)) ? $settings->isLinkDetail : 0;

        //       标题
        $top_tit=(isset($settings->top_tit) && $settings->top_tit)?$settings->top_tit:"培训排行榜";
        //        暂无数据占位内容
        $no_data=(isset($settings->no_data) && $settings->no_data)?$settings->no_data:"暂无数据";
                //选择显示页面
        $lxstyle = (isset($settings->lxstyle) && $settings->lxstyle)?$settings->lxstyle:'homey';


        //取出该公司数据
        $categorie_helper = JPATH_ROOT . '/components/com_jwpagefactory/helpers/categories.php';
        require_once $categorie_helper;

        $seach_image=(isset($settings->seach_image) && $settings->seach_image)?$settings->seach_image:"/components/com_jwpagefactory/addons/raking_list/assets/images/sd.png";
        // echo "<pre>";
        // print_r($item);
        // echo "</pre>";
        //var_dump($item);
        //数据转为多维数组



        if($lxstyle=='homey'){
            $item = JwpagefactoryHelperCategories::GetActivitylist($site_id, $detail_page_id, $layout_id, $company_id, 1);

            // 首页展示样式
            $output = '<style>
               {$addon_id} .rak_sealb tr{width:100%}
            </style>';
            $output .= '<div class="rak_cont">';

            $output .= ' <div class="rak_top"><div class="rak_tit fl">';
            $output .=  $top_tit ;
            $output .= '   </div><div class="rak_gd fr"><a href="' . JRoute::_("/index.php/component/jwpagefactory/?view=page&id=" . base64_encode($gengduo) . "&detail=" . '0' . "&Itemid=0&layout_id=" . $layout_id . '&site_id=' . $site_id . '&company_id=' . $company_id ) . '">更多>></a></div><div class="cl"></div>';
            $output .= ' </div>';

            $output .= ' <div class="rak_nr">';

            $output .= ' <div class="rak_ztys">';
            $output .= ' <div class="rak_top_left fl">';

            $output .= ' <select name="" id="selt"> </select>';
            $output .= ' </div>';

            $output .= ' <div class="rak_top_right fr">';
            $output .= '   <div class="rak_seach">';
            $output .= ' <input type="text" style="width:85%;display:inline" name="rak_sea" id="seach_key" placeholder="请输入关键词" >';
            $output .= ' <img class="ssbt" src="'.$seach_image.'" alt="">';
            $output .= ' <div class="cl"></div></div>';
            $output .= ' </div>';
            $output .= ' </div>';
            $output .= ' </div>';

            $output .= ' <div class="rak_sealb">';
            $output .= ' <table class="sjb">';
            $output .= '  <tr class="rak_ztysth">';
            $output .= ' <th width="20%" class="rak_pm">排名</th>';
            $output .= ' <th width="35%" class="rak_pmcs">培训次数</th>';
            $output .= ' <th width="43%" class="rak_pmzx">考评中心名称</th>';
            $output .= ' </tr>';

            if($item){

                foreach ($item as $k1 => $v1){

                    $output .= '  <tr class="yyc">';
                    $output .= ' <td width="20%">'.$v1['ranking'].'</td>';
                    $output .= ' <td width="35%">'.$v1['number'].'</td>';
                    $output .= ' <td width="43%">'.$v1['title'].'</td>';
                    $output .= ' </tr>';
                }

            }else{
                $output.='<tr class="yyc"><td colspan="3" class="no-data">'.$no_data.'</td></tr>';
            }

            $output .= ' </table>';

            $output .= ' </div>';
            $output .= ' </div>';

            $output .= '</div>';

        }else{
            $item = JwpagefactoryHelperCategories::GetActivitylist($site_id, $detail_page_id, $layout_id, $company_id, 2);

            $output = '<style>
               {$addon_id} .rak_sealb tr{width:100%}
            </style>';
            $output .= '<div class="rak_cont">';

            $output .= ' <div class="rak_nr">';

            $output .= ' <div class="rak_ztys">';
            $output .= ' <div class="rak_top_left fl">';

            $output .= ' <select name="" id="selt"> </select>';
            $output .= ' </div>';

            $output .= ' <div class="rak_top_right fr">';
            $output .= '   <div class="rak_seach">';
            $output .= ' <input type="text" style="width:85%;display:inline" name="rak_sea" id="seach_key" placeholder="请输入关键词" >';
            $output .= ' <img class="ssbt" src="'.$seach_image.'" alt="">';
            $output .= ' <div class="cl"></div></div>';
            $output .= ' </div>';
            $output .= ' </div>';
            $output .= ' </div>';

            $output .= ' <div class="rak_sealb">';
            $output .= ' <table class="sjb">';
                $output .= '  <tr class="rak_ztysth">';
                $output .= '<th width="7%">排名</th>
                <th width="9%">培训次数</th>
                <th width="9%">培训人数</th>
                <th width="15%">考评中心名称</th>
                <th width="8%">联系人</th>
                <th width="10%">联系电话</th>
                <th width="8%">星级</th>
                <th width="14%">详细地址</th>
                <th width="16%">详情</th>';
                $output .= ' </tr>';

            if($item){

                foreach ($item as $k1 => $v1){

                    $output .= '  <tr class="yyc">';
                    $output .= ' <td width="7%">'.$v1['ranking'].'</td>';
                    $output .= ' <td width="9%">'.$v1['number'].'</td>';
                    $output .= ' <td width="9%">'.$v1['pxrnumber'].'</td>';
                    $output .= ' <td width="15%">'.$v1['title'].'</td>';
                    $output .= ' <td width="8%">'.$v1['lxrname'].'</td>';
                    $output .= ' <td width="10%">'.$v1['lxrtel'].'</td>';
                    $output .= ' <td width="8%">'.$v1['xingji'].'</td>';
                    $output .= ' <td width="14%">'.$v1['xxaddress'].'</td>';
                    $output .= ' <td width="16%">'.$v1['fulltext'].'</td>';

                    $output .= ' </tr>';
                }

            }else{
                $output.='<tr class="yyc"><td colspan="9" class="no-data">'.$no_data.'</td></tr>';
            }

            $output .= ' </table>';

            $output .= ' </div>';
            $output .= ' </div>';

            $output .= '</div>';
        }
            $output .= '<script>

                jQuery("' . $addon_id . ' .ssbt").click(function(){
                    var search_name=jQuery("' . $addon_id . ' #seach_key").val();// 搜索关键词
                    console.log(search_name)
                    if(search_name==""){
                        alert("请输入搜索关键词")
                    }else{
                        var value = jQuery("' . $addon_id . ' #selt ").val(); // 下拉框选中值
                        onajx(value,search_name);
                    }
                });


                jQuery(document).on("ready", function(){
                    var options = "";
                    
                    //设置年份的选择
                    var myDate = new Date();

                    var startYear = myDate.getFullYear() - 10;//起始年份
                    var endYear = myDate.getFullYear();//结束年份
                    for (var i = startYear; i <= endYear; i++) //循环年份，动态生成option列表，拼接html
                    {
                        if (myDate.getFullYear() == i) {
                            options += "<option selected value="+i+">" + i + "</option>";
                        } else {
                            options += "<option value="+i+">" + i + "</option>";
                        }
                    }
                    jQuery("' . $addon_id . ' #selt ").html(options); 

                    jQuery("' . $addon_id . ' #selt ").on("change",function() {
                        var value = jQuery("' . $addon_id . ' #selt ").val(); // 选中值
                        var keys = jQuery("' . $addon_id . ' #seach_key ").val(); // key值

                        onajx(value,keys);
                        
                    });
                });';
                
                if($lxstyle=="homey"){
                    // 首页ajax筛选数据
                    $output .='function onajx(year,keys){
                        jQuery.ajax({
                            type: "POST",
                            url: "' . $urlpath . '/api/Shuju/activity",
                            dataType: "json",
                            data: {
                                "company_id" :' . $company_id . ',
                                "site_id" :' . $site_id . ',
                                "year":year,
                                "keys":keys,
                                "leixing":1

                            },
                            success: function (res) {
                                console.log(res.data);
                                var lens=res.data.length;

                                jQuery("' . $addon_id . ' .yyc ").remove(); 
                                    
                                    if(lens>0){
                                        var str="";
                                        for(var i=0;i < lens; i++){
                                            str+=\'<tr class="yyc">\';

                                            str+= \' <td width="20%">\'+res.data[i]["ranking"]+\'</td>\';
                                            str+= \' <td width="35%">\'+res.data[i]["number"]+\'</td>\';
                                            str+= \' <td width="43%">\'+res.data[i]["title"]+\'</td>\';
                                            
                                            str+=\'</tr>\';
                                        }
                                        jQuery("' . $addon_id . ' .sjb ").append(str); 
                                    }else{
                                        
                                        str=\'<tr class="yyc">\';

                                        str+= \' <td colspan="3" width="100%">'.$no_data.'</td>\';
                                        
                                        str+=\'</tr>\';

                                        jQuery("' . $addon_id . ' .sjb ").append(str); 
                                    }
                            }     
                        });
                    }';
                }else{
                    $output .='function onajx(year,keys){
                        jQuery.ajax({
                            type: "POST",
                            url: "' . $urlpath . '/api/Shuju/activity",
                            dataType: "json",
                            data: {
                                "company_id" :' . $company_id . ',
                                "site_id" :' . $site_id . ',
                                "year":year,
                                "keys":keys,
                                "leixing":2

                            },
                            success: function (res) {
                                console.log(res.data);
                                var lens=res.data.length;

                                jQuery("' . $addon_id . ' .yyc ").remove(); 
                                // 列表年份筛选数据
                                if(lens>0){
                                    var str="";
                                    for(var i=0;i < lens; i++){
                                        str+=\'<tr class="yyc">\';
                                        
                                            str+= \' <td width="8%">\'+res.data[i]["ranking"]+\'</td>\';
                                            str+= \' <td width="10%">\'+res.data[i]["number"]+\'</td>\';
                                            str+= \' <td width="10%">\'+res.data[i]["pxrnumber"]+\'</td>\';
                                            str+= \' <td width="15%">\'+res.data[i]["title"]+\'</td>\';
                                            str+= \' <td width="8%">\'+res.data[i]["lxrname"]+\'</td>\';
                                            str+= \' <td width="10%">\'+res.data[i]["lxrtel"]+\'</td>\';
                                            str+= \' <td width="8%">\'+res.data[i]["xingji"]+\'</td>\';
                                            str+= \' <td width="14%">\'+res.data[i]["xxaddress"]+\'</td>\';
                                            str+= \' <td width="17%">\'+res.data[i]["fulltext"]+\'</td>\';

                                        str+=\'</tr>\';
                                    }
                                    jQuery("' . $addon_id . ' .sjb ").append(str); 
                                }else{
                                    
                                    str=\'<tr class="yyc">\';

                                    str+= \' <td colspan="9" width="100%">'.$no_data.'</td>\';
                                    
                                    str+=\'</tr>\';

                                    jQuery("' . $addon_id . ' .sjb ").append(str); 
                                }
                            }     
                        });
                    }';
                }
                
            $output .='</script>';
 
        return $output;

    }

    //    引用css文件
    public function stylesheets()
    {
        //      $style_sheet = array(JURI::base(true) . '/components/com_jwpagefactory/assets/css/jquery.bxslider.min.css');
        return array(
            'https://www.dowebok.com/demo/2014/93/css/owl.carousel.css',
            'https://www.dowebok.com/demo/2014/93/css/owl.theme.css'
        );
    }

    //    内部css执行
    public function css()
    {
        $addon_id                     = '#jwpf-addon-' . $this->addon->id;
        $settings                     = $this->addon->settings;
        $w=(isset($settings->width) && $settings->width) ? $settings->width : 400;
        $w_sm=(isset($settings->width_sm) && $settings->width_sm) ? $settings->width_sm : 400;
        $w_xs=(isset($settings->width_xs) && $settings->width_xs) ? $settings->width_xs : 400;
        if($settings->paved===1){
            $width=$width_sm=$width_xs='100%';
        }else{
            $width=$w.'px';
            $width_sm=$w_sm.'px';
            $width_xs=$w_xs.'px';
        }

        $is_gradient=(isset($settings->is_gradient) && $settings->is_gradient) ? $settings->is_gradient : 1;
        $top_bgColor=(isset($settings->top_bgColor) && $settings->top_bgColor) ? $settings->top_bgColor : '#ffa';
        $top_bgColor_left=(isset($settings->top_bgColor_left) && $settings->top_bgColor_left) ? $settings->top_bgColor_left :"#4fa5fc";
        $top_bgColor_right=(isset($settings->top_bgColor_right) && $settings->top_bgColor_right) ? $settings->top_bgColor_right :"#3ea16a";
        if($is_gradient===1){
            $top_bgColor_str="background-image: linear-gradient(to right, ".$top_bgColor_left." , ".$top_bgColor_right.")";
        }else{
            $top_bgColor_str="background:".$top_bgColor.";";
        }

        $top_titColor=(isset($settings->top_titColor) && $settings->top_titColor) ? $settings->top_titColor : '#fff';
        $top_title_height=(isset($settings->top_title_height) && $settings->top_title_height) ?$settings->top_title_height:"40";
        $top_title_height_sm=(isset($settings->top_title_height_sm) && $settings->top_title_height_sm) ?$settings->top_title_height_sm:"40";
        $top_title_height_xs=(isset($settings->top_title_height_xs) && $settings->top_title_height_xs) ?$settings->top_title_height_xs:"40";

        $top_title_font_size=(isset($settings->top_title_font_size) && $settings->top_title_font_size)?$settings->top_title_font_size:"16";
        $top_title_font_size_sm=(isset($settings->top_title_font_size_sm) && $settings->top_title_font_size_sm)?$settings->top_title_font_size_sm:"16";
        $top_title_font_size_xs=(isset($settings->top_title_font_size_xs) && $settings->top_title_font_size_xs)?$settings->top_title_font_size_xs:"16";

        $filter_padding = (isset($settings->filter_padding) && $settings->filter_padding)?$settings->filter_padding:'8px 16px 8px 16px';
        $filter_padding_sm = (isset($settings->filter_padding_sm) && $settings->filter_padding_sm)?$settings->filter_padding_sm:'8px 16px 8px 16px';
        $filter_padding_xs = (isset($settings->filter_padding_xs) && $settings->filter_padding_xs)?$settings->filter_padding_xs:'8px 16px 8px 16px';

        $filter_year_width = (isset($settings->filter_year_width) && $settings->filter_year_width)?$settings->filter_year_width:'90';
        $filter_year_width_sm = (isset($settings->filter_year_width_sm) && $settings->filter_year_width_sm)?$settings->filter_year_width_sm:'90';
        $filter_year_width_xs = (isset($settings->filter_year_width_xs) && $settings->filter_year_width_xs)?$settings->filter_year_width_xs:'90';

        $filter_year_height = (isset($settings->filter_year_height) && $settings->filter_year_height)?$settings->filter_year_height:'100';
        $filter_year_height_sm = (isset($settings->filter_year_height_sm) && $settings->filter_year_height_sm)?$settings->filter_year_height_sm:'100';
        $filter_year_height_xs = (isset($settings->filter_year_height_xs) && $settings->filter_year_height_xs)?$settings->filter_year_height_xs:'100';

        $filter_year_color = (isset($settings->filter_year_color) && $settings->filter_year_color)?$settings->filter_year_color:'#72B19D';

        $filter_year_font_size = (isset($settings->filter_year_font_size) && $settings->filter_year_font_size)?$settings->filter_year_font_size:'16';
        $filter_year_font_size_sm = (isset($settings->filter_year_font_size_sm) && $settings->filter_year_font_size_sm)?$settings->filter_year_font_size_sm:'16';
        $filter_year_font_size_xs = (isset($settings->filter_year_font_size_xs) && $settings->filter_year_font_size_xs)?$settings->filter_year_font_size_xs:'16';

        $filter_year_background = (isset($settings->filter_year_background) && $settings->filter_year_background)?$settings->filter_year_background:'transparent';
        $filter_year_border_color = (isset($settings->filter_year_border_color) && $settings->filter_year_border_color)?$settings->filter_year_border_color:'transparent';

        $search_input_width = (isset($settings->search_input_width) && $settings->search_input_width)?$settings->search_input_width:'600';
        $search_input_width_sm = (isset($settings->filter_year_font_size_sm) && $settings->filter_year_font_size_sm)?$settings->filter_year_font_size_sm:'400';
        $search_input_width_xs = (isset($settings->filter_year_font_size_xs) && $settings->filter_year_font_size_xs)?$settings->filter_year_font_size_xs:'200';

        $search_input_height = (isset($settings->search_input_height) && $settings->search_input_height)?$settings->search_input_height:'35';
        $search_input_height_sm = (isset($settings->search_input_height_sm) && $settings->search_input_height_sm)?$settings->search_input_height_sm:'35';
        $search_input_height_xs = (isset($settings->search_input_height_xs) && $settings->search_input_height_xs)?$settings->search_input_height_xs:'35';

        $search_input_radius = (isset($settings->search_input_radius) && $settings->search_input_radius)?$settings->search_input_radius:'50';

        $search_text_align = (isset($settings->search_text_align) && $settings->search_text_align)?$settings->search_text_align:'left';

        $search_input_font_size = (isset($settings->search_input_font_size) && $settings->search_input_font_size)?$settings->search_input_font_size:'16';
        $search_input_font_size_sm = (isset($settings->search_input_font_size_sm) && $settings->search_input_font_size_sm)?$settings->search_input_font_size_sm:'16';
        $search_input_font_size_xs = (isset($settings->search_input_font_size_xs) && $settings->search_input_font_size_xs)?$settings->search_input_font_size_xs:'16';

        $search_input_color = (isset($settings->search_input_color) && $settings->search_input_color)?$settings->search_input_color:'#999996';
        $search_input_background = (isset($settings->search_input_background) && $settings->search_input_background)?$settings->search_input_background:'#fff';

        $search_button_size = (isset($settings->search_button_size) && $settings->search_button_size)?$settings->search_button_size:'20';
        $search_button_size_sm = (isset($settings->search_button_size_sm) && $settings->search_button_size_sm)?$settings->search_button_size_sm:'20';
        $search_button_size_xs = (isset($settings->search_button_size_xs) && $settings->search_button_size_xs)?$settings->search_button_size_xs:'20';

        $split_style = (isset($settings->split_style) && $settings->split_style)?$settings->split_style:'solid';
        $split_color = (isset($settings->split_color) && $settings->split_color)?$settings->split_color:'#ddd';

        $table_text_align = (isset($settings->table_text_align) && $settings->table_text_align)?$settings->table_text_align:'center';

        //选择显示页面
        $lxstyle = (isset($settings->lxstyle) && $settings->lxstyle)?$settings->lxstyle:'homey';

        $table_padding = (isset($settings->table_padding) && $settings->table_padding)?$settings->table_padding:'0px 16px 0px 16px';
        $table_padding_sm = (isset($settings->table_padding_sm) && $settings->table_padding_sm)?$settings->table_padding_sm:'0px 16px 0px 16px';
        $table_padding_xs = (isset($settings->table_padding_xs) && $settings->table_padding_xs)?$settings->table_padding_xs:'0px 16px 0px 16px';

        $table_head_line_height = (isset($settings->table_head_line_height) && $settings->table_head_line_height)?$settings->table_head_line_height:'35';
        $table_head_line_height_sm = (isset($settings->table_head_line_height_sm) && $settings->table_head_line_height_sm)?$settings->table_head_line_height_sm:'35';
        $table_head_line_height_xs = (isset($settings->table_head_line_height_xs) && $settings->table_head_line_height_xs)?$settings->table_head_line_height_xs:'35';

        $table_head_font_size = (isset($settings->table_head_font_size) && $settings->table_head_font_size)?$settings->table_head_font_size:'16';
        $table_head_font_size_sm = (isset($settings->table_head_font_size_sm) && $settings->table_head_font_size_sm)?$settings->table_head_font_size_sm:'16';
        $table_head_font_size_xs = (isset($settings->table_head_font_size_xs) && $settings->table_head_font_size_xs)?$settings->table_head_font_size_xs:'16';

        $table_head_font_color = (isset($settings->table_head_font_color) && $settings->table_head_font_color)?$settings->table_head_font_color:'#72B19D';

        $table_body_line_height = (isset($settings->table_body_line_height) && $settings->table_body_line_height)?$settings->table_body_line_height:'35';
        $table_body_line_height_sm = (isset($settings->table_body_line_height_sm) && $settings->table_body_line_height_sm)?$settings->table_body_line_height_sm:'35';
        $table_body_line_height_xs = (isset($settings->table_body_line_height_xs) && $settings->table_body_line_height_xs)?$settings->table_body_line_height_xs:'35';

        $table_body_font_size = (isset($settings->table_body_font_size) && $settings->table_body_font_size)?$settings->table_body_font_size:'16';
        $table_body_font_size_sm = (isset($settings->table_body_font_size_sm) && $settings->table_body_font_size_sm)?$settings->table_body_font_size_sm:'16';
        $table_body_font_size_xs = (isset($settings->table_body_font_size_xs) && $settings->table_body_font_size_xs)?$settings->table_body_font_size_xs:'16';

        $table_body_font_color = (isset($settings->table_body_font_color) && $settings->table_body_font_color)?$settings->table_body_font_color:'#484848';

        $no_data_line_height = (isset($settings->no_data_line_height) && $settings->no_data_line_height)?$settings->no_data_line_height:'35';
        $no_data_line_height_sm = (isset($settings->no_data_line_height_sm) && $settings->no_data_line_height_sm)?$settings->no_data_line_height_sm:'35';
        $no_data_line_height_xs = (isset($settings->no_data_line_height_xs) && $settings->no_data_line_height_xs)?$settings->no_data_line_height_xs:'35';

        $no_data_font_size = (isset($settings->no_data_font_size) && $settings->no_data_font_size)?$settings->no_data_font_size:'16';
        $no_data_font_size_sm = (isset($settings->no_data_font_size_sm) && $settings->no_data_font_size_sm)?$settings->no_data_font_size_sm:'16';
        $no_data_font_size_xs = (isset($settings->no_data_font_size_xs) && $settings->no_data_font_size_xs)?$settings->no_data_font_size_xs:'16';

        $no_data_font_color = (isset($settings->no_data_font_color) && $settings->no_data_font_color)?$settings->no_data_font_color:'#72B19D';
        $content_color = (isset($settings->content_color) && $settings->content_color)?$settings->content_color:'#F7F7F7';




        $css='';
        $css.='
            '.$addon_id.' .fl{float:left;}
            '.$addon_id.' .fr{float:right;}
            '.$addon_id.' .cl{clear:both;}

            '.$addon_id.' .rak_cont{width:'.$width.';background:'.$content_color.';}
            '.$addon_id.' .rak_top{width:100%;padding:5px 2%;'.$top_bgColor_str.';line-height:'.$top_title_height.'px;font-size:'.$top_title_font_size.'px;color:'.$top_titColor.';font-weight:bold}
            '.$addon_id.' .rak_top a{width:100%;font-size:'.$top_title_font_size.'px;color:'.$top_titColor.';}
            '.$addon_id.' .rak_top_left{width:'.$filter_year_width.'px;height:'.$filter_year_height.'px;}
            '.$addon_id.' .rak_top_right{width:'.$search_input_width.'px!important;}
            
            '.$addon_id.' .rak_ztys{
                padding:'.$filter_padding.';
                border-bottom:1px '.$split_style.' '.$split_color.';
                overflow:hidden;
            }
            '.$addon_id.' .rak_ztysth th{
                line-height:'.$table_head_line_height.'px!important;
                font-size: '.$table_head_font_size.'px!important;
                color:'.$table_head_font_color.';
            }
            '.$addon_id.' .rak_sealb{
                padding: '.$table_padding.';
            }
            '.$addon_id.' .rak_sealb table{width:100%;}
            '.$addon_id.' .rak_sealb table th,td{display:inline-block;text-align:'.$table_text_align.';}
            '.$addon_id.' .rak_sealb table,td{
                line-height:'.$table_body_line_height.'px;
                font-size:'.$table_body_font_size.'px;
                color: '.$table_body_font_color.';
            }
            '.$addon_id.' #seach_key{border:none;}
            '.$addon_id.' .rak_seach{
                border:1px solid #ccc;
                width:100%;
                border-radius:'.$search_input_radius.'px;
                line-height:'.$search_input_height.'px!important;
                padding:0px 5%;
                background:'.$search_input_background.';
            }
            '.$addon_id.' .rak_seach img{
                display:inline-block;
                width:'.$search_button_size.'px!important;
                height:auto;
                vertical-align:middle;
            }

            '.$addon_id.' #selt{
                height:100%;
                color:'.$filter_year_color.';
                font-size:'.$filter_year_font_size.'px;
                line-height:'.$filter_year_height.'px;
                background:'.$filter_year_background.';
                border-color:'.$filter_year_border_color.';
            }
            '.$addon_id.' .rak_seach input{
                text-align: '.$search_text_align.';
                font-size:'.$search_input_font_size.'px;
                color:'.$search_input_color.';
            }
            
            '.$addon_id.' .no-data{
                width: 100%;
                text-align:center;
                line-height:'.$no_data_line_height.'px;
                font-size:'.$no_data_font_size.'px;
                color:'.$no_data_font_color.';
            }
            
            @media (min-width:768px) and (max-width:991px){
                '.$addon_id.' .rak_cont{width:'.$width_sm.';}
                '.$addon_id.' .rak_top{
                    line-height:'.$top_title_height_sm.'px;
                    font-size:'.$top_title_font_size_sm.'px;
                }
                '.$addon_id.' .rak_ztys{
                    padding:'.$filter_padding_sm.';
                }
                '.$addon_id.' .rak_top_left{width:'.$filter_year_width_sm.'px;height:'.$filter_year_height_sm.'}px;}
                '.$addon_id.' #selt{font-size:'.$filter_year_font_size_sm.'px;line-height:'.$filter_year_height_sm.'px;}
                '.$addon_id.' .rak_top_right{width:'.$search_input_width_sm.'px;}
                '.$addon_id.' .rak_seach{line-height:'.$search_input_height_sm.'px;}
                '.$addon_id.' .rak_seach input{
                    font-size:'.$search_input_font_size_sm.'px;
                 }
                '.$addon_id.' .rak_seach img{
                    width:'.$search_button_size_sm.'px;
                }
                '.$addon_id.' .rak_sealb{
                    padding:'.$table_padding_sm.';
                }
                '.$addon_id.' .rak_ztysth th{
                    line-height:'.$table_head_line_height_sm.'px;
                    font-size: '.$table_head_font_size_sm.'px;
                }
                '.$addon_id.' .rak_sealb table,td{
                    line-height:'.$table_body_line_height_sm.'px;
                    font-size:'.$table_body_font_size_sm.'px;
                }
                '.$addon_id.' .no-data{
                    line-height:'.$no_data_line_height_sm.'px;
                    font-size:'.$no_data_font_size_sm.'px;
                }
            }
            @media (max-width:767px){
                '.$addon_id.' .rak_cont{width:'.$width_xs.';}
                '.$addon_id.' .rak_top{
                    line-height:'.$top_title_height_xs.'px;
                    font-size:'.$top_title_font_size_xs.'px;
                }
                '.$addon_id.' .rak_ztys{
                    padding:'.$filter_padding_xs.';
                }
                '.$addon_id.' .rak_top_left{width:'.$filter_year_width_xs.'px;height:'.$filter_year_height_xs.'px;}
                '.$addon_id.' #selt{font-size:'.$filter_year_font_size_xs.'px;line-height:'.$filter_year_height_xs.'px;}
                '.$addon_id.' .rak_top_right{width:'.$search_input_width_xs.'px;}
                '.$addon_id.' .rak_seach{line-height:'.$search_input_height_xs.'px;}
                '.$addon_id.' .rak_seach input{
                    font-size:'.$search_input_font_size_xs.'px;
                }
                '.$addon_id.' .rak_seach img{
                    width:'.$search_button_size_xs.'px;
                }
                '.$addon_id.' .rak_sealb{
                    padding:'.$table_padding_xs.';;
                }
                '.$addon_id.' .rak_ztysth th{
                    line-height:'.$table_head_line_height_xs.'px;
                    font-size: '.$table_head_font_size_xs.'px;
                }
                '.$addon_id.' .rak_sealb table,td{
                    line-height:'.$table_body_line_height_xs.'px;
                    font-size:'.$table_body_font_size_xs.'px;
                }
                '.$addon_id.' .no-data{
                    line-height:'.$no_data_line_height_xs.'px;
                    font-size:'.$no_data_font_size_xs.'px;
                }
            }
        ';

        return $css;
    }

    //    引用js文件
    public function scripts()
    {
        return array('https://www.dowebok.com/demo/2014/93/js/owl.carousel.js');
    }

    //    内部js执行
    public function js()
    {
        $addon_id = '#jwpf-addon-' . $this->addon->id;

        $settings                     = $this->addon->settings;
        $service_carousel_item_number = (isset($settings->service_carousel_item_number) && $settings->service_carousel_item_number) ? $settings->service_carousel_item_number : '4';

       // $js = "jQuery(function($){
       //      var options = '';
            
       //      //设置年份的选择
       //      var myDate = new Date();

       //      var startYear = myDate.getFullYear() - 10;//起始年份
       //      var endYear = myDate.getFullYear();//结束年份
       //      for (var i = startYear; i <= endYear; i++) //循环年份，动态生成option列表，拼接html
       //      {
       //          if (myDate.getFullYear() == i) {
       //              options += '<option selected value='+i+'>' + i + '</option>';
       //          } else {
       //              options += '<option value='+i+'>' + i + '</option>';
       //          }
 
       //      }
       //      jQuery('" . $addon_id . " #selt ').html(options); 

       //      alert(444);
       //      function changeValue(){
       //          var value = jQuery('" . $addon_id . " #selt ').val(); // 选中值
       //          alert(333);
       //          alert(value);
       //      }
       //  })";

        return $js;
    }

    public static function getTemplate()
    {

        $output = '
        <#
        var addonId = "#jwpf-addon-"+data.id;
        
        var seach_image = (!_.isEmpty(data.seach_image) && data.seach_image) ? data.seach_image : "";
        
        // 展示页面
        var lxstyle=data.lxstyle?data.lxstyle:"homey";


//        排行榜宽度是否铺满容器
        let w=(_.isObject(data.width)&&data.width)?data.width.md:data.width;
        let w_sm=(_.isObject(data.width)&&data.width)?data.width.sm:data.width;
        let w_xs=(_.isObject(data.width)&&data.width)?data.width.xs:data.width;
//        宽度
        let width="",width_sm="",width_xs="";
        if(data.paved===1){
            width=width_sm=width_xs="100%";
        }else{
            width=w+"px";width_sm=w_sm+"px";width_xs=w_xs+"px";
        }

//        头部渐变
        let top_bgColor="background:"+data.top_bgColor+";";
        let top_bgColor_left=data.top_bgColor_left?data.top_bgColor_left:"#4fa5fc";
        let top_bgColor_right=data.top_bgColor_right?data.top_bgColor_right:"#3ea16a";
        if(data.is_gradient===1){
            top_bgColor="background-image: linear-gradient(to right, "+top_bgColor_left+" , "+top_bgColor_right+")";
        }

//       标题
        let top_tit=(!_.isEmpty(data.top_tit) && data.top_tit)?data.top_tit:"培训排行榜";
//      标题颜色
        let top_titColor=(!_.isEmpty(data.top_titColor) && data.top_titColor)?data.top_titColor:"#fff";
//        行高
        let top_title_height=(_.isObject(data.top_title_height) && data.top_title_height)?data.top_title_height.md:"40";
        let top_title_height_sm=(_.isObject(data.top_title_height) && data.top_title_height)?data.top_title_height.sm:"40";
        let top_title_height_xs=(_.isObject(data.top_title_height) && data.top_title_height)?data.top_title_height.xs:"40";
//        字体
        let top_title_font_size=(_.isObject(data.top_title_font_size) && data.top_title_font_size)?data.top_title_font_size.md:"16";
        let top_title_font_size_sm=(_.isObject(data.top_title_font_size) && data.top_title_font_size)?data.top_title_font_size.sm:"16";
        let top_title_font_size_xs=(_.isObject(data.top_title_font_size) && data.top_title_font_size)?data.top_title_font_size.xs:"16";
        
//        主体背景色
        let content_color=(!_.isEmpty(data.content_color) && data.content_color)?data.content_color:"#F7F7F7";

//        筛选和搜索部分内边距
        let filter_padding = _.isObject(data.filter_padding)&&data.filter_padding?window.getMarginPadding(data.filter_padding.md, "padding"):window.getMarginPadding(data.filter_padding, "padding");
        let filter_padding_sm = _.isObject(data.filter_padding)&&data.filter_padding?window.getMarginPadding(data.filter_padding.sm, "padding"):window.getMarginPadding(data.filter_padding, "padding");
        let filter_padding_xs = _.isObject(data.filter_padding)&&data.filter_padding?window.getMarginPadding(data.filter_padding.xs, "padding"):window.getMarginPadding(data.filter_padding, "padding");
//         筛选宽度
        let filter_year_width = _.isObject(data.filter_year_width)&&data.filter_year_width?data.filter_year_width.md:data.filter_year_width;
        let filter_year_width_sm = _.isObject(data.filter_year_width)&&data.filter_year_width?data.filter_year_width.sm:data.filter_year_width;
        let filter_year_width_xs = _.isObject(data.filter_year_width)&&data.filter_year_width?data.filter_year_width.xs:data.filter_year_width;
//         筛选高度
        let filter_year_height=_.isObject(data.filter_year_height)&&data.filter_year_height?data.filter_year_height.md:data.filter_year_height;
        let filter_year_height_sm = _.isObject(data.filter_year_height)&&data.filter_year_height?data.filter_year_height.sm:data.filter_year_height;
        let filter_year_height_xs = _.isObject(data.filter_year_height)&&data.filter_year_height?data.filter_year_height.xs:data.filter_year_height;
//         筛选字体颜色
        let filter_year_color=(!_.isEmpty(data.filter_year_color) && data.filter_year_color)?data.filter_year_color:"#72B19D";
//        筛选字体大小
        let filter_year_font_size=_.isObject(data.filter_year_font_size)&&data.filter_year_font_size?data.filter_year_font_size.md:data.filter_year_font_size;
        let filter_year_font_size_sm = _.isObject(data.filter_year_font_size)&&data.filter_year_font_size?data.filter_year_font_size.sm:data.filter_year_font_size;
        let filter_year_font_size_xs = _.isObject(data.filter_year_font_size)&&data.filter_year_font_size?data.filter_year_font_size.xs:data.filter_year_font_size;
//        年份筛选背景色
        let filter_year_background=(!_.isEmpty(data.filter_year_background) && data.filter_year_background)?data.filter_year_background:"transparent";
//        筛选边框色
        let filter_year_border_color=(!_.isEmpty(data.filter_year_border_color) && data.filter_year_border_color)?data.filter_year_border_color:"transparent";

//      搜索框
//      宽度
        let search_input_width=_.isObject(data.search_input_width)&&data.search_input_width?data.search_input_width.md:data.search_input_width;
        let search_input_width_sm = _.isObject(data.search_input_width)&&data.search_input_width?data.search_input_width.sm:data.search_input_width;
        let search_input_width_xs = _.isObject(data.search_input_width)&&data.search_input_width?data.search_input_width.xs:data.search_input_width;
//      高度
        let search_input_height=_.isObject(data.search_input_height)&&data.search_input_height?data.search_input_height.md:data.search_input_height;
        let search_input_height_sm = _.isObject(data.search_input_height)&&data.search_input_height?data.search_input_height.sm:data.search_input_height;
        let search_input_height_xs = _.isObject(data.search_input_height)&&data.search_input_height?data.search_input_height.xs:data.search_input_height;
//        圆角
        let search_input_radius=(!_.isEmpty(data.search_input_radius) && data.search_input_radius)?data.search_input_radius:"100";
//        对齐
        let search_text_align=(!_.isEmpty(data.search_text_align) && data.search_text_align)?data.search_text_align:"center";
//      字体大小
        let search_input_font_size=_.isObject(data.search_input_font_size)&&data.search_input_font_size?data.search_input_font_size.md:data.search_input_font_size;
        let search_input_font_size_sm = _.isObject(data.search_input_font_size)&&data.search_input_font_size?data.search_input_font_size.sm:data.search_input_font_size;
        let search_input_font_size_xs = _.isObject(data.search_input_font_size)&&data.search_input_font_size?data.search_input_font_size.xs:data.search_input_font_size;
//        字体颜色
        let search_input_color=(!_.isEmpty(data.search_input_color) && data.search_input_color)?data.search_input_color:"#999996";
//        背景颜色
        let search_input_background=(!_.isEmpty(data.search_input_background) && data.search_input_background)?data.search_input_background:"#fff";
//      搜索图片大小
        let search_button_size=_.isObject(data.search_button_size)&&data.search_button_size?data.search_button_size.md:data.search_button_size;
        let search_button_size_sm = _.isObject(data.search_button_size)&&data.search_button_size?data.search_button_size.sm:data.search_button_size;
        let search_button_size_xs = _.isObject(data.search_button_size)&&data.search_button_size?data.search_button_size.xs:data.search_button_size;

//        分割线
//          样式
        let split_style=(!_.isEmpty(data.split_style) && data.split_style)?data.split_style:"solid";
//        颜色
        let split_color=(!_.isEmpty(data.split_color) && data.split_color)?data.split_color:"#E7E7E7";
        
//        表格
//        对齐方式
        let table_text_align=(!_.isEmpty(data.table_text_align) && data.table_text_align)?data.table_text_align:"center";
//        内边距
        let table_padding = _.isObject(data.table_padding)&&data.table_padding?window.getMarginPadding(data.table_padding.md, "padding"):window.getMarginPadding(data.table_padding, "padding");
        let table_padding_sm = _.isObject(data.table_padding)&&data.table_padding?window.getMarginPadding(data.table_padding.sm, "padding"):window.getMarginPadding(data.table_padding, "padding");
        let table_padding_xs = _.isObject(data.table_padding)&&data.table_padding?window.getMarginPadding(data.table_padding.xs, "padding"):window.getMarginPadding(data.table_padding, "padding");
//          表头行高
        let table_head_line_height="";
        let table_head_line_height_sm="";
        let table_head_line_height_xs="";
        if(_.isObject(data.table_head_line_height)&&data.table_head_line_height){
            table_head_line_height =data.table_head_line_height.md;
            table_head_line_height_sm = data.table_head_line_height.sm;
            table_head_line_height_xs = data.table_head_line_height.xs;
        }else{
            table_head_line_height =data.table_head_line_height;
            table_head_line_height_sm = data.table_head_line_height;
            table_head_line_height_xs = data.table_head_line_height;
        }
//        表头字体
        let table_head_font_size="";
        let table_head_font_size_sm="";
        let table_head_font_size_xs="";
        if(_.isObject(data.table_head_font_size)&&data.table_head_font_size){
            table_head_font_size =data.table_head_font_size.md;
            table_head_font_size_sm = data.table_head_font_size.sm;
            table_head_font_size_xs = data.table_head_font_size.xs;
        }else{
            table_head_font_size =data.table_head_font_size;
            table_head_font_size_sm = data.table_head_font_size;
            table_head_font_size_xs = data.table_head_font_size;
        }
        //        字体颜色
        let table_head_font_color=(!_.isEmpty(data.table_head_font_color) && data.table_head_font_color)?data.table_head_font_color:"";

        //          表格行高
        let table_body_line_height="";
        let table_body_line_height_sm="";
        let table_body_line_height_xs="";
        if(_.isObject(data.table_body_line_height)&&data.table_body_line_height){
            table_body_line_height =data.table_body_line_height.md;
            table_body_line_height_sm = data.table_body_line_height.sm;
            table_body_line_height_xs = data.table_body_line_height.xs;
        }else{
            table_body_line_height =data.table_head_line_height;
            table_body_line_height_sm = data.table_body_line_height;
            table_body_line_height_xs = data.table_body_line_height;
        }
        //        表格字体
        let table_body_font_size="";
        let table_body_font_size_sm="";
        let table_body_font_size_xs="";
        if(_.isObject(data.table_body_font_size)&&data.table_body_font_size){
            table_body_font_size =data.table_body_font_size.md;
            table_body_font_size_sm = data.table_body_font_size.sm;
            table_body_font_size_xs = data.table_body_font_size.xs;
        }else{
            table_body_font_size =data.table_body_font_size;
            table_body_font_size_sm = data.table_body_font_size;
            table_body_font_size_xs = data.table_body_font_size;
        }
//        字体颜色
        let table_body_font_color=(!_.isEmpty(data.table_body_font_color) && data.table_body_font_color)?data.table_body_font_color:"";


//       占位字体
        let no_data_line_height="";
        let no_data_line_height_sm="";
        let no_data_line_height_xs="";
        if(_.isObject(data.no_data_line_height)&&data.no_data_line_height){
            no_data_line_height =data.no_data_line_height.md;
            no_data_line_height_sm = data.no_data_line_height.sm;
            no_data_line_height_xs = data.no_data_line_height.xs;
        }else{
            no_data_line_height =data.no_data_line_height;
            no_data_line_height_sm = data.no_data_line_height;
            no_data_line_height_xs = data.no_data_line_height;
        }
//        字体
        let no_data_font_size="";
        let no_data_font_size_sm="";
        let no_data_font_size_xs="";
        if(_.isObject(data.no_data_font_size)&&data.no_data_font_size){
            no_data_font_size =data.no_data_font_size.md;
            no_data_font_size_sm = data.no_data_font_size.sm;
            no_data_font_size_xs = data.no_data_font_size.xs;
        }else{
            no_data_font_size =data.no_data_font_size;
            no_data_font_size_sm = data.no_data_font_size;
            no_data_font_size_xs = data.no_data_font_size;
        }
        //        颜色
        let no_data_font_color=(!_.isEmpty(data.no_data_font_color) && data.no_data_font_color)?data.no_data_font_color:"";



        // 指定文章详情页ID
        var detail_page_id = (!_.isEmpty(data.detail_page_id) && data.detail_page_id) ? data.detail_page_id : "";
        #>

        <style>
            {{addonId}} .fl{float:left;}
            {{addonId}} .fr{float:right}
            {{addonId}} .cl{clear:both;}

            {{addonId}} .rak_cont{width:{{width}};background:{{content_color}};}
            {{addonId}} .rak_top{width:100%;padding:5px 2%;{{top_bgColor}};line-height:{{top_title_height}}px;font-size:{{top_title_font_size}}px;color:{{top_titColor}};font-weight:bold}
            {{addonId}} .rak_top_left{width:{{filter_year_width}}px;height:{{filter_year_height}}px;}
            {{addonId}} .rak_top_right{width:{{search_input_width||600}}px;}
            
            {{addonId}} .rak_ztys{
                {{filter_padding}};border-bottom:1px {{split_style}} {{split_color}};
            }
            {{addonId}} .rak_ztysth th{
                line-height:{{table_head_line_height||35}}px;
                font-size: {{table_head_font_size||16}}px;
                color:{{table_head_font_color}};
            }
            {{addonId}} .rak_sealb{
                {{table_padding}};
            }
            {{addonId}} .rak_sealb table{width:100%;}
            {{addonId}} .rak_sealb table th,td{display:inline-block;text-align:{{table_text_align}};}
            {{addonId}} .rak_sealb table,td{
                line-height:{{table_body_line_height||35}}px;
                font-size:{{table_body_font_size||16}}px;
                color: {{table_body_font_color}};
            }
            {{addonId}} #seach_key{border:none;}
            {{addonId}} .rak_seach{
                border:1px solid #ccc;
                width:100%;
                border-radius:{{search_input_radius}}px;
                line-height:{{search_input_height}}px;
                padding:0px 5%;
                background:{{search_input_background}};
            }
            {{addonId}} .rak_seach img{
                width:{{search_button_size}}px;
                height:auto;
                vertical-align:middle;
            }

            {{addonId}} #selt{
                height:100%;
                color:{{filter_year_color}};
                font-size:{{filter_year_font_size}}px;
                line-height:{{filter_year_height}}px;
                background:{{filter_year_background}};
                border-color:{{filter_year_border_color}};
            }
            {{addonId}} .rak_seach input{
                text-align: {{search_text_align}};
                font-size:{{search_input_font_size}}px;
                color:{{search_input_color}};
            }
            
            {{addonId}} .no-data{
                text-align:center;
                line-height:{{no_data_line_height}}px;
                font-size:{{no_data_font_size}}px;
                color:{{no_data_font_color}};
            }
            
            <# if(lxstyle=="listy"){
                // 列表页css
            #>
                {{addonId}} .rak_ztys{padding:10px 16px 15px 16px}

            <# } #>

            @media (min-width:768px) and (max-width:991px){
                {{addonId}} .rak_cont{width:{{width_sm}};}
                {{addonId}} .rak_top{
                    line-height:{{top_title_height_sm}}px;
                    font-size:{{top_title_font_size_sm}}px;
                }
                {{addonId}} .rak_ztys{
                    {{filter_padding_sm}};
                }
                {{addonId}} .rak_top_left{width:{{filter_year_width_sm}}px;height:{{filter_year_height_sm}}px;}
                {{addonId}} #selt{font-size:{{filter_year_font_size_sm}}px;line-height:{{filter_year_height_sm}}px;}
                {{addonId}} .rak_top_right{width:{{search_input_width_sm||400}}px;}
                {{addonId}} .rak_seach{line-height:{{search_input_height_sm}}px;}
                {{addonId}} .rak_seach input{
                    font-size:{{search_input_font_size_sm}}px;
                 }
                {{addonId}} .rak_seach img{
                    width:{{search_button_size_sm}}px;
                }
                {{addonId}} .rak_sealb{
                    {{table_padding_sm}};
                }
                {{addonId}} .rak_ztysth th{
                    line-height:{{table_head_line_height_sm}}px;
                    font-size: {{table_head_font_size_sm}}px;
                }
                {{addonId}} .rak_sealb table,td{
                    line-height:{{table_body_line_height_sm}}px;
                    font-size:{{table_body_font_size_sm}}px;
                }
                {{addonId}} .no-data{
                    line-height:{{no_data_line_height_sm}}px;
                    font-size:{{no_data_font_size_sm}}px;
                }
            }
            @media (max-width:767px){
                {{addonId}} .rak_cont{width:{{width_xs}};}
                {{addonId}} .rak_top{
                    line-height:{{top_title_height_xs}}px;
                    font-size:{{top_title_font_size_xs}}px;
                }
                {{addonId}} .rak_ztys{
                    {{filter_padding_xs}};
                }
                {{addonId}} .rak_top_left{width:{{filter_year_width_xs}}px;height:{{filter_year_height_xs}}px;}
                {{addonId}} #selt{font-size:{{filter_year_font_size_xs}}px;line-height:{{filter_year_height_xs}}px;}
                {{addonId}} .rak_top_right{width:{{search_input_width_xs||200}}px;}
                {{addonId}} .rak_seach{line-height:{{search_input_height_xs}}px;}
                {{addonId}} .rak_seach input{
                    font-size:{{search_input_font_size_xs}}px;
                }
                {{addonId}} .rak_seach img{
                    width:{{search_button_size_xs}}px;
                }
                {{addonId}} .rak_sealb{
                    {{table_padding_xs}};
                }
                {{addonId}} .rak_ztysth th{
                    line-height:{{table_head_line_height_xs}}px;
                    font-size: {{table_head_font_size_xs}}px;
                }
                {{addonId}} .rak_sealb table,td{
                    line-height:{{table_body_line_height_xs}}px;
                    font-size:{{table_body_font_size_xs}}px;
                }
                {{addonId}} .no-data{
                    line-height:{{no_data_line_height_xs}}px;
                    font-size:{{no_data_font_size_xs}}px;
                }
            }
        </style>

        <# if(lxstyle=="listy"){ 
        // 判断显示页面为首页还是列表页
        // 列表
        #>
            <div class="rak_nr">
                <div class="rak_ztys">
                    <div class="rak_top_left fl">
                        <select name="" id="selt">
                            <option value="2019">2019</option>
                        </select>
                    </div>
                    <div class="rak_top_right fr">
                        <div class="rak_seach">
                            <input type="text" style="width:85%;display:inline" name="rak_sea" id="seach_key" placeholder="请输入关键词" >
                            <img style="max-width:10%;display:inline;" src=\'{{seach_image}}\' alt="">
                            <div class="cl"></div>
                        </div>
                    </div>
                    <div class="cl"></div>
                </div>
                <div class="rak_sealb">
               
                    <# if(data.has_data!=="no"){ #>
                        <table>
                            <tr class="rak_ztysth">
                                <th width="8%">排名</th>
                                <th width="9%">培训次数</th>
                                <th width="10%">培训人数</th>
                                <th width="13%">考评中心名称</th>
                                <th width="8%">联系人</th>
                                <th width="10%">联系电话</th>
                                <th width="8%">星级</th>
                                <th width="14%">详细地址</th>
                                <th width="15%">详情</th>
                            </tr>
                            <tr>
                                <th width="8%">1</th>
                                <th width="9%">15</th>
                                <th width="10%">100</th>
                                <th width="13%">太原分中心</th>
                                <th width="8%">李老师</th>
                                <th width="10%">13866666666</th>
                                <th width="8%">5星</th>
                                <th width="14%">太原市小店区</th>
                                <th width="15%">详细内容</th>
                            </tr>
                            <tr>
                                <th width="8%">2</th>
                                <th width="9%">12</th>
                                <th width="10%">90</th>
                                <th width="13%">临汾分中心</th>
                                <th width="8%">王老师</th>
                                <th width="10%">13888888888</th>
                                <th width="8%">5星</th>
                                <th width="14%">太原市小店区</th>
                                <th width="15%">详细内容</th>
                            </tr>
                        </table>
                    <# }else{ #>
                        <p class="no-data">{{data.no_data||"暂无数据"}}</p>
                    <# } #>
                </div>
            </div>

        <# }else{ 
            // 首页
        #>
            <div class="rak_cont">
                <div class="rak_top">
                    <div class="rak_tit fl">
                        {{top_tit}}
                    </div>
                    <div class="rak_gd fr">
                        更多>>
                    </div>
                    <div class="cl"></div>
                </div>
                <div class="rak_nr">
                    <div class="rak_ztys">
                        <div class="rak_top_left fl">
                            <select name="" id="selt">
                                <option value="2019">2019</option>
                            </select>
                        </div>
                        <div class="rak_top_right fr">
                            <div class="rak_seach">
                                <input type="text" style="width:85%;display:inline" name="rak_sea" id="seach_key" placeholder="请输入关键词" >
                                <img style="max-width:10%;display:inline;" src=\'{{seach_image}}\' alt="">
                                <div class="cl"></div>
                            </div>
                        </div>
                        <div class="cl"></div>
                    </div>
                    <div class="rak_sealb">
                   
                        <# if(data.has_data!=="no"){ #>
                            <table>
                                <tr class="rak_ztysth">
                                    <th width="20%" class="rak_pm">排名</th>
                                    <th width="35%" class="rak_pmcs">培训次数</th>
                                    <th width="43%" class="rak_pmzx">考评中心名称</th>
                                </tr>
                                <tr>
                                    <td width="20%">1</td>
                                    <td width="35%">15</td>
                                    <td width="43%">太原分中心</td>
                                </tr>
                                <tr>
                                    <td width="20%">2</td>
                                    <td width="35%">12</td>
                                    <td width="43%">吕梁分中心</td>
                                </tr>
                            </table>
                        <# }else{ #>
                            <p class="no-data">{{data.no_data||"暂无数据"}}</p>
                        <# } #>
                        
                    </div>
                </div>
            </div>
            
        <# } #>
        
        ';

        return $output;
    }


    //数组多层级 递归
    public function subTree($data, $pid = 1, $deep = 0)
    {   //用来存放数据
        $arr = [];
        //遍历数据库中的字段
        foreach ($data as $val)
        {
            //判断字段pid相等时放行
            if ($pid == $val['parent_id'])
            {
                //不同的层级
                $val['deep'] = $deep;
                //递归，调用自身方法，判断本条数据的id是否为本条数据的pid，如果不是就往下执行代码
                $val['sub'] = $this->subTree($data, $val['tag_id'], $deep + 1);
                //如果遇到pid==本条id时，将其存入数组
                $arr[] = $val;
            }
        }

        //返回数组
        return $arr;
    }


}
